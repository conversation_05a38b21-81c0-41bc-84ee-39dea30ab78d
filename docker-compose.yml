version: "3.9"
services:
    laravel:
        container_name: laravel
        image: ${LARAVEL_IMAGE}
        restart: always
        logging:
            driver: awslogs
            options:
                awslogs-region: ${AWS_DEFAULT_REGION}
                awslogs-group: lottery
                awslogs-stream: laravel-application
        environment:
            PHP_OPCACHE_ENABLE: 1
            PRODUCTION: 1
            PORT: 12169
            APP_ENV: prod
            APP_KEY: ${APP_KEY}
            APP_URL: ${APP_URL}
            DB_HOST: ${DB_HOST}
            DB_PORT: ${DB_PORT}
            SESSION_DRIVER: ${SESSION_DRIVER}
            DB_DATABASE: ${DB_DATABASE}
            DB_USERNAME: ${DB_USERNAME}
            DB_PASSWORD: ${DB_PASSWORD}
            REDIS_HOST: ${REDIS_HOST}
            REDIS_PASSWORD: ${REDIS_PASSWORD}
            REDIS_PORT:  ${REDIS_PORT}
            REDIS_CLIENT: ${REDIS_CLIENT}
            QUEUE_CONNECTION: redis
            LOG_CHANNEL: stderr
            LOCAL_BOT_URL_FIRED: ${LOCAL_BOT_URL_FIRED}
            CACHE_DRIVER: ${CACHE_DRIVER}
        ports:
            - 80:80
        networks:
            - my-network
    bot-node:
        container_name: bot-node
        image: ${BOT_IMAGE}
        restart: always
        logging:
            driver: awslogs
            options:
                awslogs-region: ${AWS_DEFAULT_REGION}
                awslogs-group: lottery
                awslogs-stream: node-application
        environment:
            - PM2_PUBLIC_KEY=${PM2_PUBLIC_KEY}
            - PM2_SECRET_KEY=${PM2_SECRET_KEY}
            - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
            - PORT=${PORT}
            - WEBHOOK_URL=${WEBHOOK_URL}
            - API_TICKET_SERVICE=${API_TICKET_SERVICE}
            - REDIS_HOST_BOT=${REDIS_HOST_BOT}
            - NODE_DB_USER=${NODE_DB_USER}
            - NODE_DB_PASSWORD=${NODE_DB_PASSWORD}
            - NODE_DB_NAME=${NODE_DB_NAME}
            - NODE_DB_HOST=${NODE_DB_HOST}
            - NODE_DB_DRIVER=${NODE_DB_DRIVER}
            - NODE_DB_LOGGING=${NODE_DB_LOGGING}
            - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
            - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
            - TELEGRAM_BOT_TOKEN_FIRED=${TELEGRAM_BOT_TOKEN_FIRED}
            - LOCAL_BOT_URL_FIRED=${LOCAL_BOT_URL_FIRED}
            - LOCAL_BOT_FIRED_ID=${LOCAL_BOT_FIRED_ID}
            - ENABLE_SCHEDULER=${ENABLE_SCHEDULER}
        ports:
            - ${PORT}:3000
        networks:
            - my-network
    bot:
        container_name: bot
        image: ${LOCAL_BOT_IMAGE}
        restart: always
        logging:
            driver: awslogs
            options:
                awslogs-region: ${AWS_DEFAULT_REGION}
                awslogs-group: lottery
                awslogs-stream: bot-telegram
        ports:
            - 8081:8081
        environment:
            - TELEGRAM_API_ID=${TELEGRAM_API_ID}
            - TELEGRAM_API_HASH=${TELEGRAM_API_HASH}
        command: --log /dev/stdout --verbosity=9
        networks:
            - my-network
        expose:
            - 8081

networks:
    my-network:
        driver: bridge
