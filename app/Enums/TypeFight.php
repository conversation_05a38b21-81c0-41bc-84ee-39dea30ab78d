<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class TypeFight extends Enum
{
    const DAU =   0;
    const DUOI =   1;
    const DAU_DUOI = 2;
    const BAO_LO = 3;
    const XIU_CHU = 4;
    const XIU_CHU_DAU = 5;
    const XIU_CHU_DUOI = 6;
    const DA = 7;
    const DA_XIEN = 8;
    const DANH_BAY_LO = 9;
    const BAY_LO_DAO = 10;
    const TAM_LO = 11;
    const TAM_LO_DAO = 12;
    const XIU_CHU_DAO_DAU = 13;
    const XIU_CHU_DAO_DUOI = 14;
    const XIU_CHU_DAO = 15;
    const BAO_LO_DAO = 16;
    const CHAN_DA_THANG = 27;
    const CHAN_DA_XIEN = 28;


}
