<?php

namespace App\Telegram\Commands;


use App\Enums\CallBackTelegramBotEnum;
use App\Enums\TypeMessageBotEnum;
use App\Models\TokenBot;
use App\Traits\TraitsUtil;
use Telegram\Bot\Commands\Command;
use Telegram\Bot\Keyboard\Keyboard;

class KeToanCommand extends Command
{
    use TraitsUtil;

    protected $name = 'ketoan';
    protected $description = 'Sao kê kế toán';


    public function handle()
    {

        $objectMessage = $this->getUpdate()->getMessage();
        $message_id = $this->getUpdate()->getChat()->getId();
        if ($this->checkIsGroup($objectMessage)) {
            $this->removeConversationOfAccount($message_id, TypeMessageBotEnum::GROUP);
            $checkToken = TokenBot::query()
                ->where('provider', 'TELEGRAM')
                ->where('provider_message_id', $message_id)
                ->where('type', TypeMessageBotEnum::GROUP)
                ->first();
        } else {
            $account_id = $objectMessage->getFrom()->getId();
            $this->removeConversationOfAccount($account_id, TypeMessageBotEnum::PRIVATE);
            $checkToken = TokenBot::query()
                ->where('provider', 'TELEGRAM')
                ->where('provider_message_id', $message_id)
                ->where('type', TypeMessageBotEnum::PRIVATE)
                ->first();
        }


        if (!$checkToken) {
            $this->replyWithMessage([
                'text' => 'Tài khoản hoặc mật khẩu không đúng',
            ]);
            return;
        }


        if ($this->checkTokenIsAvaiable($checkToken)) {
            $this->replyWithMessage([
                'text' => 'Tài khoản hiện không thể sử dụng bot vui lòng liên lạc Admin.',

            ]);
            return;
        }
        $keyboard = Keyboard::make()
            ->inline()
            ->row(
                Keyboard::inlineButton(['text' => 'Hôm qua', 'callback_data' => CallBackTelegramBotEnum::KT_HOM_QUA]),
                Keyboard::inlineButton(['text' => 'Hôm nay', 'callback_data' => CallBackTelegramBotEnum::KT_HOM_NAY]),

            )->row(
                Keyboard::inlineButton(['text' => 'Tuần này', 'callback_data' => CallBackTelegramBotEnum::KT_TUAN_NAY]),
                Keyboard::inlineButton(['text' => 'Tuần trước', 'callback_data' => CallBackTelegramBotEnum::KT_TUAN_TRUOC])
            );

        $this->replyWithMessage([
            'text' => 'Vui lòng chọn ngày để xem sao kê: ',
            'reply_markup' => $keyboard
        ]);

    }
}
