<?php

namespace App\Telegram\Commands;


use App\Enums\TypeMessageBotEnum;
use App\Enums\TypeSideEnum;
use App\Models\Report;
use App\Models\Ticket;
use App\Models\TokenBot;
use App\Services\CrawlResultService;
use App\Services\GetPrizeService;
use App\Services\TicketService;
use App\Traits\TraitsUtil;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Telegram\Bot\Commands\Command;

class XoaTinCommand extends Command
{
    use TraitsUtil;

    protected $name = 'xoa';
    protected $description = 'X<PERSON>a tin theo miền';

    protected TicketService $ticketService;
    protected CrawlResultService $crawlerService;
    protected GetPrizeService $getPrizeService;

    public function __construct(TicketService $ticketService, CrawlResultService $crawlerService, GetPrizeService $getPrizeService)
    {
        $this->ticketService = $ticketService;
        $this->crawlerService = $crawlerService;
        $this->getPrizeService = $getPrizeService;

    }

    /**
     * @throws \Exception
     */
    public
    function handle()
    {

        $objectMessage = $this->getUpdate()->getMessage();
        $message_id = $this->getUpdate()->getChat()->getId();
        if ($this->checkIsGroup($objectMessage)) {
            $this->removeConversationOfAccount($message_id, TypeMessageBotEnum::GROUP);

            $checkToken = TokenBot::query()
                ->where('provider', 'TELEGRAM')
                ->where('provider_message_id', $message_id)
                ->where('type', TypeMessageBotEnum::GROUP)
                ->first();
        } else {
            $account_id = $objectMessage->getFrom()->getId();
            $this->removeConversationOfAccount($account_id, TypeMessageBotEnum::PRIVATE);
            $checkToken = TokenBot::query()
                ->where('provider', 'TELEGRAM')
                ->where('provider_message_id', $message_id)
                ->where('type', TypeMessageBotEnum::PRIVATE)
                ->first();
        }

        if (!$checkToken) {
            $this->replyWithMessage([
                'text' => 'Tài khoản hoặc mật khẩu không đúng',
            ]);
            return;
        }
        if ($this->checkTokenIsAvaiable($checkToken)) {

            $this->replyWithMessage([
                'text' => 'Tài khoản hiện không thể sử dụng bot vui lòng liên lạc Admin.',

            ]);
            return;
        }

        $text = explode(" ", trim($this->getUpdate()->getMessage()['text']));
        if (isset($text[1])) {
            if (preg_match("/MN\s*([\d+,]+)|mn\s*([\d+,]+)|miennam\s*([\d+,]+)/", strtolower($text[1]), $matches)) {
                $number = $matches[count($matches) - 1];
                $textMien = 'MN';
                $dai = TypeSideEnum::MN;
            } else if (preg_match("/MB\s*([\d+,]+)|mb\s*([\d+,]+)|mienbac\s*([\d+,]+)/", strtolower($text[1]), $matches)) {
                $number = $matches[count($matches) - 1];
                $textMien = 'MB';
                $dai = TypeSideEnum::MB;

            } else if (preg_match("/MT\s*([\d+,]+)|mt\s*([\d+,]+)|mientrung\s*([\d+,]+)/", strtolower($text[1]), $matches)) {
                $textMien = 'MT';
                $number = $matches[count($matches) - 1];
                $dai = TypeSideEnum::MT;

            } else {
                $this->replyWithMessage([
                    'text' => 'Vui lòng gõ đúng cú pháp, vd: /xoa MN1',
                ]);
                return;
            }
            $numbers = [];
            if ($number) {
                $numbers = explode(",", $number);

            }
            $isSent = false;
            $count = 0;
            foreach ($numbers as $element) {
                if (is_numeric(trim($element))) {
                    Log::info("First " . $element);
                    $id = (int)intval(trim($element)) - 1 - $count;
                    Log::info("id " . $id);

                    $ticket = Ticket::query()
                        ->where('date_check', date('Y-m-d'))
                        ->where('customer_id', $checkToken->customer_id)
                        ->where('region_id', $dai)
                        ->orderBy('created_at', 'ASC')
                        ->get()
                        ->values()
                        ->get($id);
                    try {
                        if ($ticket && $this->checkIsRemove($ticket) && $this->checkTimeRemoveMessageBot($dai, $ticket)) {
                            $this->replyWithMessage([
                                'text' => "Xóa thất bại vì vượt quá thời gian cho phép hoặc tính năng đã tắt!❌",
                            ]);
                            return;
                        }

                        if ($ticket) {
                            $report = Report::query()
                                ->where('region_id', $ticket->region_id)
                                ->where('customer_id', $ticket->customer_id)
                                ->where('date_check', $ticket->date_check)
                                ->get()
                                ->first();

                            $report->hai_so = $report->hai_so - $ticket->hai_so;
                            $report->ba_so = $report->ba_so - $ticket->ba_so;
                            $report->bon_so = $report->bon_so - $ticket->bon_so;
                            $report->dau_duoi = $report->dau_duoi - $ticket->dau_duoi;
                            $report->xiu_chu = $report->xiu_chu - $ticket->xiu_chu;
                            $report->da_thang = $report->da_thang - $ticket->da_thang;
                            $report->da_xien = $report->da_xien - $ticket->da_xien;
                            $report->tong_tien_2so = $report->tong_tien_2so - $ticket->tong_tien_2so;
                            $report->tong_tien_2so_lai = $report->tong_tien_2so_lai - $ticket->tong_tien_2so_lai;
                            $report->tong_tien_3so = $report->tong_tien_3so - $ticket->tong_tien_3so;
                            $report->tong_tien_3so_lai = $report->tong_tien_3so_lai - $ticket->tong_tien_3so_lai;
                            $report->tong_tien_4so = $report->tong_tien_4so - $ticket->tong_tien_4so;
                            $report->tong_tien_4so_lai = $report->tong_tien_4so_lai - $ticket->tong_tien_4so_lai;
                            $report->total_danh_lai = $report->total_danh_lai - $ticket->total_danh_lai;
                            $report->total_danh = $report->total_danh - $ticket->total_danh;


                            $report->hai_so_trung = $report->hai_so_trung - $ticket->hai_so_trung;
                            $report->ba_so_trung = $report->ba_so_trung - $ticket->ba_so_trung;
                            $report->bon_so_trung = $report->bon_so_trung - $ticket->bon_so_trung;
                            $report->dau_duoi_trung = $report->dau_duoi_trung - $ticket->dau_duoi_trung;
                            $report->xiu_chu_trung = $report->xiu_chu_trung - $ticket->xiu_chu_trung;
                            $report->da_thang_trung = $report->da_thang_trung - $ticket->da_thang_trung;
                            $report->da_xien_trung = $report->da_xien_trung - $ticket->da_xien_trung;
                            $report->tong_tien_2so_trung = $report->tong_tien_2so_trung - $ticket->tong_tien_2so_trung;
                            $report->tong_tien_2so_trung_lai = $report->tong_tien_2so_trung_lai - $ticket->tong_tien_2so_trung_lai;
                            $report->tong_tien_3so_trung = $report->tong_tien_3so_trung - $ticket->tong_tien_3so_trung;
                            $report->tong_tien_3so_trung_lai = $report->tong_tien_3so_trung_lai - $ticket->tong_tien_3so_trung_lai;
                            $report->tong_tien_4so_trung = $report->tong_tien_4so_trung - $ticket->tong_tien_4so_trung;
                            $report->tong_tien_4so_trung_lai = $report->tong_tien_4so_trung_lai - $ticket->tong_tien_4so_trung_lai;
                            $report->total_trung = $report->total_trung - $ticket->total_trung;
                            $report->total_trung_lai = $report->total_trung_lai - $ticket->total_trung_lai;
                            $report->total = $report->total - $ticket->total;
                            $report->total_lai = $report->total_lai - $ticket->total_lai;


                            $report->save();

                            $ticket->delete();
                            $id = $id + 1 +$count;

                            $this->replyWithMessage([
                                'text' => "Xóa thành công tin có số Stt: {$id} của đài {$textMien}✅",
                            ]);
                            $isSent = true;


                        } else {
                            $id = $id + 1;
                            $this->replyWithMessage([
                                'text' => "Không tìm thấy số thứ tự {$id} của đài {$textMien}",
                            ]);

                        }
                    } catch (\Exception $exception) {
                        $this->replyWithMessage([
                            'text' => $exception->getMessage(),
                        ]);
                        return;
                    }
                    $count++;
                } else {
                    Log::info("Không phải số khi xoa tn " . $element);
                }
            }
            if ($isSent) {
                if ($dai == 1) {
                    $this->viewDsMessage($dai, $checkToken->customer_id, 'Nam', true);
                    return;
                } elseif ($dai == 2) {
                    $this->viewDsMessage($dai, $checkToken->customer_id, 'Bắc', true);
                    return;
                } elseif ($dai == 3) {
                    $this->viewDsMessage($dai, $checkToken->customer_id, 'Trung', true);
                    return;
                }


            }
        }

        $this->replyWithMessage([
            'text' => 'Vui lòng gõ đúng cú pháp, vd: /xoa MN1',
        ]);

    }

    private function viewDsMessage($region, $customer_id, $side, $isToday)
    {
        $date = $isToday ? date('Y-m-d') : date('Y-m-d', strtotime('-1 day', time()));
        $tickets = DB::table('tickets')
            ->where('customer_id', $customer_id)
            ->where('date_check', $date)
            ->where('region_id', $region)
            ->get();

        if ($tickets->count() == 0) {
            $this->replyWithMessage([
                'text' => "Miền {$side} hiện chưa có tin nhắn nào!",
            ]);
            return;
        }
        $strRes = '';
        $i = 1;
        $strMien = 'MN';
        if ($region == TypeSideEnum::MB) {
            $strMien = 'MB';
        }
        if ($region == TypeSideEnum::MT) {
            $strMien = 'MT';
        }
        $arr_message = [];
        foreach ($tickets as $ticket) {
            $message = str_replace("\r\n", "\n", $ticket->message);
            $message = str_replace("Số trúng:", "<b>Số trúng:</b>", $message);
            $message = str_replace("2 so:", "<b>2 so:</b>", $message);
            $message = str_replace("3 so:", "<b>3 so:</b>", $message);
            $message = str_replace("4 so:", "<b>4 so:</b>", $message);
            $tien_trung = $ticket->tien_trung ?? '';
            $str = "<b>Stt: {$i}, {$strMien}</b>" .
                "\n{$message}" .
                "\n<b>Tiền trúng: {$tien_trung}</b>" .
                "\n--------------------------------------\n";

            if (strlen($strRes . $str) >= 2500) {
                $arr_message[] = $strRes;
                if (strlen($str) >= 2500) {
                    $this->customSubstring($str, 2500, $arr_message);
                } else {
                    $strRes = $str;
                }

            } else {
                $strRes .= $str;
            }
            $i++;
        }
        if (!empty($strRes)) {
            $arr_message[] = $strRes;
        }
        foreach ($arr_message as $message) {
            $this->replyWithMessage([
                'text' => $message,
                'parse_mode' => 'HTML',
            ]);
        }

//        $this->show($customer_id, $date, $chatId, $region);
    }

    private function customSubstring($str, $maxLength, &$result)
    {
        // Determine the number of segments needed.
        $numSegments = ceil(strlen($str) / $maxLength);

        // Split the string into segments.
        for ($i = 0; $i < $numSegments; $i++) {
            $result[] = substr($str, $i * $maxLength, $maxLength);
        }

        return $result;
    }
}
