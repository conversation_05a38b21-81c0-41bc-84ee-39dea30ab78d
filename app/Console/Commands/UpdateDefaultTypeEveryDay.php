<?php

namespace App\Console\Commands;

use App\Models\ConversationBot;
use Illuminate\Console\Command;

class UpdateDefaultTypeEveryDay extends Command
{


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'updateDefaultType:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ConversationBot::query()->update(['type' => 1]);

    }
}
