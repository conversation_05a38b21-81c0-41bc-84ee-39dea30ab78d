<?php

namespace App\Http\Controllers;

use App\Enums\AreaEnum;
use App\Enums\TypeUser;
use App\Models\Accountant;
use App\Models\Customer;
use App\Models\Machine;
use App\Models\Ration;
use App\Models\Report;
use App\Models\ReportDetail;
use App\Models\Shareholder;
use App\Models\Statistics;
use App\Models\Ticket;
use App\Models\User;
use App\Services\ReportService;
use App\Traits\UserUtil;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

class ReportController extends Controller
{
    use UserUtil;

    protected ReportService $reportService;

    public function __construct(ReportService $reportService)
    {

        $this->reportService = $reportService;

    }

    public function index(Request $request)
    {

        if (auth()->guard('machine')->check()) {
            return view('report.index');

        }

        return view('report.index_admin');
    }

    public function reportDetail(Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        return view('reportDetail.index', [
            'fromDate' => $date_start,
            'toDate' => $date_end,
        ]);
    }

    public function report_detail_api(Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        $reports = Report::query()->select(
            'customer_id',
            DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam'),
            DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac'),
            DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung'),
            DB::raw('COALESCE(sum(total_trung_lai)) - COALESCE(sum(total_danh_lai)) as sum_all'),
        )
            ->whereDate('date_check', '>=', $date_start)
            ->whereDate('date_check', '<=', $date_end)
            ->groupBy('customer_id')
            ->with(['customer'])
            ->get()
            ->map(function ($user) {
                $user['username'] = $user['customer']['username'];
                unset($user->customer);
                return $user;
            });

        return $reports;
    }

    public function reportDetailShow($customer_id, Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        $region = $request->region;
        $customer = Customer::find($customer_id);

        if ($region == 1)
            $region_name = "Miền Nam";
        elseif ($region == 2)
            $region_name = "Miền Bắc";
        elseif ($region == 3)
            $region_name = "Miền Trung";
        else $region_name = "3 miền";


        return view('reportDetail.detail', [
            'fromDate' => $date_start,
            'toDate' => $date_end,
            'region' => $region,
            'region_name' => $region_name,
            'name' => $customer ? $customer['username'] : "tất cả user",
        ]);
    }

    public function report_detail_api_show($customer_id, Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        $region = $request->region;
        if ($customer_id != 0) {
            $report_details = ReportDetail::query()
                ->select(
                    'customer_id',
                    DB::raw('COALESCE(hai_so, 0) as hai_so_danh'),
                    DB::raw('COALESCE(hai_so_lai, 0) as hai_so_lai'),
                    DB::raw('COALESCE(ba_so, 0) as ba_so_danh'),
                    DB::raw('COALESCE(ba_so_lai, 0) as ba_so_lai'),
                    DB::raw('COALESCE(bon_so, 0) as bon_so_danh'),
                    DB::raw('COALESCE(bon_so_lai, 0) as bon_so_lai'),
                    DB::raw('COALESCE(dau_duoi, 0) as dau_duoi_danh'),
                    DB::raw('COALESCE(dau_duoi_lai, 0) as dau_duoi_lai'),
                    DB::raw('COALESCE(bao_lo_2_so, 0) as bao_lo_danh_2_so'),
                    DB::raw('COALESCE(bao_lo_3_so, 0) as bao_lo_danh_3_so'),
                    DB::raw('COALESCE(bao_lo_4_so, 0) as bao_lo_danh_4_so'),
                    DB::raw('COALESCE(bao_lo_2_so_lai, 0) as bao_lo_2_so_lai'),
                    DB::raw('COALESCE(bao_lo_3_so_lai, 0) as bao_lo_3_so_lai'),
                    DB::raw('COALESCE(bao_lo_4_so_lai, 0) as bao_lo_4_so_lai'),
                    DB::raw('COALESCE(xiu_chu, 0) as xiu_chu_danh'),
                    DB::raw('COALESCE(xiu_chu_lai, 0) as xiu_chu_lai'),
                    DB::raw('COALESCE(da_thang, 0) as da_thang_danh'),
                    DB::raw('COALESCE(da_thang_lai, 0) as da_thang_lai'),
                    DB::raw('COALESCE(da_xien, 0) as da_xien_danh'),
                    DB::raw('COALESCE(da_xien_lai, 0) as da_xien_lai'),
                    DB::raw('COALESCE(total_danh, 0) as tong_danh'),
                    DB::raw('COALESCE(total_lai, 0) as tong_lai'),
                    'so_danh',
                    'so_trung',
                    'date_check',
                    'region_id',
                    'area_id'
                )
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->where('region_id', '=', $region)
                ->where('customer_id', '=', (int)$customer_id)
                ->get();
        } else {
            $report_details = ReportDetail::query()
                ->select(
                    'customer_id',
                    DB::raw('COALESCE(hai_so, 0) as hai_so_danh'),
                    DB::raw('COALESCE(hai_so_lai, 0) as hai_so_lai'),
                    DB::raw('COALESCE(ba_so, 0) as ba_so_danh'),
                    DB::raw('COALESCE(ba_so_lai, 0) as ba_so_lai'),
                    DB::raw('COALESCE(bon_so, 0) as bon_so_danh'),
                    DB::raw('COALESCE(bon_so_lai, 0) as bon_so_lai'),
                    DB::raw('COALESCE(dau_duoi, 0) as dau_duoi_danh'),
                    DB::raw('COALESCE(dau_duoi_lai, 0) as dau_duoi_lai'),
                    DB::raw('COALESCE(bao_lo_2_so, 0) as bao_lo_danh_2_so'),
                    DB::raw('COALESCE(bao_lo_3_so, 0) as bao_lo_danh_3_so'),
                    DB::raw('COALESCE(bao_lo_4_so, 0) as bao_lo_danh_4_so'),
                    DB::raw('COALESCE(bao_lo_2_so_lai, 0) as bao_lo_2_so_lai'),
                    DB::raw('COALESCE(bao_lo_3_so_lai, 0) as bao_lo_3_so_lai'),
                    DB::raw('COALESCE(bao_lo_4_so_lai, 0) as bao_lo_4_so_lai'),
                    DB::raw('COALESCE(xiu_chu, 0) as xiu_chu_danh'),
                    DB::raw('COALESCE(xiu_chu_lai, 0) as xiu_chu_lai'),
                    DB::raw('COALESCE(da_thang, 0) as da_thang_danh'),
                    DB::raw('COALESCE(da_thang_lai, 0) as da_thang_lai'),
                    DB::raw('COALESCE(da_xien, 0) as da_xien_danh'),
                    DB::raw('COALESCE(da_xien_lai, 0) as da_xien_lai'),
                    DB::raw('COALESCE(total_danh, 0) as tong_danh'),
                    DB::raw('COALESCE(total_lai, 0) as tong_lai'),
                    'so_danh',
                    'so_trung',
                    'date_check',
                    'region_id',
                    'area_id'
                )
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->where('region_id', '=', $region)
                ->whereHas('customer', function ($query) {
                    $machineUser = auth()->guard('machine')->user();
                    if ($machineUser) {
                        $query->where('machine_id', $machineUser->id);
                    }
                })
                ->get();
        }

        $table1 = $report_details->groupBy('area_id')
            ->map(function ($group) {
                return [
                    'Hai số' => ["danh" => $group->sum('hai_so_danh'), "trung" => $group->sum('hai_so_lai')],
                    'Ba số' => ["danh" => $group->sum('ba_so_danh'), "trung" => $group->sum('ba_so_lai')],
                    'Bốn số' => ["danh" => $group->sum('bon_so_danh'), "trung" => $group->sum('bon_so_lai')],
                    'Đầu đuôi' => ["danh" => $group->sum('dau_duoi_danh'), "trung" => $group->sum('dau_duoi_lai')],
                    '2 Số Bao lô' => ["danh" => $group->sum('bao_lo_danh_2_so'), "trung" => $group->sum('bao_lo_2_so_lai')],
                    '3 Số Bao lô' => ["danh" => $group->sum('bao_lo_danh_3_so'), "trung" => $group->sum('bao_lo_3_so_lai')],
                    '4 Số Bao lô' => ["danh" => $group->sum('bao_lo_danh_4_so'), "trung" => $group->sum('bao_lo_4_so_lai')],
                    'Xỉu chủ' => ["danh" => $group->sum('xiu_chu_danh'), "trung" => $group->sum('xiu_chu_lai')],
                    'Đá thẳng' => ["danh" => $group->sum('da_thang_danh'), "trung" => $group->sum('da_thang_lai')],
                    'Đá xiên' => ["danh" => $group->sum('da_xien_danh'), "trung" => $group->sum('da_xien_lai')],
                    'Tổng' => ["danh" => $group->sum('tong_danh'), "trung" => $group->sum('tong_lai')],
                    'area_id' => $group->first()->area_id,
                    'area_name' => $this->getDaiName($group->first()->area_id)
                ];
            })->values();
        $hai_so_bao_lo = [];
        $ba_so_bao_lo = [];
        $bon_so_bao_lo = [];
        $bon_so_duoi = [];
        $dau = [];
        $duoi = [];
        $dau_duoi = [];
        $xiu_chu = [];
        $xiu_chu_dau = [];
        $xiu_chu_duoi = [];
        $da_xien = [];
        $da_thang = [];
        $bay_lo = [];
        $bay_lo_dao = [];
        $tam_lo = [];
        $tam_lo_dao = [];
        $so_trung = [];
        $region_enum = AreaEnum::getValueByRegion($region);
        if ($region != 2) {
            $so_trung[$region_enum] = [];
        }
        foreach ($report_details as $each) {
            foreach ($each['so_danh'] as $key => $each_so_danh) {

                $detail = $this->getDetail($key, $this->getDaiName($each['area_id']));
                $keyy = $detail[0] . ',' . $detail[2];
                switch ($detail[1]) {
                    case 'da_thang':
                        if (!array_key_exists($keyy, $da_thang)) {
                            $da_thang[$keyy] = $each_so_danh;
                        } else {
                            $da_thang[$keyy]['count'] += $each_so_danh['count'];
                            $da_thang[$keyy]['danh'] += $each_so_danh['danh'];
                        }
                        break;
                    case 'da_xien':
                        if (!array_key_exists($keyy, $da_xien)) {
                            $da_xien[$keyy] = $each_so_danh;
                        } else {
                            $da_xien[$keyy]['count'] += $each_so_danh['count'];
                            $da_xien[$keyy]['danh'] += $each_so_danh['danh'];
                        }
                        break;
                    case 'dau':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $dau)) {
                                $dau[$keyy] = $each_so_danh;
                            } else {
                                $dau[$keyy]['count'] += $each_so_danh['count'];
                                $dau[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu_dau)) {
                                $xiu_chu_dau[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu_dau[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu_dau[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'duoi':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $duoi)) {
                                $duoi[$keyy] = $each_so_danh;
                            } else {
                                $duoi[$keyy]['count'] += $each_so_danh['count'];
                                $duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu_duoi)) {
                                $xiu_chu_duoi[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 4) {
                            if (!array_key_exists($keyy, $bon_so_duoi)) {
                                $bon_so_duoi[$keyy] = $each_so_danh;
                            } else {
                                $bon_so_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $bon_so_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'dau_duoi':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $dau_duoi)) {
                                $dau_duoi[$keyy] = $each_so_danh;
                            } else {
                                $dau_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $dau_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu)) {
                                $xiu_chu[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'bao_lo':

                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $hai_so_bao_lo)) {
                                $hai_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $hai_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $hai_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $ba_so_bao_lo)) {
                                $ba_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $ba_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $ba_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 4) {
                            if (!array_key_exists($keyy, $bon_so_bao_lo)) {
                                $bon_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $bon_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $bon_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case '7lo':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $bay_lo)) {
                                $bay_lo[$keyy] = $each_so_danh;
                            } else {
                                $bay_lo[$keyy]['count'] += $each_so_danh['count'];
                                $bay_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } else {
                            if (!array_key_exists($keyy, $bay_lo_dao)) {
                                $bay_lo_dao[$keyy] = $each_so_danh;
                            } else {
                                $bay_lo_dao[$keyy]['count'] += $each_so_danh['count'];
                                $bay_lo_dao[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }

                        break;
                    case '8lo':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $tam_lo)) {
                                $tam_lo[$keyy] = $each_so_danh;
                            } else {
                                $tam_lo[$keyy]['count'] += $each_so_danh['count'];
                                $tam_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } else {
                            if (!array_key_exists($keyy, $tam_lo_dao)) {
                                $tam_lo_dao[$keyy] = $each_so_danh;
                            } else {
                                $tam_lo_dao[$keyy]['count'] += $each_so_danh['count'];
                                $tam_lo_dao[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                }
            }

            if ($each['so_trung'] != null) {
                foreach ($each['so_trung'] as $key => $each_trung) {
                    $detail = $this->getDetail($key, $this->getDaiName($each['area_id']));
                    if (!array_key_exists($each['area_id'], $so_trung)) {
                        $so_trung[$each['area_id']] = [];
                    }
                    $keyy = $detail[0] . ',' . $detail[1] . ',' . $detail[2];
                    if ($detail[1] == 'tru_da_xien') {
                        $keyy = $detail[0] . ',' . 'da_xien' . ',' . $detail[2];
                        if (!array_key_exists($keyy, $so_trung[$region_enum])) {
                            $so_trung[$region_enum][$keyy] = $each_trung;
                        } else {
                            $so_trung[$region_enum][$keyy]['count'] += $each_trung['count'];
                            $so_trung[$region_enum][$keyy]['danh'] += $each_trung['danh'];
                            $so_trung[$region_enum][$keyy]['trung'] += $each_trung['trung'];
                        }
                    } else {
                        if (!array_key_exists($keyy, $so_trung[$each['area_id']])) {
                            $so_trung[$each['area_id']][$keyy] = $each_trung;
                        } else {
                            $so_trung[$each['area_id']][$keyy]['count'] += $each_trung['count'];
                            $so_trung[$each['area_id']][$keyy]['danh'] += $each_trung['danh'];
                            $so_trung[$each['area_id']][$keyy]['trung'] += $each_trung['trung'];
                        }

                        if ($region != 2) {
                            if (!array_key_exists($keyy, $so_trung[$region_enum])) {
                                $so_trung[$region_enum][$keyy] = $each_trung;
                            } else {
                                $so_trung[$region_enum][$keyy]['count'] += $each_trung['count'];
                                $so_trung[$region_enum][$keyy]['danh'] += $each_trung['danh'];
                                $so_trung[$region_enum][$keyy]['trung'] += $each_trung['trung'];
                            }
                            if ($detail[1] == 'da_xien') {
                                $so_trung[$region_enum][$keyy]['count'] -= $each_trung['count'] / 2;
                                $so_trung[$region_enum][$keyy]['danh'] -= $each_trung['danh'] / 2;
                                $so_trung[$region_enum][$keyy]['trung'] -= $each_trung['trung'] / 2;
                            }
                        }
                    }
                }
            }
        }

        return response()->json([
            'table1' => $table1,
            'hai_so_bao_lo' => $hai_so_bao_lo,
            'ba_so_bao_lo' => $ba_so_bao_lo,
            'bon_so_bao_lo' => $bon_so_bao_lo,
            'bon_so_duoi' => $bon_so_duoi,
            'dau' => $dau,
            'duoi' => $duoi,
            'dau_duoi' => $dau_duoi,
            'xiu_chu' => $xiu_chu,
            'xiu_chu_dau' => $xiu_chu_dau,
            'xiu_chu_duoi' => $xiu_chu_duoi,
            'da_xien' => $da_xien,
            'da_thang' => $da_thang,
            'bay_lo' => $bay_lo,
            'tam_lo' => $tam_lo,
            'bay_lo_dao' => $bay_lo_dao,
            'tam_lo_dao' => $tam_lo_dao,
            'so_trung' => $so_trung,
        ]);
    }

    public function report_detail_all_api_show($customer_id, Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        if ($customer_id != 0) {
            $report_details = ReportDetail::query()
                ->select(
                    'customer_id',
                    'so_danh',
                    'so_trung',
                    'date_check',
                    'region_id',
                    'area_id'
                )
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->where('customer_id', '=', (int)$customer_id)
                ->get();
        } else {
            $report_details = ReportDetail::query()
                ->select(
                    'customer_id',
                    'so_danh',
                    'so_trung',
                    'date_check',
                    'region_id',
                    'area_id'
                )
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->get();
        }


        $hai_so_bao_lo = [];
        $ba_so_bao_lo = [];
        $bon_so_bao_lo = [];
        $bon_so_duoi = [];
        $dau = [];
        $duoi = [];
        $dau_duoi = [];
        $xiu_chu = [];
        $xiu_chu_dau = [];
        $xiu_chu_duoi = [];
        $da_xien = [];
        $da_thang = [];
        $bay_lo = [];
        $tam_lo = [];
        $bay_lo_dao = [];
        $tam_lo_dao = [];
        $so_trung = [];

        foreach ($report_details as $each) {
            foreach ($each['so_danh'] as $key => $each_so_danh) {

                $detail = $this->getDetail($key, $this->getDaiName($each['area_id']));
                $keyy = $detail[0] . ',' . $detail[2];
                switch ($detail[1]) {
                    case 'da_thang':
                        if (!array_key_exists($keyy, $da_thang)) {
                            $da_thang[$keyy] = $each_so_danh;
                        } else {
                            $da_thang[$keyy]['count'] += $each_so_danh['count'];
                            $da_thang[$keyy]['danh'] += $each_so_danh['danh'];
                        }
                        break;
                    case 'da_xien':
                        if (!array_key_exists($keyy, $da_xien)) {
                            $da_xien[$keyy] = $each_so_danh;
                        } else {
                            $da_xien[$keyy]['count'] += $each_so_danh['count'];
                            $da_xien[$keyy]['danh'] += $each_so_danh['danh'];
                        }
                        break;
                    case 'dau':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $dau)) {
                                $dau[$keyy] = $each_so_danh;
                            } else {
                                $dau[$keyy]['count'] += $each_so_danh['count'];
                                $dau[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu_dau)) {
                                $xiu_chu_dau[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu_dau[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu_dau[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'duoi':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $duoi)) {
                                $duoi[$keyy] = $each_so_danh;
                            } else {
                                $duoi[$keyy]['count'] += $each_so_danh['count'];
                                $duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu_duoi)) {
                                $xiu_chu_duoi[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 4) {
                            if (!array_key_exists($keyy, $bon_so_duoi)) {
                                $bon_so_duoi[$keyy] = $each_so_danh;
                            } else {
                                $bon_so_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $bon_so_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'dau_duoi':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $dau_duoi)) {
                                $dau_duoi[$keyy] = $each_so_danh;
                            } else {
                                $dau_duoi[$keyy]['count'] += $each_so_danh['count'];
                                $dau_duoi[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } elseif (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $xiu_chu)) {
                                $xiu_chu[$keyy] = $each_so_danh;
                            } else {
                                $xiu_chu[$keyy]['count'] += $each_so_danh['count'];
                                $xiu_chu[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'bao_lo_2_so':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $hai_so_bao_lo)) {
                                $hai_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $hai_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $hai_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'bao_lo_3_so':
                        if (strlen($detail[0]) == 3) {
                            if (!array_key_exists($keyy, $ba_so_bao_lo)) {
                                $ba_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $ba_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $ba_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case 'bao_lo_4_so':
                        if (strlen($detail[0]) == 4) {
                            if (!array_key_exists($keyy, $bon_so_bao_lo)) {
                                $bon_so_bao_lo[$keyy] = $each_so_danh;
                            } else {
                                $bon_so_bao_lo[$keyy]['count'] += $each_so_danh['count'];
                                $bon_so_bao_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }
                        break;
                    case '7lo':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $bay_lo)) {
                                $bay_lo[$keyy] = $each_so_danh;
                            } else {
                                $bay_lo[$keyy]['count'] += $each_so_danh['count'];
                                $bay_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } else {
                            if (!array_key_exists($keyy, $bay_lo_dao)) {
                                $bay_lo_dao[$keyy] = $each_so_danh;
                            } else {
                                $bay_lo_dao[$keyy]['count'] += $each_so_danh['count'];
                                $bay_lo_dao[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }

                        break;
                    case '8lo':
                        if (strlen($detail[0]) == 2) {
                            if (!array_key_exists($keyy, $tam_lo)) {
                                $tam_lo[$keyy] = $each_so_danh;
                            } else {
                                $tam_lo[$keyy]['count'] += $each_so_danh['count'];
                                $tam_lo[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        } else {
                            if (!array_key_exists($keyy, $tam_lo_dao)) {
                                $tam_lo_dao[$keyy] = $each_so_danh;
                            } else {
                                $tam_lo_dao[$keyy]['count'] += $each_so_danh['count'];
                                $tam_lo_dao[$keyy]['danh'] += $each_so_danh['danh'];
                            }
                        }

                        break;
                }
            }

            if ($each['so_trung'] != null) {
                foreach ($each['so_trung'] as $key => $each_trung) {
                    $detail = $this->getDetail($key, $this->getDaiName($each['area_id']));
                    $keyy = $detail[0] . ',' . $detail[1] . ',' . $detail[2];

                    if (!array_key_exists($keyy, $so_trung)) {
                        $so_trung[$keyy] = $each_trung;
                    } else {
                        $so_trung[$keyy]['count'] += $each_trung['count'];
                        $so_trung[$keyy]['danh'] += $each_trung['danh'];
                        $so_trung[$keyy]['trung'] += $each_trung['trung'];
                    }
                    if ($detail[1] == 'da_xien') {
                        $so_trung[$keyy]['count'] -= $each_trung['count'] / 2;
                        $so_trung[$keyy]['danh'] -= $each_trung['danh'] / 2;
                        $so_trung[$keyy]['trung'] -= $each_trung['trung'] / 2;
                    }


                }
            }
        }

        return response()->json([
            'hai_so_bao_lo' => $hai_so_bao_lo,
            'ba_so_bao_lo' => $ba_so_bao_lo,
            'bon_so_bao_lo' => $bon_so_bao_lo,
            'bon_so_duoi' => $bon_so_duoi,
            'dau' => $dau,
            'duoi' => $duoi,
            'dau_duoi' => $dau_duoi,
            'xiu_chu' => $xiu_chu,
            'xiu_chu_dau' => $xiu_chu_dau,
            'xiu_chu_duoi' => $xiu_chu_duoi,
            'da_xien' => $da_xien,
            'da_thang' => $da_thang,
            'bay_lo' => $bay_lo,
            'tam_lo' => $tam_lo,
            'bay_lo_dao' => $bay_lo_dao,
            'tam_lo_dao' => $tam_lo_dao,
            'so_trung' => $so_trung,
        ]);
    }

    private function getDetail($so, $dai)
    {

        $detail = explode('.', $so);
//        dd($detail);
        if (count($detail) == 1) {

            return [$detail[0], 'bao_lo', $dai];
        } elseif (count($detail) == 4) {
            return [$detail[0] . ' ' . $detail[1], 'da_xien', $this->getDaiName($detail[2]) . ' ' . $this->getDaiName($detail[3])];
        }
        switch (end($detail)) {
            case 'a':
                return [$detail[0], 'dau', $dai];
                break;
            case 'u':
                return [$detail[0], 'duoi', $dai];
                break;
            case 'd':
                return [$detail[0], 'dau_duoi', $dai];
                break;
            case '7l':
                return [$detail[0], '7lo', $dai];
                break;
            case '8l':
                return [$detail[0], '8lo', $dai];
                break;
            case 'm':
                return [$detail[0] . ' ' . $detail[1], 'tru_da_xien', $this->getDaiName($detail[2]) . ' ' . $this->getDaiName($detail[3])];
                break;
            default:
                return [$detail[0] . ' ' . $detail[1], 'da_thang', $dai];
                break;
        }
    }

    private function getDaiName($dai)
    {
        if ($dai == AreaEnum::MB) {
            return "Miền Bắc";
        } elseif ($dai == AreaEnum::MN_HCM) {
            return "TP. HCM";
        } elseif ($dai == AreaEnum::MN_VINH_LONG) {
            return "Vĩnh Long";
        } elseif ($dai == AreaEnum::MN_BINH_DUONG) {
            return "Bình Dương";
        } elseif ($dai == AreaEnum::MN_TRA_VINH) {
            return "Trà Vinh";
        } elseif ($dai == AreaEnum::MN_TAY_NINH) {
            return "Tây Ninh";
        } elseif ($dai == AreaEnum::MN_AN_GIANG) {
            return "An Giang";
        } elseif ($dai == AreaEnum::MN_LONG_AN) {
            return "Long An";
        } elseif ($dai == AreaEnum::MN_BINH_PHUOC) {
            return "Bình Phước";
        } elseif ($dai == AreaEnum::MN_HAU_GIANG) {
            return "Hậu Giang";
        } elseif ($dai == AreaEnum::MN_TIEN_GIANG) {
            return "Tiền Giang";
        } elseif ($dai == AreaEnum::MN_KIEN_GIANG) {
            return "Kiên Giang";
        } elseif ($dai == AreaEnum::MN_LAM_DONG) {
            return "Đà Lạt";
        } elseif ($dai == AreaEnum::MN_DONG_NAI) {
            return "Đồng Nai";
        } elseif ($dai == AreaEnum::MN_CAN_THO) {
            return "Cần Thơ";
        } elseif ($dai == AreaEnum::MN_SOC_TRANG) {
            return "Sóc Trăng";
        } elseif ($dai == AreaEnum::MN_DONG_THAP) {
            return "Đồng Tháp";
        } elseif ($dai == AreaEnum::MN_VUNG_TAU) {
            return "Vũng Tàu";
        } elseif ($dai == AreaEnum::MN_BAC_LIEU) {
            return "Bạc Liêu";
        } elseif ($dai == AreaEnum::MN_CA_MAU) {
            return "Cà Mau";
        } elseif ($dai == AreaEnum::MN_BEN_TRE) {
            return "Bến Tre";
        } elseif ($dai == AreaEnum::MN_BINH_THUAN) {
            return "Bình Thuận";
        } elseif ($dai == AreaEnum::MT_BINH_DINH) {
            return "Bình Định";
        } elseif ($dai == AreaEnum::MT_DAK_LAK) {
            return "Đắk Lắk";
        } elseif ($dai == AreaEnum::MT_DA_NANG) {
            return "Đà Nẵng";
        } elseif ($dai == AreaEnum::MT_DAK_NONG) {
            return "Đắk Nông";
        } elseif ($dai == AreaEnum::MT_THUA_THIEN_HUE) {
            return "Huế";
        } elseif ($dai == AreaEnum::MT_GIA_LAI) {
            return "Gia Lai";
        } elseif ($dai == AreaEnum::MT_KHANH_HOA) {
            return "Khánh Hòa";
        } elseif ($dai == AreaEnum::MT_KON_TUM) {
            return "Kon Tum";
        } elseif ($dai == AreaEnum::MT_NINH_THUAN) {
            return "Ninh Thuận";
        } elseif ($dai == AreaEnum::MT_PHU_YEN) {
            return "Phú Yên";
        } elseif ($dai == AreaEnum::MT_QUANG_BINH) {
            return "Quảng Bình";
        } elseif ($dai == AreaEnum::MT_QUANG_TRI) {
            return "Quảng Trị";
        } elseif ($dai == AreaEnum::MT_QUANG_NAM) {
            return "Quảng Nam";
        } elseif ($dai == AreaEnum::MT_QUANG_NGAI) {
            return "Quảng Ngãi";
        } elseif ($dai == AreaEnum::MN) {
            return "Miền Nam";
        } elseif ($dai == AreaEnum::MT) {
            return "Miền Trung";
        } else {
            return "Unknown";
        }
    }


    public function destroyStatistic($id)
    {

        $customer = Statistics::query()->findOrFail($id);
        $customer->delete();
        $path = url('statistic/#menu2');
        return redirect()->to($path);

    }

    public function statistic()
    {


        if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {

            if (auth()->guard('shareholder')->user()->user_id == null) {

                $shareholders = Shareholder::query()
                    ->with('shareholderCustomers.customer')
                    ->where('id', auth()->guard('shareholder')->user()->id)
                    ->get()
                    ->map(function ($item) {
//                        $arr = [];
//                        $item->shareholderCustomers->map(function ($item2) use (&$arr) {
//                            if ($item2->customer != null) {
//                                $item2->customer->is_send = $item2->is_send;
//                                $item2->customer->percent = $item2->percent;
//
//                                $arr[] = $item2->customer;
//                            }
//                        });
//                        unset($item->shareholderCustomers);
                        $item->children = $item->shareholderCustomers;

                        return $item;
                    });

                $statistics = Statistics::query()
                    ->select(['id', 'name', 'created_at'])
                    ->where('shareholder_id', auth()->guard('shareholder')->user()->id)->latest()->get();

                return view('statistic.index', [
                    'shareholders' => $shareholders,
                    'statistics' => $statistics,
                    'username' => null,
                ]);
            } else {

                $shareholders = Shareholder::query()
                    ->with('shareholderCustomers.customer')
                    ->whereHas('shareholderCustomers')
                    ->where('id', auth()->guard('shareholder')->user()->id)
                    ->get()
                    ->map(function ($item) {
//                        $arr = [];
//                        $item->shareholderCustomers->map(function ($item2) use (&$arr) {
//                            if ($item2->customer != null) {
//                                $item2->customer->is_send = $item2->is_send;
//                                $item2->customer->percent = $item2->percent;
//                                $arr[] = $item2->customer;
//                            }
//                        });
//                        unset($item->shareholderCustomers);
                        $item->children = $item->shareholderCustomers;

                        return $item;
                    });

                $statistics = Statistics::query()
                    ->select(['id', 'name', 'created_at'])
                    ->where('shareholder_id', auth()->guard('shareholder')->user()->id)
                    ->latest()->get();
                return view('statistic.index_admin', [
                    'shareholders' => $shareholders,
                    'statistics' => $statistics,
                    'username' => null,
                    'accountants' => null,
                ]);
            }

        } else if (auth()->guard('machine')->check()) {

            $shareholders = Shareholder::query()
                ->with('shareholderCustomers.customer')
                ->where('machine_id', auth()->guard('machine')->user()->id)
                ->get()
                ->map(function ($item) {
//                    $arr = [];
//                    $item->shareholderCustomers->map(function ($item2) use (&$arr) {
//                        if ($item2->machine != null) {
//                            $item2->machine->is_send = $item2->is_send;
//                            $item2->machine->percent = $item2->percent;
//                            $arr[] = $item2->machine;
//                        }
//                        if ($item2->customer != null) {
//                            $item2->customer->is_send = $item2->is_send;
//                            $item2->customer->percent = $item2->percent;
//                            $arr[] = $item2->customer;
//                        }
//                    });
//                    unset();
                    $item->children = $item->shareholderCustomers;
                    return $item;
                });

            $statistics = Statistics::query()
                ->select(['id', 'name', 'created_at'])
                ->where('machine_id', auth()->guard('machine')->user()->id)->latest()->get();
            return view('statistic.index', [
                'shareholders' => $shareholders,
                'statistics' => $statistics,
                'username' => auth()->guard('machine')->check() ? auth()->guard('machine')->user()->username . '0000' . Shareholder::max('id') :
                    auth()->user()->username . '0000' . Shareholder::max('id'),
            ]);
        } else if (auth()->guard('accountant')->check()) {

            $shareholders = Shareholder::query()
                ->with('shareholderCustomers.customer')
                ->whereHas('shareholderCustomers')
                ->where('accountant_id', auth()->guard('accountant')->user()->id)
                ->get()
                ->map(function ($item) {
                    $arr = [];
                    $item->shareholderCustomers->map(function ($item2) use (&$arr) {
                        if ($item2->machine != null) {
                            unset($item2->machine);
                            $item2->machine->is_send = $item2->is_send;
                            $item2->machine->username = $item2->username;
                            $item2->machine->percent = $item2->percent;
                            $arr[] = $item2->machine;
                        }
                        if ($item2->customer != null) {
                            unset($item2->customer);
                            $item2->customer->is_send = $item2->is_send;
                            $item2->customer->username = $item2->username;

                            $item2->customer->percent = $item2->percent;
                            $arr[] = $item2->customer;
                        }

                    });
                    unset($item->shareholderCustomers);
                    $item->children = $arr;
                    return $item;
                });

            $statistics = Statistics::query()
                ->select(['id', 'name', 'created_at'])
                ->where('accountant_id', auth()->guard('accountant')->user()->id)
                ->latest()->get(10);

            $accountants = Accountant::query()->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id)->get();
            if (auth()->guard('accountant')->check()) {
                $username = User::query()->where('id', auth()->guard('accountant')->user()->user_id)->first()->username . '0000' . Shareholder::max('id');
                $username_accountant = "";
            } else {
                $username = auth()->guard('machine')->check() ? auth()->guard('machine')->user()->username . '0000' . Shareholder::max('id') :
                    auth()->user()->username . '0000' . Shareholder::max('id');
                $username_accountant = "ketoan_" . auth()->user()->username . '0000' . Accountant::max('id');
            }

            if (auth()->guard('accountant')->user()->parent_id != null) {
                $statistics = Statistics::query()
                    ->select(['id', 'name', 'created_at'])
                    ->where('accountant_id', auth()->guard('accountant')->user()->parent_id)
                    ->latest()->get(10);
                return view('sub-accountants.statistic', [
                    'shareholders' => $shareholders,
                    'statistics' => $statistics,
                    'username' => $username,
                    'accountants' => $accountants,
                    'username_accountant' => $username_accountant,
                ]);
            }
            return view('statistic.index_admin', [
                'shareholders' => $shareholders,
                'statistics' => $statistics,
                'username' => $username,
                'accountants' => $accountants,
                'username_accountant' => $username_accountant,
            ]);

        } else {

            $shareholders = Shareholder::query()
                ->with('shareholderCustomers.customer')
                ->whereHas('shareholderCustomers')
                ->where('user_id', auth()->user()->id)
                ->get()
                ->map(function ($item) {
                    $arr = [];
                    $item->shareholderCustomers->map(function ($item2) use (&$arr) {
                        if ($item2->machine != null) {
                            unset($item2->machine);
                            $item2->machine->is_send = $item2->is_send;
                            $item2->machine->percent = $item2->percent;
                            $arr[] = $item2->machine;
                        }
                        if ($item2->customer != null) {
                            unset($item2->customer);
                            $item2->customer->is_send = $item2->is_send;
                            $item2->customer->percent = $item2->percent;
                            $arr[] = $item2->customer;
                        }

                    });
                    unset($item->shareholderCustomers);
                    $item->children = $arr;
                    return $item;
                });

            $statistics = Statistics::query()
                ->select(['id', 'name', 'created_at'])
                ->where('user_id', auth()->user()->id)
                ->latest()->get(10);

            $accountants = Accountant::query()->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id)->get();
            if (auth()->guard('accountant')->check()) {
                $username = User::query()->where('id', auth()->guard('accountant')->user()->user_id)->first()->username . '0000' . Shareholder::max('id');
                $username_accountant = "";
            } else {
                $username = auth()->guard('machine')->check() ? auth()->guard('machine')->user()->username . '0000' . Shareholder::max('id') :
                    auth()->user()->username . '0000' . Shareholder::max('id');
                $username_accountant = "ketoan_" . auth()->user()->username . '0000' . Accountant::max('id');

            }
            if (auth()->user()->parent_sub_id != null) {

                return view('sub-admin.statistic', [
                    'shareholders' => $shareholders,
                    'statistics' => $statistics,
                    'username' => $username,
                    'accountants' => $accountants,
                    'username_accountant' => $username_accountant,
                ]);
            }

            return view('statistic.index_admin', [
                'shareholders' => $shareholders,
                'statistics' => $statistics,
                'username' => $username,
                'accountants' => $accountants,
                'username_accountant' => $username_accountant,
            ]);
        }


    }

    public function show_statistic($id)
    {
        $statistics = Statistics::query()->findOrFail($id);
        return view('statistic.detail', [
            'data' => $statistics->content,
            'name' => $statistics->name,
        ]);
    }

    public function createReport(Request $request)
    {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->from_date)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->to_date)));
        $shareHolderContent = Shareholder::query();
        if (auth()->guard('machine')->check()) {
            $shareHolderContent = $shareHolderContent->where('machine_id', auth()->guard('machine')->user()->id);

        } else if (auth()->guard('shareholder')->check()) {
            $shareHolderContent = $shareHolderContent->where('id', auth()->guard('shareholder')->user()->id);
        } else if (auth()->guard('accountant')->check()) {
            $shareHolderContent = $shareHolderContent->where('accountant_id', auth()->guard('accountant')->user()->id);

        } else {
            $shareHolderContent = $shareHolderContent->where('user_id', auth()->user()->id);
        }

        $shareHolderContent = $shareHolderContent->with('shareholderCustomers.customer.reports',
            function ($query) use ($date_start, $date_end) {
                $query
                    ->select('customer_id', DB::raw('sum(total_danh_lai) as tien_cuoc')
                        , DB::raw('sum(total_lai) as thang_thua')
                        , DB::raw('sum(total) as tong_tien')
                        , DB::raw('SUM(lai_report) as lai_ve_mien')
                    )
                    ->where('date_check', '>=', $date_start)
                    ->where('date_check', '<=', $date_end)
                    ->groupBy('customer_id');
            })->get()
            ->filter(function ($item) {
                return $item->shareholderCustomers->pluck('customer.reports')->flatten()->filter()->count() > 0;
            })
            ->map(function ($item) use ($date_start, $date_end) {
                $array = [];

                $thang_thua = 0;
                $tong_tien = 0;
                $laive = 0;

                $item->lai_ve = 0;
                $item->shareholderCustomers->each(function ($item2) use ($item, &$array, &$tien_cuoc, &$thang_thua, &$tong_tien, $date_start, $date_end, &$laive) {
                    $tien_cuoc = 0;

                    if ($item2->customer != null) {
                        $customerClone = clone $item2->customer;
                        $formula = $item2->is_send == 0 ? +1 : -1;
                        $customerClone->is_send = $item2->is_send;
                        $customerClone->percent = $item2->percent;

                        $customerClone->reports->map(function ($item3) use (&$customerClone, $formula, $item2, &$tien_cuoc, &$thang_thua, &$tong_tien, $date_start, $date_end, &$laive) {

//                            $ttp = $customerClone->reports->sum('tien_cuoc');
                            $customerClone->tong_tien = $formula * ($item3->thang_thua * $item2->percent / 100);
                            $customerClone->thang_thua = $item3->thang_thua;
//                            $customerClone->tien_cuoc = $ttp - ($ttp * $item2->percent / 100);
                            $thang_thua += $item3->thang_thua;
                            $tien_cuoc += $thang_thua - $tong_tien;
//                            $laivemien = $item3->lai_report;
//                            $laive += $item3->lai_report;

                            $customerClone->lai_ve = 0;
                            $lv = 0;
                            $tt = 0;
                            $checkLaiVe3Areas = $item3->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                                && $item3->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                                && $item3->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;

                            if ($item3->customer->laive != 1) {
                                $start_date = Carbon::parse($date_start);
                                $end_date = Carbon::parse($date_end);
                                while ($start_date->lte($end_date)) {
                                    $total = Report::query()
                                        ->where('customer_id', $item3->customer_id)
                                        ->where('date_check', $start_date)->sum('total_lai');
                                    if ($total < 0) {
                                        $lv += abs($total) - abs($total * $item3->customer->laive);
                                        $tt += $total * $item3->customer->laive;
                                    } else {
                                        $tt += $total;
                                    }
                                    $start_date->addDay();
                                }
                                $item3->thang_thua = $tt;
                                $item3->lai_ve = $lv;
                                $customerClone->lai_ve = $lv;
                                $customerClone->thang_thua = $tt;
                                $customerClone->tong_tien = $formula * ($tt * $item2->percent / 100);
                            } else if (!$checkLaiVe3Areas) {
                                $item3->customer->lai_ve = $item3->lai_ve_mien;
                                $customerClone->lai_ve = $item3->lai_ve_mien;
                            }
                            unset($item3->customer->rations);
                            $laive += $customerClone->lai_ve;
                            $tong_tien += $customerClone->tong_tien;

                        });
//                        $customerClone->percent = 0;
                        $customerClone->tien_cuoc = $customerClone->reports->sum('tien_cuoc');
                        unset($customerClone->customer);
                        $array[] = $customerClone;

                    }

                });

                $item->tong_tien = $tong_tien;
                $item->thang_thua = $thang_thua;
                $item->tien_cuoc = $tien_cuoc;
                $item->lai_ve = $laive;
                unset($item->reports);
                $item->children = $array;

                return $item;
            })->toJson();
        if (auth()->guard('shareholder')->check()) {
            $statistics = Statistics::updateOrCreate([
                'name' => "KT $date_start đến $date_end",
                'shareholder_id' => auth()->guard('shareholder')->user()->id,
            ],
                [

                    'content' => $shareHolderContent
                ]);
        } else
            if (auth()->guard('machine')->check()) {
                $statistics = Statistics::updateOrCreate([
                    'name' => "KT $date_start đến $date_end",
                    'machine_id' => auth()->guard('machine')->user()->id,

                ], [

                    'content' => $shareHolderContent
                ]);
            } else
                if (auth()->guard('accountant')->check()) {
                    $statistics = Statistics::updateOrCreate([
                        'name' => "KT $date_start đến $date_end",
                        'accountant_id' => auth()->guard('accountant')->user()->id,

                    ], [
                        'content' => $shareHolderContent
                    ]);
                } else {
                    $statistics = Statistics::updateOrCreate([
                        'name' => "KT $date_start đến $date_end",
                        'user_id' => auth()->user()->id,

                    ], [

                        'content' => $shareHolderContent
                    ]);
                }

        return redirect()->route('statistic.show', $statistics->id);
    }

    public function statistic_api(Request $request)
    {


        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_start)));
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $request->date_end)));
        if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {

            if (auth()->guard('shareholder')->user()->user_id == null) {

                return Report::query()->select('customer_id', DB::raw('sum(total_danh_lai) as tien_cuoc')
                    , DB::raw('sum(total_lai) as thang_thua')
                    , DB::raw('sum(total) as tong_tien')
                    , DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam')
                    , DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac')
                    , DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung')
                    , DB::raw('SUM(lai_report) as lai_ve_mien')
                )
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                    })
                    ->groupBy('customer_id')
                    ->get()->map(function ($user) use ($date_start, $date_end) {
                        $user->username = $user->customer->username;
                        $array_name = [];
                        $array_percent = [];
                        $array_total_sum = [];
                        $array_shareholder_id = [];
                        // add lai ve va change thanh tien field(total_lai)
                        $user->lai_ve = 0;
                        $lv = 0;
                        $tt = 0;
                        if ($user->customer != null) {
                            $checkLaiVe3Areas = $user->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                                && $user->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                                && $user->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;
                            if ($user->customer->laive != 1) {
                                $start_date = Carbon::parse($date_start);
                                $end_date = Carbon::parse($date_end);
                                while ($start_date->lte($end_date)) {
                                    $total = Report::query()
                                        ->where('customer_id', $user->customer_id)
                                        ->where('date_check', $start_date)->sum('total_lai');
                                    if ($total < 0) {
                                        $lv += abs($total) - abs($total * $user->customer->laive);
                                        $tt += $total * $user->customer->laive;
                                    } else {
                                        $tt += $total;
                                    }
                                    $start_date->addDay();
                                }
                                $user->thang_thua = $tt;
                                $user->lai_ve = $lv;

                            } else if (!$checkLaiVe3Areas) {
                                $user->lai_ve = $user->lai_ve_mien;
                            }

                        }
                        $getFirstShareHolder = $user->customer->shareholderCustomers->where('shareholder_id', auth()->guard('shareholder')->user()->id)->first();

                        if ($getFirstShareHolder != null && $getFirstShareHolder->shareholder != null) {
                            $formula = $getFirstShareHolder->is_send == 0 ? +1 : -1;


                            $array_name[] = "<div>" . $getFirstShareHolder->shareholder->name . "</div>";
                            $array_shareholder_id[] = $getFirstShareHolder->shareholder->id;
                            $array_percent[] = "<div>" . (float)$getFirstShareHolder->percent . "</div>";
                            $array_total_sum[] = ($user->thang_thua * $getFirstShareHolder->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                . number_format($user->thang_thua * $getFirstShareHolder->percent * $formula / 100, 0, '.', ',')
                                . "</div>" : "<div>" . number_format($user->thang_thua * $getFirstShareHolder->percent * $formula / 100, 0, '.', ',') . "</div>";
                        }

                        $user->shareholder_ids = $array_shareholder_id;
                        $user->name_shareholder = implode("", $array_name);
                        $user->percent = implode("", $array_percent);
                        $user->total_sum = implode("", $array_total_sum);

                        unset($user->customer);
                        return $user;
                    });

            } else {

                $reports = Report::query()->select('customer_id', DB::raw('sum(total_danh_lai) as tien_cuoc')
                    , DB::raw('sum(total_lai) as thang_thua')
                    , DB::raw('sum(total) as tong_tien')
                    , DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam')
                    , DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac')
                    , DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung')
                    , DB::raw('SUM(lai_report) as lai_ve_mien')
                )
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                    })
                    ->groupBy('customer_id')
                    ->get()->map(function ($user) use ($date_start, $date_end) {

                        $array_name = [];
                        $array_percent = [];
                        $array_total_sum = [];
                        $array_shareholder_id = [];
                        $user->lai_ve = 0;
                        $lv = 0;
                        $tt = 0;
                        if ($user->customer != null) {

                            $checkLaiVe3Areas = $user->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                                && $user->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                                && $user->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;
                            if ($user->customer->laive != 1) {
                                $start_date = Carbon::parse($date_start);
                                $end_date = Carbon::parse($date_end);
                                while ($start_date->lte($end_date)) {
                                    $total = Report::query()
                                        ->where('customer_id', $user->customer_id)
                                        ->where('date_check', $start_date)->sum('total_lai');
                                    if ($total < 0) {
                                        $lv += abs($total) - abs($total * $user->customer->laive);
                                        $tt += $total * $user->customer->laive;
                                    } else {
                                        $tt += $total;
                                    }
                                    $start_date->addDay();
                                }
                                $user->thang_thua = $tt;
                                $user->lai_ve = $lv;

                            } else if (!$checkLaiVe3Areas) {
                                $user->lai_ve = $user->lai_ve_mien;
                            }

                            $user->username = $user->customer->username;
                            $user->machine_id = $user->customer->machine_id;
                            $shareholder_customers = $user->customer->shareholderCustomers->where('shareholder_id', auth()->guard('shareholder')->user()->id)->first();
                            if ($shareholder_customers != null && $shareholder_customers->shareholder != null) {
                                $formula = $shareholder_customers->is_send == 0 ? +1 : -1;

                                $array_name[] = "<div>" . $shareholder_customers->shareholder->name . "</div>";
                                $array_shareholder_id[] = $shareholder_customers->shareholder->id;
                                $array_percent[] = "<div>" . (float)$shareholder_customers->percent . "</div>";
                                $array_total_sum[] = ($user->thang_thua * $shareholder_customers->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                    . number_format($user->thang_thua * $shareholder_customers->percent * $formula / 100, 0, '.', ',')
                                    . "</div>" : "<div>" . number_format($user->thang_thua * $shareholder_customers->percent * $formula / 100, 0, '.', ',') . "</div>";
                            }

                        }
                        $user->shareholder_ids = $array_shareholder_id;
                        $user->name_shareholder = implode("", $array_name);
                        $user->percent = implode("", $array_percent);
                        $user->total_sum = implode("", $array_total_sum);
                        unset($user->customer);
                        return $user;
                    });

                return Machine::query()
                    ->select('id', 'username')
                    ->get()
                    ->map(function ($item) use ($reports) {
                        $array_name = [];
                        $array_percent = [];
                        $array_total_sum = [];
                        $array_shareholder_id = [];
                        $customers = $reports
                            ->where('machine_id', $item['id']);


                        $item->customersList = array_filter($customers->all(), function ($item) {
                            return in_array(auth()->guard('shareholder')->user()->id, $item->shareholder_ids);
                        });
                        if ($item->customersList) {
                            $item->tien_cuoc = $customers->sum('tien_cuoc');
                            $item->thang_thua = $customers->sum('thang_thua');

                            foreach ($item->shareholderCustomers->where('shareholder_id', auth()->guard('shareholder')->user()->id) as $shareholderCustomer) {
                                if ($shareholderCustomer->shareholder != null) {
                                    $formula = $shareholderCustomer->is_send == 0 ? +1 : -1;


                                    $array_name[] = "<div>" . $shareholderCustomer->shareholder->name . "</div>";
                                    $array_shareholder_id[] = $shareholderCustomer->shareholder->id;
                                    $array_percent[] = "<div>" . (float)$shareholderCustomer->percent . "</div>";
                                    $array_total_sum[] = ($item->thang_thua * $shareholderCustomer->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                        . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',')
                                        . "</div>" : "<div>" . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',') . "</div>";
                                }

                                $item->shareholder_ids = $array_shareholder_id;
                                $item->name_shareholder = implode("", $array_name);
                                $item->percent = implode("", $array_percent);
                                $item->total_sum = implode("", $array_total_sum);
                            }


                        }

                        return $item;
                    });
            }
        } else if (auth()->guard('machine')->user()) {
            return Report::query()->select(
                'customer_id',
                DB::raw('sum(total_danh_lai) as tien_cuoc'),
                DB::raw('sum(total_lai) as thang_thua'),
                DB::raw('sum(total) as tong_tien'),
                DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam'),
                DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac'),
                DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung'),
                DB::raw('SUM(lai_report) as lai_ve_mien')
            )
                ->join('customers', 'reports.customer_id', '=', 'customers.id')
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->with('customer.shareholderCustomers.shareholder', function ($query) {
                    $query->where('machine_id', auth()->guard('machine')->user()->id);
                })
                ->whereHas('customer', function ($query) {
                    $query->where('machine_id', auth()->guard('machine')->user()->id);
                })
                ->groupBy('customer_id')
                ->get()->map(function ($user) use ($date_start, $date_end) {
                    $user->username = $user->customer->username;
                    $array_name = [];
                    $array_percent = [];
                    $array_total_sum = [];
                    $array_shareholder_id = [];
                    // add lai ve va change thanh tien field(total_lai)
                    $user->lai_ve = 0;
                    $lv = 0;
                    $tt = 0;
                    if ($user->customer != null) {
                        $checkLaiVe3Areas = $user->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;
                        if ($user->customer->laive != 1) {
                            $start_date = Carbon::parse($date_start);
                            $end_date = Carbon::parse($date_end);
                            while ($start_date->lte($end_date)) {
                                $total = Report::query()
                                    ->where('customer_id', $user->customer_id)
                                    ->where('date_check', $start_date)->sum('total_lai');
                                if ($total < 0) {
                                    $lv += abs($total) - abs($total * $user->customer->laive);
                                    $tt += $total * $user->customer->laive;
                                } else {
                                    $tt += $total;
                                }
                                $start_date->addDay();
                            }
                            $user->thang_thua = $tt;
                            $user->lai_ve = $lv;
                        } else if (!$checkLaiVe3Areas) {
                            $user->lai_ve = $user->lai_ve_mien;
                        }
                    }
                    foreach ($user->customer->shareholderCustomers as $item) {
                        if ($item->shareholder != null) {
                            $formula = $item->is_send == 0 ? +1 : -1;
                            $stringGiao = $formula == +1 ? " (Giao +)" : " (Nhận -)";
                            $array_percent[] = "<div>" . (float)$item->percent . $stringGiao . "</div>";

                            $array_name[] = "<div>" . $item->shareholder->name . "</div>";
                            $array_shareholder_id[] = $item->shareholder->id;

                            $array_total_sum[] = ($user->thang_thua * $item->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',')
                                . "</div>" : "<div>" . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',') . "</div>";
                        }
                    }
                    $user->shareholder_ids = $array_shareholder_id;
                    $user->name_shareholder = implode("", $array_name);
                    $user->percent = implode("", $array_percent);
                    $user->total_sum = implode("", $array_total_sum);

                    unset($user->customer);
                    return $user;
                });
        }
        if (auth()->guard('accountant')->check()) {
            $reports = Report::query()->select(
                'customer_id',
                DB::raw('sum(total_danh_lai) as tien_cuoc'),
                DB::raw('sum(total_lai) as thang_thua'),
                DB::raw('sum(total) as tong_tien'),
                DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam'),
                DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac'),
                DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung'),
                DB::raw('SUM(lai_report) as lai_ve_mien')
            )
                ->join('customers', 'reports.customer_id', '=', 'customers.id')
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->with('customer.shareholderCustomers.shareholder', function ($query) {

                    $query->where('accountant_id', auth()->guard('accountant')->user()->id);
                })
                ->whereHas('customer.machine', function ($query) {
                    $query->where('user_id', auth()->guard('accountant')->user()->user_id);
                })
                ->groupBy('customer_id')
                ->get()->map(function ($user) use ($date_start, $date_end) {
                    $array_name = [];
                    $array_percent = [];
                    $array_total_sum = [];
                    $array_shareholder_id = [];

                    $user->lai_ve = 0;
                    $lv = 0;
                    $tt = 0;

                    if ($user->customer != null) {

                        $checkLaiVe3Areas = $user->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;
                        if ($user->customer->laive != 1) {
                            $start_date = Carbon::parse($date_start);
                            $end_date = Carbon::parse($date_end);
                            while ($start_date->lte($end_date)) {
                                $total = Report::query()
                                    ->where('customer_id', $user->customer_id)
                                    ->where('date_check', $start_date)->sum('total_lai');
                                if ($total < 0) {
                                    $lv += abs($total) - abs($total * $user->customer->laive);
                                    $tt += $total * $user->customer->laive;
                                } else {
                                    $tt += $total;
                                }
                                $start_date->addDay();
                            }
                            $user->thang_thua = $tt;
                            $user->lai_ve = $lv;
                        } else if (!$checkLaiVe3Areas) {
                            $user->lai_ve = $user->lai_ve_mien;
                        }

                        $user->username = $user->customer->username;
                        $user->machine_id = $user->customer->machine_id;

                        foreach ($user->customer->shareholderCustomers as $item) {

                            if ($item->shareholder != null) {
                                $formula = $item->is_send == 0 ? +1 : -1;

                                $stringGiao = $formula == +1 ? " (Giao +)" : " (Nhận -)";

                                $array_name[] = "<div>" . $item->shareholder->name . "</div>";
                                $array_shareholder_id[] = $item->shareholder->id;
                                $array_percent[] = "<div>" . (float)$item->percent . $stringGiao . "</div>";
                                $array_total_sum[] = ($user->thang_thua * $item->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                    . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',')
                                    . "</div>" : "<div>" . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',') . "</div>";
                            }
                        }
                    }
                    $user->shareholder_ids = $array_shareholder_id;
                    $user->name_shareholder = implode("", $array_name);
                    $user->percent = implode("", $array_percent);
                    $user->total_sum = implode("", $array_total_sum);

                    unset($user->customer);
                    return $user;
                });

            return Machine::query()
                ->select('id', 'username')
                ->get()
                ->map(function ($item) use ($reports, $date_start, $date_end) {
                    $array_name = [];
                    $array_percent = [];
                    $array_total_sum = [];
                    $array_shareholder_id = [];
                    $customers = $reports->where('machine_id', $item['id']);

                    $item->customersList = $customers->all();
                    $item->tien_cuoc = $customers->sum('tien_cuoc');
                    $item->thang_thua = $customers->sum('thang_thua');
                    $item->sum_nam = $customers->sum('sum_nam');
                    $item->sum_bac = $customers->sum('sum_bac');
                    $item->sum_trung = $customers->sum('sum_trung');
                    $item->lai_ve = $customers->sum('lai_ve');

                    foreach ($item->shareholderCustomers as $shareholderCustomer) {
                        if ($shareholderCustomer->shareholder != null && $shareholderCustomer->shareholder->accountant_id == auth('accountant')->user()->id) {
                            $formula = $shareholderCustomer->is_send == 0 ? +1 : -1;
                            $stringGiao = $formula == +1 ? " (Giao +)" : " (Nhận -)";

                            $array_name[] = "<div>" . $shareholderCustomer->shareholder->name . "</div>";
                            $array_shareholder_id[] = $shareholderCustomer->shareholder->id;
                            $array_percent[] = "<div>" . (float)$shareholderCustomer->percent . $stringGiao . "</div>";
                            $array_total_sum[] = ($item->thang_thua * $shareholderCustomer->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',')
                                . "</div>" : "<div>" . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',') . "</div>";
                        }
                    }
                    $item->shareholder_ids = $array_shareholder_id;
                    $item->name_shareholder = implode("", $array_name);
                    $item->percent = implode("", $array_percent);
                    $item->total_sum = implode("", $array_total_sum);


                    return $item;
                });
        } else {
            $shareHolderIds = [];
            if (auth()->user()->parent_sub_id != null) {
                $shareHolderIds = Shareholder::query()->where('user_id', auth()->user()->id)->pluck('id');
            }
            $user = auth()->user();
            $reports = Report::query()->select(
                'customer_id',
                DB::raw('sum(total_danh_lai) as tien_cuoc'),
                DB::raw('sum(total_lai) as thang_thua'),
                DB::raw('sum(total) as tong_tien'),
                DB::raw('SUM(CASE WHEN region_id = 1 THEN total_lai ELSE 0 END) as sum_nam'),
                DB::raw('SUM(CASE WHEN region_id = 2 THEN total_lai ELSE 0 END) as sum_bac'),
                DB::raw('SUM(CASE WHEN region_id = 3 THEN total_lai ELSE 0 END) as sum_trung'),
                DB::raw('SUM(lai_report) as lai_ve_mien')
            )
                ->join('customers', 'reports.customer_id', '=', 'customers.id')
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->with('customer.shareholderCustomers.shareholder', function ($query) use ($user) {


                    $query->where('user_id', $user->id);


                })
                ->whereHas('customer.machine', function ($query) use ($user) {
                    if ($user->parent_sub_id == null) {
                        $query->where('user_id', $user->id);
                    } else {

                        $query->where('user_id', $user->parent_sub_id);
                    }
                })
                ->groupBy('customer_id')
                ->get()->map(function ($user) use ($date_start, $date_end, $shareHolderIds) {
                    $array_name = [];
                    $array_percent = [];
                    $array_total_sum = [];
                    $array_shareholder_id = [];

                    $user->lai_ve = 0;
                    $lv = 0;
                    $tt = 0;

                    if ($user->customer != null) {

                        $checkLaiVe3Areas = $user->customer->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                            && $user->customer->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;
                        if ($user->customer->laive != 1) {
                            $start_date = Carbon::parse($date_start);
                            $end_date = Carbon::parse($date_end);
                            while ($start_date->lte($end_date)) {
                                $total = Report::query()
                                    ->where('customer_id', $user->customer_id)
                                    ->where('date_check', $start_date)->sum('total_lai');
                                if ($total < 0) {
                                    $lv += abs($total) - abs($total * $user->customer->laive);
                                    $tt += $total * $user->customer->laive;
                                } else {
                                    $tt += $total;
                                }
                                $start_date->addDay();
                            }
                            $user->thang_thua = $tt;
                            $user->lai_ve = $lv;
                        } else if (!$checkLaiVe3Areas) {
                            $user->lai_ve = $user->lai_ve_mien;
                        }

                        $user->username = $user->customer->username;
                        $user->machine_id = $user->customer->machine_id;
                        $listShareholders = $user->customer->shareholderCustomers;
                        if (auth()->user()->parent_sub_id != null) {
                            $listShareholders = $user->customer->shareholderCustomers->whereIn('shareholder_id', $shareHolderIds);
                        }
                        foreach ($listShareholders as $item) {

                            if ($item->shareholder != null) {
                                $formula = $item->is_send == 0 ? +1 : -1;
                                $stringGiao = $formula == +1 ? " (Giao +)" : " (Nhận -)";

                                $array_name[] = "<div>" . $item->shareholder->name . "</div>";
                                $array_shareholder_id[] = $item->shareholder->id;
                                $array_percent[] = "<div>" . (float)$item->percent . $stringGiao . "</div>";
                                $array_total_sum[] = ($user->thang_thua * $item->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                    . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',')
                                    . "</div>" : "<div>" . number_format($user->thang_thua * $item->percent * $formula / 100, 0, '.', ',') . "</div>";
                            }
                        }
                    }
                    $user->shareholder_ids = $array_shareholder_id;
                    $user->name_shareholder = implode("", $array_name);
                    $user->percent = implode("", $array_percent);
                    $user->total_sum = implode("", $array_total_sum);

                    unset($user->customer);
                    return $user;
                });

            return Machine::query()
                ->select('id', 'username')
                ->get()
                ->map(function ($item) use ($reports, $shareHolderIds) {
                    $array_name = [];
                    $array_percent = [];
                    $array_total_sum = [];
                    $array_shareholder_id = [];
                    $customers = $reports->where('machine_id', $item['id']);

                    $item->customersList = $customers->all();
                    $item->tien_cuoc = $customers->sum('tien_cuoc');
                    $item->thang_thua = $customers->sum('thang_thua');
                    $item->sum_nam = $customers->sum('sum_nam');
                    $item->sum_bac = $customers->sum('sum_bac');
                    $item->sum_trung = $customers->sum('sum_trung');
                    $item->lai_ve = $customers->sum('lai_ve');

                    $listShareholders = $item->shareholderCustomers;

                    if (auth()->user()->parent_sub_id != null) {
                        $listShareholders = $item->shareholderCustomers->whereIn('shareholder_id', $shareHolderIds);
                    }

                    foreach ($listShareholders as $shareholderCustomer) {
                        if ($shareholderCustomer->shareholder != null) {
                            $formula = $shareholderCustomer->is_send == 0 ? +1 : -1;
                            $stringGiao = $formula == +1 ? " (Giao +)" : " (Nhận -)";

                            $array_name[] = "<div>" . $shareholderCustomer->shareholder->name . "</div>";
                            $array_shareholder_id[] = $shareholderCustomer->shareholder->id;
                            $array_percent[] = "<div>" . (float)$shareholderCustomer->percent . $stringGiao . "</div>";
                            $array_total_sum[] = ($item->thang_thua * $shareholderCustomer->percent * $formula / 100) < 0 ? "<div class='text-danger'>"
                                . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',')
                                . "</div>" : "<div>" . number_format($item->thang_thua * $shareholderCustomer->percent * $formula / 100, 0, '.', ',') . "</div>";
                        }
                    }
                    $item->shareholder_ids = $array_shareholder_id;
                    $item->name_shareholder = implode("", $array_name);
                    $item->percent = implode("", $array_percent);
                    $item->total_sum = implode("", $array_total_sum);


                    return $item;
                });
        }

    }

    public function show($customer_id)
    {
        if (auth()->guard('machine')->check()) {
            $customer = Customer::query()->where('id', $customer_id)
                ->where('machine_id', auth()->guard('machine')->user()->id)
                ->firstOrFail();

        } else {

            $customer = Customer::query()->where('id', $customer_id)
                ->firstOrFail();

        }

        return view('report.show', [
            'name' => Customer::find($customer_id)->username,
            'customer_id' => Customer::find($customer_id)->id,
            'customer' => $customer
        ]);
    }

    public function detail($customer_id, Request $request)
    {
        if (auth()->guard('machine')->check()) {
            $customer = Customer::query()->where('id', $customer_id)
                ->where('machine_id', auth()->guard('machine')->user()->id)
                ->firstOrFail();
        } else {
            $customer = Customer::query()->where('id', $customer_id)
                ->firstOrFail();
        }
        $region = $request->region_id;

        if (empty($region)) {
            $region = 1;
        }
        $date_check = date('Y-m-d', strtotime($request->date));
        $ration_user = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::User)->where('region_id', $region)->get()->first();
        $countByRegionId = DB::table('tickets')
            ->select(['region_id', DB::raw('count(id) as sl')])
            ->where('customer_id', $customer_id)
            ->where('date_check', $date_check)
            ->groupBy("region_id")
            ->orderBy("region_id")
            ->get();
        $south_count = 0;
        $north_count = 0;
        $middle_count = 0;
        foreach ($countByRegionId as $item) {
            if ($item->region_id == 1) {
                $south_count = $item->sl;
            } elseif ($item->region_id == 2) {
                $north_count = $item->sl;
            } elseif ($item->region_id == 3) {
                $middle_count = $item->sl;
            }
        }

        return view('report.details', [
            'name' => Customer::find($customer_id)->username,
            'date' => $date_check,
            'customer_id' => Customer::find($customer_id)->id,
            'region_id' => $region,
            'count_south' => $south_count,
            'count_north' => $north_count,
            'count_middle' => $middle_count,
            'report' => Report::query()
                ->where('customer_id', $customer_id)
                ->where('date_check', $date_check)
                ->where('region_id', $region)
                ->get()->first(),
            'customer' => $customer,
            'ration_user' => $ration_user,
        ]);
    }

    public function statistic_detail($customer_id, Request $request)
    {
        $region = $request->region_id;
        $south_count = 0;
        $north_count = 0;
        $middle_count = 0;

        if ($region != 0 && empty($region)) {
            $region = 1;
        }
        $date_start = date('Y-m-d', strtotime($request->date_start));
        $date_end = date('Y-m-d', strtotime($request->date_end));
        $name = "tất cả khách hàng";

        if ($region != 0) {

            if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {
                if (auth()->guard('shareholder')->user()->user_id == null) {
                    $countByRegionId = Ticket::query()
                        ->select(['region_id', DB::raw('count(id) as sl')])
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                        });

                    $reportsQuery = Report::query()->select([
                        DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                        DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                        DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                        DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                        DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                        DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                        DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                        DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                        DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                        DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                        DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                        DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                        DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                        DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                        DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                        DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                        DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                        DB::raw('SUM(COALESCE(total, 0)) as total'),
                        DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                    ])
                        ->join('customers', 'reports.customer_id', '=', 'customers.id')
                        ->leftJoin('rations', 'reports.customer_id', '=', 'rations.customer_id')
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('reports.region_id', $region)->where('rations.region_id', $region)->where('rations.type', '=', TypeUser::User)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                        });


                } else {
                    $countByRegionId = Ticket::query()
                        ->select(['region_id', DB::raw('count(id) as sl')])
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                        });

                    $reportsQuery = Report::query()->select([
                        DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                        DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                        DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                        DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                        DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                        DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                        DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                        DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                        DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                        DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                        DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                        DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                        DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                        DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                        DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                        DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                        DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                        DB::raw('SUM(COALESCE(total, 0)) as total'),
                        DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                    ])
                        ->join('customers', 'reports.customer_id', '=', 'customers.id')
                        ->leftJoin('rations', 'reports.customer_id', '=', 'rations.customer_id')
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                        })
                        ->where('reports.region_id', $region)->where('rations.region_id', $region)->where('rations.type', '=', TypeUser::User);

                }
            } else if (auth()->guard('machine')->user()) {
                $countByRegionId = Ticket::query()
                    ->select(['region_id', DB::raw('count(tickets.id) as sl')])
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer', function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    });
                $reportsQuery = Report::query()->select([
                    DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                    DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                    DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                    DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                    DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                    DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                    DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                    DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                    DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                    DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                    DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                    DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                    DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                    DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                    DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                    DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                    DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                    DB::raw('SUM(COALESCE(total, 0)) as total'),
                    DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                ])
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->leftJoin('rations', 'reports.customer_id', '=', 'rations.customer_id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('reports.region_id', $region)
                    ->where('rations.region_id', $region)
                    ->where('rations.type', '=', TypeUser::User)
                    ->whereHas('customer', function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    });
            } else {
                $countByRegionId = Ticket::query()
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->select(['region_id', DB::raw('count(id) as sl')])
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    });

                $reportsQuery = Report::query()->select([
                    DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                    DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                    DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                    DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                    DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                    DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                    DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                    DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                    DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                    DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                    DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                    DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                    DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                    DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                    DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                    DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                    DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                    DB::raw('SUM(COALESCE(total, 0)) as total'),
                    DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')

                ])
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->leftJoin('rations', 'reports.customer_id', '=', 'rations.customer_id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('reports.region_id', $region)->where('rations.region_id', $region)->where('rations.type', '=', TypeUser::User)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    });
            }
            if ($customer_id != 0) {
                $name = Customer::find($customer_id)->username;
                $countByRegionId->where('customer_id', $customer_id);
                $reportsQuery->where('reports.customer_id', $customer_id);
            }
            $countByRegionId = $countByRegionId
                ->groupBy('region_id')
                ->orderBy('region_id')
                ->get();

            $reports = $reportsQuery->get();
            $ration_user = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::User)->where('region_id', $region)->get()->first();

            foreach ($countByRegionId as $item) {
                if ($item->region_id == 1) {
                    $south_count = $item->sl;
                } elseif ($item->region_id == 2) {
                    $north_count = $item->sl;
                } elseif ($item->region_id == 3) {
                    $middle_count = $item->sl;
                }
            }
            return view('statistic.show', [
                'name' => $name,
                'date_start' => $date_start,
                'date_end' => $date_end,
                'customer_id' => $customer_id,
                'region_id' => $region,
                'count_south' => $south_count,
                'count_north' => $north_count,
                'count_middle' => $middle_count,
                'report' => $reports[0],
                'ration_user' => $ration_user,
            ]);
        } else {
            if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {
                if (auth()->guard('shareholder')->user()->user_id == null) {


                    $reportsQuery = Report::query()->select([
                        DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                        DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                        DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                        DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                        DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                        DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                        DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                        DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                        DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                        DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                        DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                        DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                        DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                        DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                        DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                        DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                        DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                        DB::raw('SUM(COALESCE(total, 0)) as total'),
                        DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                    ])
                        ->join('customers', 'reports.customer_id', '=', 'customers.id')
                        ->join('rations', function ($join) {
                            $join->on('reports.customer_id', '=', 'rations.customer_id');
                            $join->on('reports.region_id', '=', 'rations.region_id');
                        })
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('rations.type', '=', TypeUser::User)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                        });


                } else {

                    $reportsQuery = Report::query()->select([
                        DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                        DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                        DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                        DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                        DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                        DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                        DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                        DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                        DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                        DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                        DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                        DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                        DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                        DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                        DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                        DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                        DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                        DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                        DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                        DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                        DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                        DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                        DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                        DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                        DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                        DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                        DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                        DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                        DB::raw('SUM(COALESCE(total, 0)) as total'),
                        DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                    ])
                        ->join('customers', 'reports.customer_id', '=', 'customers.id')
                        ->join('rations', function ($join) {
                            $join->on('reports.customer_id', '=', 'rations.customer_id');
                            $join->on('reports.region_id', '=', 'rations.region_id');
                        })
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                        })
                        ->where('rations.type', '=', TypeUser::User);

                }
            } else if (auth()->guard('machine')->user()) {

                $reportsQuery = Report::query()->select([
                    DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                    DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                    DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                    DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                    DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                    DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                    DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                    DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                    DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                    DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                    DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                    DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                    DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                    DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                    DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                    DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                    DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                    DB::raw('SUM(COALESCE(total, 0)) as total'),
                    DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')
                ])
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->join('rations', function ($join) {
                        $join->on('reports.customer_id', '=', 'rations.customer_id');
                        $join->on('reports.region_id', '=', 'rations.region_id');
                    })
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('rations.type', '=', TypeUser::User)
                    ->whereHas('customer', function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    });
            } else {


                $reportsQuery = Report::query()->select([
                    DB::raw('SUM(COALESCE(hai_so, 0)) as hai_so'),
                    DB::raw('SUM(COALESCE(hai_so, 0)*rations.hai_so_percent) as hai_so_percent'),
                    DB::raw('SUM(COALESCE(ba_so, 0)) as ba_so'),
                    DB::raw('SUM(COALESCE(ba_so, 0)*rations.ba_so_percent) as ba_so_percent'),
                    DB::raw('SUM(COALESCE(bon_so, 0)) as bon_so'),
                    DB::raw('SUM(COALESCE(bon_so, 0)*rations.bon_so_percent) as bon_so_percent'),
                    DB::raw('SUM(COALESCE(da_thang, 0)) as da_thang'),
                    DB::raw('SUM(COALESCE(da_thang, 0)*rations.da_thang_percent) as da_thang_percent'),
                    DB::raw('SUM(COALESCE(da_xien, 0)) as da_xien'),
                    DB::raw('SUM(COALESCE(da_xien, 0)*rations.da_xien_percent) as da_xien_percent'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)) as xiu_chu'),
                    DB::raw('SUM(COALESCE(xiu_chu, 0)*rations.xiu_chu_percent) as xiu_chu_percent'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)) as dau_duoi'),
                    DB::raw('SUM(COALESCE(dau_duoi, 0)*rations.dau_duoi_percent) as dau_duoi_percent'),
                    DB::raw('SUM(COALESCE(tong_tien_2so, 0)) as tong_tien_2so'),
                    DB::raw('SUM(COALESCE(tong_tien_3so, 0)) as tong_tien_3so'),
                    DB::raw('SUM(COALESCE(tong_tien_4so, 0)) as tong_tien_4so'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)) as hai_so_trung'),
                    DB::raw('SUM(COALESCE(hai_so_trung, 0)*rations.hai_so_ration) as hai_so_ration'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)) as ba_so_trung'),
                    DB::raw('SUM(COALESCE(ba_so_trung, 0)*rations.ba_so_ration) as ba_so_ration'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)) as bon_so_trung'),
                    DB::raw('SUM(COALESCE(bon_so_trung, 0)*rations.bon_so_ration) as bon_so_ration'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)) as da_thang_trung'),
                    DB::raw('SUM(COALESCE(da_thang_trung, 0)*rations.da_thang_ration) as da_thang_ration'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)) as da_xien_trung'),
                    DB::raw('SUM(COALESCE(da_xien_trung, 0)*rations.da_xien_ration) as da_xien_ration'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)) as dau_duoi_trung'),
                    DB::raw('SUM(COALESCE(dau_duoi_trung, 0)*rations.dau_duoi_ration) as dau_duoi_ration'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)) as xiu_chu_trung'),
                    DB::raw('SUM(COALESCE(xiu_chu_trung, 0)*rations.xiu_chu_ration) as xiu_chu_ration'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_lai, 0)) as tong_tien_2so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_lai, 0)) as tong_tien_3so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_lai, 0)) as tong_tien_4so_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung, 0)) as tong_tien_2so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung, 0)) as tong_tien_3so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung, 0)) as tong_tien_4so_trung'),
                    DB::raw('SUM(COALESCE(tong_tien_2so_trung_lai, 0)) as tong_tien_2so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_3so_trung_lai, 0)) as tong_tien_3so_trung_lai'),
                    DB::raw('SUM(COALESCE(tong_tien_4so_trung_lai, 0)) as tong_tien_4so_trung_lai'),
                    DB::raw('SUM(COALESCE(total_danh, 0)) as total_danh'),
                    DB::raw('SUM(COALESCE(total_danh_lai, 0)) as total_danh_lai'),
                    DB::raw('SUM(COALESCE(total_trung, 0)) as total_trung'),
                    DB::raw('SUM(COALESCE(total_trung_lai, 0)) as total_trung_lai'),
                    DB::raw('SUM(COALESCE(total, 0)) as total'),
                    DB::raw('SUM(COALESCE(total_lai, 0)) as total_lai')

                ])
                    ->join('customers', 'reports.customer_id', '=', 'customers.id')
                    ->join('rations', function ($join) {
                        $join->on('reports.customer_id', '=', 'rations.customer_id');
                        $join->on('reports.region_id', '=', 'rations.region_id');
                    })
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('rations.type', '=', TypeUser::User)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    });
            }
            if ($customer_id != 0) {
                $name = Customer::find($customer_id)->username;
                $reportsQuery->where('reports.customer_id', $customer_id);
            }

            $reports = $reportsQuery->get();


            return view('statistic.show', [
                'name' => $name,
                'date_start' => $date_start,
                'date_end' => $date_end,
                'customer_id' => $customer_id,
                'region_id' => $region,
                'report' => $reports[0],
                'ration_user' => []
            ]);
        }
    }

    public function api_report(Request $request)
    {
        if (auth()->guard('machine')->user()) {
            $data = Customer::query()
                ->select(['customers.id', 'customers.username', DB::raw('SUM(tickets.tien_xac) as tien_xac'),
                    DB::raw('SUM(tickets.tien_xac_2nd) as tien_xac_2nd'),
                    DB::raw('SUM(tickets.tien_trung) as tien_trung'),
                    DB::raw('SUM(tickets.tien_trung_lai_ve) as tien_trung_lai_ve'),
                    DB::raw('SUM(tickets.tien_trung_2nd) as tien_trung_2nd'),
                    DB::raw('SUM(tickets.tong_tien) as tong_tien'),
                    DB::raw('SUM(tickets.tong_tien_2nd) as tong_tien_2nd'),
                    DB::raw('COUNT(tickets.id) as count_ticket'),
                    DB::raw('MIN(tickets.date_check) as min_date_check'),
                    DB::raw('MAX(tickets.date_check) as max_date_check'),
                ])
                ->join('tickets', 'customers.id', '=', 'tickets.customer_id')
                ->where('tickets.created_at', '>=', Carbon::now()->subMonths(6)->format('Y-m-d'))
                ->where('machine_id', auth()->guard('machine')->user()->id)
                ->groupBy('customers.id')
                ->get();
            unset($data->tickets);
            return DataTables::of($data)
                ->editColumn('tien_xac', function ($value) {
                    return number_format($value->tien_xac, 0, '', '.');
                })->editColumn('tien_xac_2nd', function ($value) {
                    return number_format($value->tien_xac_2nd, 0, '', '.');
                })->editColumn('tien_trung', function ($value) {
                    return $value->tien_trung !== null ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return $value->tien_trung_2nd !== null ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    $money = $value->tong_tien !== null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    $money_lai = $value->tien_trung_lai_ve != null ? "(" . number_format($value->tien_trung_lai_ve, 0, '', '.') . ")" : "";

                    if ($value->laive == 1 || $money_lai < 0) {
                        return $money;
                    }
                    if ($money == "Đang chờ kết quả") {
                        return $money;
                    }
                    return '<div style="color: red">' . $money . '</div> <div style="color: red">' . $money_lai . '</div>';
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tong_tien_2nd !== null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    if ($value->count_ticket == 0) {
                        return "...";
                    }
                    return date('d/m/Y', strtotime($value->min_date_check)) . "-" . date('d/m/Y', strtotime($value->max_date_check));
//                return date('d/m/Y', strtotime("-2 months", strtotime(now()))) . " - " . date('d/m/Y');
                })
                ->addColumn('action', function ($user) {
                    return '<a href="" class="btn btn-xs btn-warning warning-edit"><i class="fa fa-eye"></i> </a> <a href="javascript:void(0)" data-id="" class="btn btn-xs btn-danger btn-delete"><i class="fa fa-times"></i> </a>';
                })->rawColumns(['tong_tien', 'action'])
                ->make(true);
        }
        $machine_id = $request->machine_id;
        if ($machine_id) {

            return DataTables::of(Customer::query()
                ->with('tickets', function ($q) {
                    $q->where('created_at', '>=', Carbon::now()->subMonths(2)->format('Y-m-d'));
                })
                ->where('machine_id', $request->machine_id)
                ->get())
                ->editColumn('tien_xac', function ($value) {
                    return number_format($value->tickets->sum('tien_xac'), 0, '', '.');
                })->editColumn('tien_xac_2nd', function ($value) {
                    return number_format($value->tickets->sum('tien_xac_2nd'), 0, '', '.');
                })->editColumn('tien_trung', function ($value) {
                    return $value->tickets->sum('tien_trung') !== null ? number_format($value->tickets->sum('tien_trung'), 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return $value->tickets->sum('tien_trung_2nd') !== null ? number_format($value->tickets->sum('tien_trung_2nd'), 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    $money = $value->tickets->sum('tong_tien') !== null ? number_format($value->tickets->sum('tong_tien'), 0, '', '.') : "Đang chờ kết quả";
                    $money_lai = $value->tickets->sum('tien_trung_lai_ve') != null ? "(" . number_format($value->tickets->sum('tien_trung_lai_ve'), 0, '', '.') . ")" : "";

                    if ($value->laive == 1 || $money_lai < 0) {
                        return $money;
                    }
                    if ($money == "Đang chờ kết quả") {
                        return $money;
                    }
                    return '<div style="color: red">' . $money . '</div> <div style="color: red">' . $money_lai . '</div>';
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tickets->sum('tong_tien_2nd') !== null ? number_format($value->tickets->sum('tong_tien_2nd'), 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    if ($value->tickets->count() == 0) {
                        return "...";
                    }
                    return date('d/m/Y', strtotime($value->tickets->min('date_check'))) . "-" . date('d/m/Y', strtotime($value->tickets->max('date_check')));
//                return date('d/m/Y', strtotime("-2 months", strtotime(now()))) . " - " . date('d/m/Y');
                })
                ->addColumn('action', function ($user) {
                    return '<a href="" class="btn btn-xs btn-warning warning-edit"><i class="fa fa-eye"></i> </a> <a href="javascript:void(0)" data-id="" class="btn btn-xs btn-danger btn-delete"><i class="fa fa-times"></i> </a>';
                })->rawColumns(['tong_tien', 'action'])
                ->make(true);

        }
        return DataTables::of(Machine::query()
            ->with('customers.tickets', function ($query) {
                $query->select('customer_id', DB::raw('SUM(tien_xac) as tien_xac')
                    , DB::raw('SUM(tien_xac_2nd) as tien_xac_2nd')
                    , DB::raw('SUM(tien_trung) as tien_trung')
                    , DB::raw('SUM(tien_trung_2nd) as tien_trung_2nd')
                    , DB::raw('SUM(tong_tien) as tong_tien')
                    , DB::raw('SUM(tong_tien_2nd) as tong_tien_2nd')
                )
                    ->groupBy('customer_id');
            })
            ->where('user_id', $this->getListChildrenIdOfAdmin())
            ->get())
            ->editColumn('tien_xac', function ($value) {
                $totalTienXac = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_xac');
                });
                return number_format($totalTienXac, 0, '', '.');
            })
            ->editColumn('tien_xac_2nd', function ($value) {
                $totalTienXac2nd = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_xac_2nd');
                });
                return number_format($totalTienXac2nd, 0, '', '.');
            })
            ->editColumn('tien_trung', function ($value) {
                $totalTienTrung = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_trung');
                });
                return $totalTienTrung !== null ? number_format($totalTienTrung, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_trung_2nd', function ($value) {
                $totalTienTrung2nd = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_trung_2nd');
                });
                return $totalTienTrung2nd !== null ? number_format($totalTienTrung2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tong_tien', function ($value) {
                $totalTongTien = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_trung');
                });
                $totalTongTienLaiVe = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tien_trung_lai_ve');
                });
                $money = $totalTongTien !== null ? number_format($totalTongTien, 0, '', '.') : "Đang chờ kết quả";
                $money_lai = $totalTongTienLaiVe != null ? "(" . number_format($totalTongTienLaiVe, 0, '', '.') . ")" : "";

                if ($value->laive == 1 || $money_lai < 0) {
                    return $money;
                }
                if ($money == "Đang chờ kết quả") {
                    return $money;
                }
                return '<div style="color: red">' . $money . '</div> <div style="color: red">' . $money_lai . '</div>';
            })
            ->editColumn('tong_tien_2nd', function ($value) {
                $totalTongTien2nd = $value->customers->sum(function ($customer) {
                    return $customer->tickets->sum('tong_tien_2nd');
                });
                return $totalTongTien2nd !== null ? number_format($totalTongTien2nd, 0, '', '.') : "Đang chờ kết quả";
            })->editColumn('created_at', function ($value) {
                $count = $value->customers->count(function ($customer) {
                    return $customer->tickets->count();
                });

                if ($count == 0) {
                    return "...";
                }
                return "";
//                return date('d/m/Y', strtotime($value->customers->min('tickets.date_check'))) . "-" . date('d/m/Y', strtotime($value->customers->max('tickets.date_check')));
//                return date('d/m/Y', strtotime("-2 months", strtotime(now()))) . " - " . date('d/m/Y');
            })
            ->addColumn('action', function ($user) {
                return '<a href="" class="btn btn-xs btn-warning warning-edit"><i class="fa fa-eye"></i> </a> <a href="javascript:void(0)" data-id="" class="btn btn-xs btn-danger btn-delete"><i class="fa fa-times"></i> </a>';
            })->rawColumns(['tong_tien', 'action'])
            ->make(true);

    }

    public function api_report_show($customer_id)
    {

        $cus = Customer::query()
            ->with('rations')
            ->where('id', $customer_id)->get()->first();

        $tickets = Ticket::query()
            ->where('customer_id', $cus->id)
            ->where('tien_trung', '=', null)
            ->get();
        $reports = DB::table('reports')
            ->where('customer_id', $cus->id)
            ->get();

        return DataTables::of(DB::table('tickets')
            ->select(['date_check', 'customer_id',
                DB::raw('sum(tien_trung_lai_ve) as tien_trung_lai_ve'),
                DB::raw('sum(tien_xac) as tien_xac'),
                DB::raw('sum(tien_xac_2nd) as tien_xac_2nd'),
                DB::raw('sum(tien_trung) as tien_trung'),
                DB::raw('sum(tien_trung_2nd) as tien_trung_2nd'),
                DB::raw('sum(tong_tien) as tong_tien'),
                DB::raw('sum(tong_tien_2nd) as tong_tien_2nd'),
                DB::raw('sum(tien_trung_lai_ve) as tien_trung_lai_ve')])
            ->where('customer_id', $customer_id)
            ->where('created_at', '>=', Carbon::now()->subMonths(2)->format('Y-m-d'))
            ->groupBy('date_check', 'customer_id')
            ->orderByDesc('date_check')
            ->get())
            ->editColumn('tien_xac', function ($value) {

                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_xac_2nd', function ($value) {
                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_trung', function ($value) {
                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_trung_2nd', function ($value) {
                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tong_tien_2nd', function ($value) {
                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('date_check', function ($value) {
                return [
                    'display' => date('d-m-Y', strtotime($value->date_check)),
                    'timestamp' => strtotime($value->date_check)
                ];

            })
            ->editColumn('tong_tien', function ($value) use ($cus, $tickets, $reports) {

                $money = $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                $money_total_lai = $reports
                    ->where('date_check', $value->date_check)
                    ->sum('total_lai');

                $money_lai = $money_total_lai != null ? "(" .
                    number_format($money_total_lai * $cus->laive, 0, '', '.') . ")" : "";


                $money_lai_3_areas = $money_total_lai != null
                    ? "(" . number_format($money_total_lai, 0, '', '.') . ")" : "";

                $count = $tickets->where('date_check', $value->date_check)
                    ->count();

                $checkLaiVe3Areas = $cus->rations->where('region_id', 1)->where('type', 0)->first()->lai_ve == 1
                    && $cus->rations->where('region_id', 2)->where('type', 0)->first()->lai_ve == 1
                    && $cus->rations->where('region_id', 3)->where('type', 0)->first()->lai_ve == 1;


                if ($cus->laive == 1 && $checkLaiVe3Areas) {
                    if ($count > 0) {
                        return '<div style="color: red">' . $money . '</div> <div>(' . $count . ' tin chưa tính tiền)</div>';
                    }
                    return $money;
                }
                if (is_numeric($money)) {
                    $strShow = '<div style="color: red">' . $money . '</div>';
                    if (!$checkLaiVe3Areas) {
                        if ($money_total_lai > 0) {
                            $strShow .= '<div >' . $money_lai_3_areas . '<div>';
                        } else {
                            $strShow .= ' <div style="color: red">' . $money_lai_3_areas . '<div>';
                        }
                    }
                    if ($cus->laive != 1 && $money_total_lai < 0) {

                        $strShow .= '<div style="color: red">' . $money_lai . '<div>';

                    }
                    if ($count > 0) {
                        $strShow .= '<div>(' . $count . ' tin chưa tính tiền)</div>';
                    }
                } else {
                    $strShow = '<div >' . $money . '</div>';
                    if (!$checkLaiVe3Areas) {
                        if ($money_total_lai > 0) {
                            $strShow .= '<div >' . $money_lai_3_areas . '<div>';
                        } else {
                            $strShow .= '<div style="color: red">' . $money_lai_3_areas . '<div>';
                        }
                    }
                    if ($cus->laive != 1 && $money_total_lai < 0) {
                        $strShow .= '<div >' . $money_lai . '<div>';
                    }
                    if ($count > 0) {
                        $strShow .= '<div>(' . $count . ' tin chưa tính tiền)</div>';
                    }
                }
                return $strShow;
            })
            ->addColumn('action', function ($value) {
                return ' <a  data-date="' . $value->date_check . '" class="btn btn-reload btn-edit-reload btn-icon-only"><i class="fa fa-redo"></i> </a>
                        <a href="javascript:void(0)" id="' . $value->date_check . '" class="btn btn-xs btn-danger btn-delete delete"><i class="fa fa-trash-alt"></i> </a>';

            })->rawColumns(['tong_tien', 'action', 'date_check'])
            ->make(true);
    }

    public function api_report_details($customer_id, Request $request)
    {

        if ($request->type_view && $request->type_view == 2) {
            return DataTables::of(DB::table('tickets')
                ->where('customer_id', $customer_id)
                ->where('date_check', $request->date)
                ->where('region_id', $request->region_id)
                ->get())
                ->editColumn('tien_xac', function ($value) {

                    return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_xac_2nd', function ($value) {

                    return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung', function ($value) {

                    return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    return date("H:i:s", strtotime($value->created_at));
                })
                ->editColumn('message_show', function ($value) {
                    return $value->detail_message;
                })
                ->addColumn('action', function ($value) {
                    if (auth()->guard('machine')->user()) {
                        return '<div class="action-center"> <a href ="javascript:void(0)" class="btn btn-reload btn-edit-reload btn-icon-only" ><i class="fa fa-redo" ></i > </a ><a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-warning warning-edit btn-update" ><i class="fa fa-edit" ></i > </a > <a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-danger btn-delete delete" ><i class="fa fa-trash-alt" ></i > </a > </div> ';
                    }
                    return '';
                })
                ->make(true);
        } else if ($request->type_view && $request->type_view == 3) {
            return DataTables::of(Ticket::query()
                ->where('customer_id', $customer_id)
                ->where('date_check', $request->date)
                ->where('region_id', $request->region_id)
                ->withWinningNumber()
                ->get())
                ->editColumn('tien_xac', function ($value) {

                    return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_xac_2nd', function ($value) {

                    return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung', function ($value) {

                    return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    return date("H:i:s", strtotime($value->created_at));
                })
                ->editColumn('message_show', function ($value) {
                    return $value->message_show != null ? $value->message_show : $value->message;
                })
                ->addColumn('action', function ($value) {
                    if (auth()->guard('machine')->user()) {
                        return '<div class="action-center"> <a href ="javascript:void(0)" class="btn btn-reload btn-edit-reload btn-icon-only" ><i class="fa fa-redo" ></i > </a ><a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-warning warning-edit btn-update" ><i class="fa fa-edit" ></i > </a > <a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-danger btn-delete delete" ><i class="fa fa-trash-alt" ></i > </a > </div> ';
                    }
                    return '';
                })
                ->make(true);
        }
        return DataTables::of(DB::table('tickets')
            ->where('customer_id', $customer_id)
            ->where('date_check', $request->date)
            ->where('region_id', $request->region_id)
            ->get())
            ->editColumn('tien_xac', function ($value) {

                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_xac_2nd', function ($value) {

                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_trung', function ($value) {

                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tien_trung_2nd', function ($value) {
                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tong_tien', function ($value) {
                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
            })
            ->editColumn('tong_tien_2nd', function ($value) {
                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
            })->editColumn('created_at', function ($value) {
                return date("H:i:s", strtotime($value->created_at));
            })->editColumn('message_show', function ($value) {
                return $value->message_show != null ? $value->message_show : $value->message;
            })
            ->addColumn('action', function ($value) {
                if (auth()->guard('machine')->user()) {
                    return '<div class="action-center"> <a href ="javascript:void(0)" class="btn btn-reload btn-edit-reload btn-icon-only" ><i class="fa fa-redo" ></i > </a ><a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-warning warning-edit btn-update" ><i class="fa fa-edit" ></i > </a > <a href = "javascript:void(0)" id = "' . $value->id . '" class="btn btn-xs btn-danger btn-delete delete" ><i class="fa fa-trash-alt" ></i > </a > </div>';
                }
                return '';
            })
            ->make(true);
    }

    public function statistic_detail_api($customer_id, Request $request)
    {
        $date_start = date('Y-m-d', strtotime($request->date_start));
        $date_end = date('Y-m-d', strtotime($request->date_end));
        $region_id = $request->region_id;
        if ($region_id != 0) {
            if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {
                if (auth()->guard('shareholder')->user()->user_id == null) {
                    if ($request->type_view && $request->type_view == 2) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->where('region_id', $request->region_id)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                            })
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->detail_message;
                            })
                            ->make(true);
                    } elseif ($request->type_view && $request->type_view == 3) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->where('region_id', $request->region_id)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                            })
                            ->withWinningNumber()
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->message_show != null ? $value->message_show : $value->message;
                            })
                            ->make(true);
                    }
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('region_id', $request->region_id)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                        })
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                } else {
                    if ($request->type_view && $request->type_view == 2) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->where('region_id', $request->region_id)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                            })
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->detail_message;
                            })
                            ->make(true);
                    } else if ($request->type_view && $request->type_view == 3) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->where('region_id', $request->region_id)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                            })
                            ->withWinningNumber()
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->message_show != null ? $value->message_show : $value->message;
                            })
                            ->make(true);
                    }
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('region_id', $request->region_id)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                        })
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                }
            } else if (auth()->guard('machine')->user()) {
                if ($request->type_view && $request->type_view == 2) {
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('region_id', $request->region_id)
                        ->whereHas('customer', function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        })
                        ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        }])
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })
                        ->editColumn('message_show', function ($value) {
                            return $value->detail_message;
                        })
                        ->make(true);
                } elseif ($request->type_view && $request->type_view == 3) {
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->where('region_id', $request->region_id)
                        ->whereHas('customer', function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        })
                        ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        }])
                        ->withWinningNumber()
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })
                        ->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                }
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                    ->where('customer_id', $customer_id)
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('region_id', $request->region_id)
                    ->whereHas('customer', function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    })
                    ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    }])
                    ->get())
                    ->editColumn('tien_xac', function ($value) {
                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {
                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {
                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->message_show != null ? $value->message_show : $value->message;
                    })
                    ->make(true);
            }
            if ($request->type_view && $request->type_view == 2) {
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->where('customer_id', $customer_id)
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('region_id', $request->region_id)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    })
                    ->with('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                    })
                    ->get())
                    ->editColumn('tien_xac', function ($value) {

                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {

                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {

                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->detail_message;
                    })
                    ->make(true);
            } else if ($request->type_view && $request->type_view == 3) {
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->where('customer_id', $customer_id)
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->where('region_id', $request->region_id)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    })
                    ->with('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                    })
                    ->withWinningNumber()
                    ->get())
                    ->editColumn('tien_xac', function ($value) {

                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {

                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {

                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->message_show != null ? $value->message_show : $value->message;
                    })
                    ->make(true);
            }
            return DataTables::of(Ticket::query()
                ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                ->where('customer_id', $customer_id)
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->where('region_id', $request->region_id)
                ->whereHas('customer.machine', function ($query) {
                    $query->where('user_id', $this->getListChildrenIdOfAdmin());
                })
                ->with('customer.shareholderCustomers.shareholder', function ($query) {
                    $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                })
                ->get())
                ->editColumn('tien_xac', function ($value) {

                    return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_xac_2nd', function ($value) {

                    return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung', function ($value) {

                    return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    return date("d-m H:i:s", strtotime($value->created_at));
                })->editColumn('message_show', function ($value) {
                    return $value->message_show != null ? $value->message_show : $value->message;
                })
                ->make(true);
        } else {
            if (auth()->guard('shareholder')->check() && !auth()->guard('web')->check()) {
                if (auth()->guard('shareholder')->user()->user_id == null) {
                    if ($request->type_view && $request->type_view == 2) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                            })
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->detail_message;
                            })
                            ->make(true);
                    } elseif ($request->type_view && $request->type_view == 3) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                            })
                            ->withWinningNumber()
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->message_show != null ? $value->message_show : $value->message;
                            })
                            ->make(true);
                    }
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('machine_id', auth()->guard('shareholder')->user()->machine_id);
                        })
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                } else {
                    if ($request->type_view && $request->type_view == 2) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                            })
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->detail_message;
                            })
                            ->make(true);
                    } else if ($request->type_view && $request->type_view == 3) {
                        return DataTables::of(Ticket::query()
                            ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                            ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                            ->where('customer_id', $customer_id)
                            ->whereDate('date_check', '>=', $date_start)
                            ->whereDate('date_check', '<=', $date_end)
                            ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                                $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                            })
                            ->withWinningNumber()
                            ->get())
                            ->editColumn('tien_xac', function ($value) {

                                return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_xac_2nd', function ($value) {

                                return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung', function ($value) {

                                return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tien_trung_2nd', function ($value) {
                                return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien', function ($value) {
                                return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                            })
                            ->editColumn('tong_tien_2nd', function ($value) {
                                return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                            })->editColumn('created_at', function ($value) {
                                return date("d-m H:i:s", strtotime($value->created_at));
                            })
                            ->editColumn('message_show', function ($value) {
                                return $value->message_show != null ? $value->message_show : $value->message;
                            })
                            ->make(true);
                    }
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer.shareholderCustomers.shareholder', function ($query) {
                            $query->where('user_id', auth()->guard('shareholder')->user()->user_id);
                        })
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                }
            } else if (auth()->guard('machine')->user()) {
                if ($request->type_view && $request->type_view == 2) {
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer', function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        })
                        ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        }])
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })
                        ->editColumn('message_show', function ($value) {
                            return $value->detail_message;
                        })
                        ->make(true);
                } elseif ($request->type_view && $request->type_view == 3) {
                    return DataTables::of(Ticket::query()
                        ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                        ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                        ->where('customer_id', $customer_id)
                        ->whereDate('date_check', '>=', $date_start)
                        ->whereDate('date_check', '<=', $date_end)
                        ->whereHas('customer', function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        })
                        ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                            $query->where('machine_id', auth()->guard('machine')->user()->id);
                        }])
                        ->withWinningNumber()
                        ->get())
                        ->editColumn('tien_xac', function ($value) {

                            return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_xac_2nd', function ($value) {

                            return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung', function ($value) {

                            return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tien_trung_2nd', function ($value) {
                            return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien', function ($value) {
                            return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                        })
                        ->editColumn('tong_tien_2nd', function ($value) {
                            return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                        })->editColumn('created_at', function ($value) {
                            return date("d-m H:i:s", strtotime($value->created_at));
                        })
                        ->editColumn('message_show', function ($value) {
                            return $value->message_show != null ? $value->message_show : $value->message;
                        })
                        ->make(true);
                }
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                    ->where('customer_id', $customer_id)
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer', function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    })
                    ->with(['customer.shareholderCustomers.shareholder' => function ($query) {
                        $query->where('machine_id', auth()->guard('machine')->user()->id);
                    }])
                    ->get())
                    ->editColumn('tien_xac', function ($value) {
                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {
                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {
                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->message_show != null ? $value->message_show : $value->message;
                    })
                    ->make(true);
            }
            if ($request->type_view && $request->type_view == 2) {
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'detail_message')
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->where('customer_id', $customer_id)
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    })
                    ->with('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                    })
                    ->get())
                    ->editColumn('tien_xac', function ($value) {

                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {

                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {

                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->detail_message;
                    })
                    ->make(true);
            } else if ($request->type_view && $request->type_view == 3) {
                return DataTables::of(Ticket::query()
                    ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                    ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                    ->where('customer_id', $customer_id)
                    ->whereDate('date_check', '>=', $date_start)
                    ->whereDate('date_check', '<=', $date_end)
                    ->whereHas('customer.machine', function ($query) {
                        $query->where('user_id', $this->getListChildrenIdOfAdmin());
                    })
                    ->with('customer.shareholderCustomers.shareholder', function ($query) {
                        $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                    })
                    ->withWinningNumber()
                    ->get())
                    ->editColumn('tien_xac', function ($value) {

                        return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_xac_2nd', function ($value) {

                        return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung', function ($value) {

                        return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tien_trung_2nd', function ($value) {
                        return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien', function ($value) {
                        return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                    })
                    ->editColumn('tong_tien_2nd', function ($value) {
                        return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                    })->editColumn('created_at', function ($value) {
                        return date("d-m H:i:s", strtotime($value->created_at));
                    })
                    ->editColumn('message_show', function ($value) {
                        return $value->message_show != null ? $value->message_show : $value->message;
                    })
                    ->make(true);
            }
            return DataTables::of(Ticket::query()
                ->select('tien_xac', 'tien_xac_2nd', 'tien_trung', 'tien_trung_2nd', 'tien_trung_2nd', 'tong_tien', 'tong_tien_2nd', 'tickets.created_at', 'message')
                ->join('customers', 'tickets.customer_id', '=', 'customers.id')
                ->where('customer_id', $customer_id)
                ->whereDate('date_check', '>=', $date_start)
                ->whereDate('date_check', '<=', $date_end)
                ->whereHas('customer.machine', function ($query) {
                    $query->where('user_id', $this->getListChildrenIdOfAdmin());
                })
                ->with('customer.shareholderCustomers.shareholder', function ($query) {
                    $query->where('user_id', auth()->user()->id ?? auth()->guard('accountant')->user()->user_id);
                })
                ->get())
                ->editColumn('tien_xac', function ($value) {

                    return $value->tien_xac != null ? number_format($value->tien_xac, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_xac_2nd', function ($value) {

                    return $value->tien_xac_2nd != null ? number_format($value->tien_xac_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung', function ($value) {

                    return isset($value->tien_trung) ? number_format($value->tien_trung, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tien_trung_2nd', function ($value) {
                    return isset($value->tien_trung_2nd) ? number_format($value->tien_trung_2nd, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien', function ($value) {
                    return $value->tong_tien != null ? number_format($value->tong_tien, 0, '', '.') : "Đang chờ kết quả";
                })
                ->editColumn('tong_tien_2nd', function ($value) {
                    return $value->tong_tien_2nd != null ? number_format($value->tong_tien_2nd, 0, '', '.') : "Đang chờ kết quả";
                })->editColumn('created_at', function ($value) {
                    return date("d-m H:i:s", strtotime($value->created_at));
                })->editColumn('message_show', function ($value) {
                    return $value->message_show != null ? $value->message_show : $value->message;
                })
                ->make(true);
        }
    }

    public function api_show_report_detail(Request $request)
    {

        $result = Ticket::find($request->id);
        $html = '';

        if ($result->hai_so) {
            $html .= '
             <tr>
                <td>2SO</td>
                <td>' . number_format($result->hai_so, 1, '.', ',') . '</td>
                <td>' . (isset($result->hai_so_trung) ? number_format($result->hai_so_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->ba_so) {
            $html .= '
            <tr>
                <td>3SO</td>
                <td>' . number_format($result->ba_so, 1, '.', ',') . '</td>
                <td>' . (isset($result->ba_so_trung) ? number_format($result->ba_so_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->bon_so) {
            $html .= '
            <tr>
                <td>4SO</td>
                <td>' . number_format($result->bon_so, 1, '.', ',') . '</td>
                <td>' . (isset($result->bon_so_trung) ? number_format($result->bon_so_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->da_thang) {
            $html .= '
            <tr>
                <td>DATHANG</td>
                <td>' . number_format($result->da_thang, 1, '.', ',') . '</td>
                <td>' . (isset($result->da_thang_trung) ? number_format($result->da_thang_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->da_xien) {
            $html .= '
            <tr>
                <td>DAXIEN</td>
                <td>' . number_format($result->da_xien, 1, '.', ',') . '</td>
                <td>' . (isset($result->da_xien_trung) ? number_format($result->da_xien_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->dau_duoi) {
            $html .= '
            <tr>
                <td>DD</td>
                <td>' . number_format($result->dau_duoi, 1, '.', ',') . '</td>
                <td>' . (isset($result->dau_duoi_trung) ? number_format($result->dau_duoi_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->xiu_chu) {
            $html .= '
            <tr>
                <td>XC</td>
                <td>' . number_format($result->xiu_chu, 1, '.', ',') . '</td>
                <td>' . (isset($result->xiu_chu_trung) ? number_format($result->xiu_chu_trung, 1, '.', ',') : '') . '</td>
                <td></td>
            </tr>';
        }

        if ($result->tong_tien_2so) {
            $str_2_so = '';
            if (isset($result->tong_tien_2so_trung)) {
                $str_2_so .= number_format($result->tong_tien_2so_trung, 1, '.', ',');
                $str_2_so .= isset($result->tong_tien_2so_trung_lai) ? ' (' . number_format($result->tong_tien_2so_trung_lai, 1, '.', ',') . ')' : '';
            }

            $html .= '
            <tr>
                <td>Tổng 2 số</td>
                <td>' . number_format($result->tong_tien_2so, 1, '.', ',')
                . ' (' . number_format($result->tong_tien_2so_lai, 1, '.', ',') . ')</td>
                <td>' . $str_2_so . '</td>
                <td>' . number_format(($result->tong_tien_2so_trung_lai ?? 0) - ($result->tong_tien_2so_lai ?? 0), 1, '.', ',') . '</td>
            </tr>';
        }

        if ($result->tong_tien_3so) {
            $str_2_so = '';
            if (isset($result->tong_tien_3so_trung)) {
                $str_2_so .= number_format($result->tong_tien_3so_trung, 1, '.', ',');
                $str_2_so .= isset($result->tong_tien_3so_trung_lai) ? ' (' . number_format($result->tong_tien_3so_trung_lai, 1, '.', ',') . ')' : '';
            }

            $html .= '
            <tr>
                <td>Tổng 3 số</td>
                <td>' . number_format($result->tong_tien_3so, 1, '.', ',')
                . ' (' . number_format($result->tong_tien_3so_lai, 1, '.', ',') . ')</td>
                <td>' . $str_2_so . '</td>
                <td>' . number_format(($result->tong_tien_3so_trung_lai ?? 0) - ($result->tong_tien_3so_lai ?? 0), 1, '.', ',') . '</td>
            </tr>';
        }

        if ($result->tong_tien_4so) {
            $str_2_so = '';
            if (isset($result->tong_tien_4so_trung)) {
                $str_2_so .= number_format($result->tong_tien_4so_trung, 1, '.', ',');
                $str_2_so .= isset($result->tong_tien_4so_trung_lai) ? ' (' . number_format($result->tong_tien_4so_trung_lai, 1, '.', ',') . ')' : '';
            }

            $html .= '
            <tr>
                <td>Tổng 4 số</td>
                <td>' . number_format($result->tong_tien_4so, 1, '.', ',')
                . ' (' . number_format($result->tong_tien_4so_lai, 1, '.', ',') . ')</td>
                <td>' . $str_2_so . '</td>
                <td>' . number_format(($result->tong_tien_4so_trung_lai ?? 0) - ($result->tong_tien_4so_lai ?? 0), 1, '.', ',') . '</td>
            </tr>';
        }


        $html .= '
     <tr>
        <td><b>Thu chi</b></td>
        <td class="text-red">';

        if (isset($result->total_danh) && isset($result->total_danh_lai)) {
            $html .= number_format($result->total_danh, 0, '.', ',')
                . ' (' . number_format($result->total_danh_lai, 0, '.', ',') . ')';
        }

        $html .= '</td>
        <td class="text-primary">';

        if (isset($result->total_trung) && isset($result->total_trung_lai)) {
            $html .= number_format($result->total_trung, 1, '.', ',')
                . ' (' . number_format($result->total_trung_lai, 1, '.', ',') . ')';
        }

        $html .= '</td>
        <td class="text-danger">';

        if (isset($result->total) && isset($result->total_lai)) {
            $html .= number_format($result->total, 0, '.', ',')
                . ' (' . number_format($result->total_lai, 0, '.', ',') . ')';
        }

        $html .= '</td>
    </tr>';

        return $html;
    }

    public function api_show_report_delete(Request $request)
    {
        $report = Report::query()
            ->where('date_check', $request->date_check)
            ->where('customer_id', $request->customer)->get();

        $ticket = Ticket::query()
            ->where('date_check', $request->date_check)
            ->where('customer_id', $request->customer)->get();

        foreach ($report as $ticketItem) {
            $ticketItem->delete(); // Xóa bản ghi cha
        }

        foreach ($ticket as $ticketItem) {
            // Xóa các bản ghi con
            $ticketItem->histories()->delete();
            $ticketItem->reportDetails()->delete();
            // Xóa bản ghi cha
            $ticketItem->delete();
        }
    }

}
