<?php

namespace App\Http\Controllers;


use App\Models\ActivityLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ActivityLogController extends \Illuminate\Routing\Controller
{
    public function index(Request $request)
    {

        if (Auth::guard('machine')->check()) {
            return view('activity.index', [
                'activities' => ActivityLog::query()->where('causer_type', 'machine')
                    ->where('causer_id',auth('machine')->user()->id)->latest()->get(),
            ]);
        } else {
            return view('activity.index', [
                'activities' => ActivityLog::query()->latest()->get(),
            ]);
        }
    }
}

