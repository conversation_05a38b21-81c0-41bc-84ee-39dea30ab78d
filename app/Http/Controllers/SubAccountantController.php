<?php

namespace App\Http\Controllers;

use App\Models\Accountant;
use Illuminate\Http\Request;

class SubAccountantController extends Controller
{
    public function index()
    {
        $accountants = Accountant::query()->where('parent_id', auth()->id())->get();
        $username = 'sub_' . auth()->user()->username . $accountants->count();
        return view('sub-accountants.index', compact('accountants', 'username'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'username' => 'required|unique:accountants',
            'name' => 'required',
            'password' => 'required'
        ]);

        Accountant::create([
            'username' => $validated['username'],
            'name' => $validated['name'],
            'password' => $validated['password'],
            'parent_id' => auth()->id(),
            'user_id' => auth()->user()->user_id
        ]);

        return redirect()->route('sub-accountants.index');
    }

    public function update(Request $request, $id)
    {

        $validated = $request->validate([
            'name' => 'required',
        ]);
        $accountant = Accountant::query()->findOrFail($id);
        $accountant->update([
            'name' => $validated['name'],
            'password' => $request->password ? $request->password  : $accountant->password,
        ]);

        return redirect()->route('sub-accountants.index');
    }

    public function destroy($id)
    {
        Accountant::query()->findOrFail($id)->delete();

        return redirect()->route('sub-accountants.index');
    }
}
