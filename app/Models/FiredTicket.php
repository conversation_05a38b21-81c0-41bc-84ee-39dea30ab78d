<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FiredTicket extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function firedTicketDetails()
    {
        return $this->hasMany(FiredTicketDetail::class);
    }

    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($parentModel) {
            $parentModel->firedTicketDetails()->delete();
        });
    }
}
