<?php

namespace App\Models;

use App\Enums\FiredTypeEnum;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Bot extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'block_type' => 'array',
    ];

    public function getBlockTypeLabels(): string
    {
        if ($this->block_type == null) return '';
        return implode('<br/> ', array_map(fn($value) => FiredTypeEnum::getLabel((int)$value), $this->block_type));
    }
}
