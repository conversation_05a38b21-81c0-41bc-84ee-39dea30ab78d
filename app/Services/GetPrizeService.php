<?php

namespace App\Services;

use App\Enums\AreaEnum;
use App\Enums\TypeFight;
use App\Enums\TypeUser;
use App\Models\Area;
use App\Models\Ration;
use Illuminate\Support\Facades\Log;

class GetPrizeService extends BaseService
{


    function findPositions($string, $array)
    {

        $positions = array();

        foreach ($array as $item) {
            $offset = 0;
            $pattern = "/{$item}\s{,}[^a-zA-z]/";

            while (preg_match($pattern, $string, $matches, PREG_OFFSET_CAPTURE, $offset)) {
//                echo $item . " ";
                $positions[] = strpos($string, $matches[0][0]);
                $offset = strpos($string, $matches[0][0]) + strlen($item);

            }
        }

        if (sizeof($positions) >= 2) {
            sort($positions);

        }
        if (sizeof($positions) >= 2) {
            if ($positions[0] == $positions[1]) {
                return [0];
            }

            return $positions;
        } else {


            return [0];
        }

    }

    public function getALLSide($array, $dai)
    {

        if ($array == null) {
            $array = ['hn'];
        }

        $array_dai_mb = ['mb', 'hn', 'hanoi', 'ha noi', 'mien bac'];
        $array_dai_mn = ['dc', 'dp', 'daichanh', 'dai chanh', 'daiphu', 'dai phu', 'ag', 'angiang', 'an giang', 'bl', 'blieu', 'baclieu', 'bac lieu', 'btre', 'bt', 'bentre', 'ben tre', 'bd', 'bduong', 'sb', 'binhduong', 'binh duong', 'bp', 'bphuoc', 'binhphuoc', 'binh phuoc', 'cm', 'camau', 'ca mau', 'ct', 'ctho', 'cantho', 'can tho',
            'dl', 'dlat', 'dalat', 'da lat', 'dn', 'dnai', 'dongnai', 'dn', 'dong nai', 'dt', 'dthap', 'dongthap', 'dong thap', 'hg', 'hgiang', 'haugiang', 'kg', 'kgiang', 'kiengiang', 'kien giang', 'la', 'lan', 'longan', 'long an', 'st', 'strang', 'soctrang', 'soc trang', 'tn', 'tninh', 'tayninh', 'tay ninh', 'tg', 'tgiang', 'tien giang', 'tiengiang', 'tp', 'hcm', 'tv', 'tvinh', 'travinh', 'tra vinh', 'vl', 'vlong', 'vinhlong', 'vinh long', 'vt', 'vtau', 'vungtau', 'vung tau', 'vung tau'];

        $array_dai_mt = ['dc', 'dp', 'daichanh', 'dai chanh', 'daiphu', 'dai phu', 'bdinh', 'binhdinh', 'binh dinh', 'bd', 'dlak', 'daklak', 'dak lak', 'dlac', 'daclac', 'dac lac', 'dl', 'dnang', 'danang', 'da nang', 'dk', 'dknong', 'daknong', 'dak nong', 'dn', 'glai', 'gialai', 'gia lai', 'gl', 'khoa', 'khanhhoa', 'khanh hoa', 'kh', 'ktum', 'kontum', 'kon tum', 'kt', 'nthuan', 'ninhthuan', 'ninh thuan', 'nt', 'pyen', 'phyen', 'phuyen', 'phu yen', 'py',
            'qbinh', 'quangbinh', 'quang binh', 'qb', 'qngai', 'quangngai', 'quang ngai', 'qg', 'qn', 'qnam', 'quangnam', 'quang nam', 'qtri', 'quangtri', 'quang tri', 'qt', 'thuathienhue', 'tt', 'thua thien hue', 'th', 'hue'];

        $array_dai_mn_group = ['2d', '3d', '4d'];

        $intersect_mn = [];
        $intersect_mt = [];

        $intersect_mb = array_intersect($array, $array_dai_mb);
        if ($dai == 1) {
            $key = array_search('dl', $array_dai_mt);

            unset($array_dai_mt[$key]);
            $intersect_mn = array_intersect($array, $array_dai_mn);
        }
        if ($dai == 3) {
            $key = array_search('dl', $array_dai_mn);
            unset($array_dai_mn[$key]);
            $intersect_mt = array_intersect($array, $array_dai_mt);
        }


        $intersect_mn_group = array_intersect($array, $array_dai_mn_group);

        $results = array_filter([$intersect_mn, $intersect_mb, $intersect_mt, $intersect_mn_group], function ($item) {

            return count($item) > 0;


        });

        if (array_keys($results)[sizeof($results) - 1] >= 3) {
            if ((array_values($results)[0][0]) == "2d") {
                return [3];
            } else if ((array_values($results)[0][0]) == "3d") {
                return [4];
            } else if ((array_values($results)[0][0]) == "4d") {
                return [5];
            }

        }

        return array_reduce(array_map(function ($key, $value) {
            return array_map(function ($item) use ($key) {
                return (int)$key * 1;
            }, $value);
        }, array_keys($results), $results), 'array_merge', array());
    }

    public
    function arrayKq($array_kq, $number)
    {

        $array_1 = [];
        $array_2 = [];

        foreach ($array_kq as $value) {
            if (is_string($value)) {
                $array_1[] = substr($value, -$number);
            }
            if (is_array($value) or ($value instanceof Traversable)) {
                foreach ($value as $each) {
                    $array_2[] = substr($each, -$number);

                }
            }


        }
        return array_merge($array_1, $array_2);
    }

    public
    function getSideToCheck($value, $dai, $date_check)
    {

        if ($dai == 1) {
            $arrayVl = ['vl', 'vlong', 'vinhlong', 'vinh long',];
            $arrayBd = ['bd', 'bduong', 'binhduong', 'binh duong'];
            $arrayTv = ['tv', 'tvinh', 'travinh', 'tra vinh'];
            $arrayTn = ['tn', 'tninh', 'tayninh', 'tay ninh'];
            $arrayAg = ['ag', 'angiang', 'an giang'];
            $arrayBthuan = ['bthuan', 'binhthuan', 'binh thuan'];
            $arrayHcm = ['tp', 'hcm'];
            $arrayLa = ['la', 'lan', 'longan', 'long an'];
            $arrayBp = ['bp', 'bphuoc', 'binhphuoc', 'binh phuoc'];
            $arrayHg = ['hg', 'hgiang', 'haugiang'];
            $arrayTg = ['tg', 'tgiang', 'tien giang', 'tiengiang'];
            $arrayKg = ['kg', 'kgiang', 'kiengiang', 'kien giang'];
            $arrayDl = ['dl', 'dlat', 'dalat', 'da lat'];
            $arrayDn = ['dn', 'dnai', 'dongnai', 'dn', 'dong nai'];
            $arrayCt = ['ct', 'ctho', 'cantho', 'can tho'];
            $arraySt = ['st', 'strang', 'soctrang', 'soc trang'];
            $arrayDt = ['dt', 'dthap', 'dongthap', 'dong thap'];
            $arrayVt = ['vt', 'vtau', 'vungtau', 'vung tau', 'vung tau'];
            $arrayBl = ['bl', 'blieu', 'baclieu', 'bac lieu'];
            $arrayBtre = ['btre', 'bentre', 'ben tre'];
            $arrayCm = ['cm', 'camau', 'ca mau'];
            $date = new \DateTime($date_check);
            $weekday = $date->format('N'); // 1 (Monday) to 7 (Sunday)

            if ($weekday == 4) {
                array_push($arrayBthuan, 'bt');
            } elseif ($weekday == 2) {
                array_push($arrayBtre, 'bt');
            }

            // binh thuan voi ben tre trung nhau
            $str = '';
            if (in_array($value, $arrayVl)) {
                $str = "Vĩnh Long";
            } elseif (in_array($value, $arrayBd)) {
                $str = "Bình Dương";
            } elseif (in_array($value, $arrayTv)) {
                $str = "Trà Vinh";
            } elseif (in_array($value, $arrayTn)) {
                $str = "Tây Ninh";
            } elseif (in_array($value, $arrayAg)) {
                $str = "An Giang";
            } elseif (in_array($value, $arrayBthuan)) {
                $str = "Bình Thuận";
            } elseif (in_array($value, $arrayHcm)) {
                $str = "TP. HCM";
            } elseif (in_array($value, $arrayLa)) {
                $str = "Long An";
            } elseif (in_array($value, $arrayBp)) {
                $str = "Bình Phước";
            } elseif (in_array($value, $arrayHg)) {
                $str = "Hậu Giang";
            } elseif (in_array($value, $arrayTg)) {
                $str = "Tiền Giang";
            } elseif (in_array($value, $arrayKg)) {
                $str = "Kiên Giang";
            } elseif (in_array($value, $arrayDl)) {
                $str = "Đà Lạt";
            } elseif (in_array($value, $arrayDn)) {
                $str = "Đồng Nai";
            } elseif (in_array($value, $arrayCt)) {
                $str = "Cần Thơ";
            } elseif (in_array($value, $arraySt)) {
                $str = "Sóc Trăng";
            } elseif (in_array($value, $arrayDt)) {
                $str = "Đồng Tháp";
            } elseif (in_array($value, $arrayVt)) {
                $str = "Vũng Tàu";
            } elseif (in_array($value, $arrayBl)) {
                $str = "Bạc Liêu";
            } elseif (in_array($value, $arrayBtre)) {
                $str = "Bến Tre";
            } elseif (in_array($value, $arrayCm)) {
                $str = "Cà Mau";
            }
            return $str;
        } else if ($dai == 3) {
            $arrayBd = ['bdinh', 'binhdinh', 'binh dinh', 'bd'];
            $arrayDl = ['dlak', 'daklak', 'dak lak', 'dlac', 'daclac', 'dac lac', 'dl'];
            $arrayDnang = ['dnang', 'danang', 'da nang'];
            $arrayDnong = ['dk', 'dknong', 'daknong', 'dak nong'];
            $arrayGl = ['glai', 'gialai', 'gia lai', 'gl'];
            $arrayKh = ['khoa', 'khanhhoa', 'khanh hoa', 'kh'];
            $arrayKt = ['ktum', 'kontum', 'kon tum', 'kt'];
            $arrayNt = ['nthuan', 'ninhthuan', 'ninh thuan', 'nt'];
            $arrayPy = ['pyen', 'phyen', 'phuyen', 'phu yen', 'py'];
            $arrayQb = ['qbinh', 'quangbinh', 'quang binh', 'qb'];
            $arrayQngai = ['qg', 'qngai', 'quangngai', 'quang ngai'];
            $arrayQnam = ['qnam', 'quangnam', 'quang nam'];
            $arrayQt = ['qtri', 'quangtri', 'quang tri', 'qt'];
            $arrayTth = ['thuathienhue', 'tt', 'thua thien hue', 'th', 'hue', 'tt'];
            $date = new \DateTime($date_check);
            $weekday = $date->format('N');
            //t5

            if ($weekday == 2) {

                array_push($arrayQnam, 'qn');
                // t7
            } elseif ($weekday == 3) {
                array_push($arrayDnang, 'dn');

            } elseif ($weekday == 6) {
//

                array_push($arrayDnang, 'dn');

//                array_push($arrayDnong, 'dn');
                array_push($arrayQngai, 'qn');
            }
            $str = '';

            if (in_array($value, $arrayBd)) {
                $str = "Bình Định";
            } elseif (in_array($value, $arrayDl)) {
                $str = "Đắk Lắk";
            } elseif (in_array($value, $arrayTth)) {
                $str = "Huế";
            } elseif (in_array($value, $arrayDnang)) {
                $str = "Đà Nẵng";
            } elseif (in_array($value, $arrayDnong)) {
                $str = "Đắk Nông";
            } elseif (in_array($value, $arrayGl)) {
                $str = "Gia Lai";
            } elseif (in_array($value, $arrayKh)) {
                $str = "Khánh Hòa";
            } elseif (in_array($value, $arrayNt)) {
                $str = "Ninh Thuận";
            } elseif (in_array($value, $arrayPy)) {
                $str = "Phú Yên";
            } elseif (in_array($value, $arrayQb)) {
                $str = "Quảng Bình";
            } elseif (in_array($value, $arrayQngai)) {
                $str = "Quảng Ngãi";
            } elseif (in_array($value, $arrayQnam)) {
                $str = "Quảng Nam";
            } elseif (in_array($value, $arrayQt)) {
                $str = "Quảng Trị";
            } elseif (in_array($value, $arrayKt)) {
                $str = "Kon Tum";
            }


            return $str;
        }

    }

    public function calculatorPrize($type, $so_danh, $so_tien_danh, $array_dai, $dai, $customer_id, $array_kq, $array_full_kq, $status, $type_detail, $date, $channel = null)
    {
        $ration_user = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::User)->where('region_id', $dai)->get()->first();
        $ration_customer = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::Customer)->where('region_id', $dai)->get()->first();

        $get_dai = $this->getALLSide($array_dai, $dai);

        $so_trung = [];
        $type_trung_and_money = [];
        $array_result = [];
        $sum_2_total = 0;
        $sum_3_total = 0;
        $sum_4_total = 0;
        $hai_so = 0;
        $ba_so = 0;
        $bon_so = 0;
        $DD = 0;
        $XC = 0;
        $DATHANG = 0;
        $DAXIEN = 0;

        $sum_2_fight = 0;
        $sum_3_fight = 0;
        $sum_4_fight = 0;
        if ($dai != 2)
            $list_area = array_map(function ($d) use ($dai) {
                return $this->getDaiFromFullname($d['channel'], $dai);
            }, $array_full_kq);
        else $list_area = [AreaEnum::MB];
        $dai_hien_tai = $this->getDaiFromFullname($channel, $dai);
        $result_tung_dai = array_fill_keys($list_area, [
            'hai_so_lai' => 0,
            'ba_so_lai' => 0,
            'bon_so_lai' => 0,
            'dau_duoi_lai' => 0,
            'bao_lo_2_so_lai' => 0,
            'bao_lo_3_so_lai' => 0,
            'bao_lo_4_so_lai' => 0,
            'xiu_chu_lai' => 0,
            'da_thang_lai' => 0,
            'da_xien_lai' => 0,
            'total_lai' => 0,
            'so_trung' => [],
            'tra_lai' => 0,
        ]);
        if ($dai != 2)
            $list_area = array_map(function ($d) use ($dai, $date) {
                return $this->convertArea($d, $dai, $date);
            }, (new TicketService())->convertMultipleArea($array_dai, $date, $dai));
        else $list_area = [AreaEnum::MB];
        foreach ($type as $type_each) {
            $val_sw = $type_each;
            switch ($val_sw) {
                case TypeFight::DAU:
                    $sum_dau_2 = 0;
                    $sum_xc_3 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(0, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 3) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq['t6'] as $each) {
                                    if (substr($each, -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = substr($each, -3) . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq['db'], -3);
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }

                            } else {

                                if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = substr($so_danh_each, -3);
                                    $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }


                            }


                        } elseif (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                    }
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t8"], -2);
                                    $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t8"], -2) . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                }
                            }
                        }
                    }

                    $sum_2_total += $sum_dau_2 * $ration_user->dau_duoi_ration;

                    $sum_2_fight += $sum_dau_2;
                    $DD += $sum_dau_2;

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$sum_dau_2 * $ration_user->dau_duoi_ration;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $sum_dau_2 * $ration_user->dau_duoi_ration;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_dau_2 * $ration_user->dau_duoi_ration + $sum_xc_3 * $ration_user->xiu_chu_ration;
                    break;
                case TypeFight::DUOI:
                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(1, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if (substr($array_kq["db"], -2) === $so_danh_each) {
                                $sum_duoi_2 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -2);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = substr($array_kq["db"], -2) . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];

                            }

                        } elseif (strlen($so_danh_each) == 3) {


                            if (substr($array_kq["db"], -3) === $so_danh_each) {
                                $sum_duoi_3 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -3);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = substr($array_kq["db"], -3) . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                            }

                        } elseif (strlen($so_danh_each) == 4) {

                            if (substr($array_kq["db"], -4) === $so_danh_each) {
                                $sum_duoi_4 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -4);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = substr($array_kq["db"], -4) . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];

                            }

                        }
                    }

                    $sum_2_total += $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $sum_3_total += $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $sum_4_total += $sum_duoi_4 * $ration_user->bon_so_ration;

                    $sum_2_fight += $sum_duoi_2;
                    $sum_3_fight += $sum_duoi_3;
                    $sum_4_fight += $sum_duoi_4;

                    $DD += $sum_duoi_2;
                    $XC += $sum_duoi_3;
                    $bon_so += $sum_duoi_4;


                    $hai_so_area = $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $bon_so_area = $sum_duoi_4 * $ration_user->bon_so_ration;

                    $result_tung_dai[$dai_hien_tai]["bon_so_lai"] += $bon_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area + $bon_so_area;

                    break;
                case TypeFight::DAU_DUOI:

                    $sum_dau_duoi = 0;
                    $sum_xc_3 = 0;

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(2, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 3) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }
                            if ($dai == 2) {
                                foreach ($array_kq['t6'] as $each) {
                                    if (substr($each, -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;

                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }

                                }
                                if (substr($array_kq['db'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;

                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            } else {


                                if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }

                                if (substr($array_kq['db'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }


                        } elseif (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_duoi += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                    }

                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                }
                            }
                        }
                    }
                    $sum_2_total += $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_duoi;
                    $DD += $sum_dau_duoi;

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $hai_so_area = $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area;
                    break;
                case TypeFight::BAO_LO:
                    $sum_3 = 0;
                    $sum_4 = 0;
                    $sum_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(3, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            foreach (($this->arrayKq($array_kq, 2)) as $value) {
                                if (substr($value, -2) === $so_danh_each) {

                                    $sum_2 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                            }

                        } else if (strlen($so_danh_each) == 3) {
                            foreach (($this->arrayKq($array_kq, 3)) as $value) {
                                if (substr($value, -3) === $so_danh_each) {
                                    $sum_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }
                            }

                        } elseif (strlen($so_danh_each) == 4) {

                            foreach (($this->arrayKq($array_kq, 4)) as $value) {

                                if (substr($value, -4) === $so_danh_each) {
                                    $sum_4 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];
                                }

                            }
                        }
                    }

                    $sum_2_total += $sum_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_3 * $ration_user->ba_so_ration;
                    $sum_4_total += $sum_4 * $ration_user->bon_so_ration;

                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;

                    $sum_2_fight += $sum_2;
                    $sum_3_fight += $sum_3;
                    $sum_4_fight += $sum_4;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_4_so_lai"] += $sum_4_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;
                    break;
                case  TypeFight::XIU_CHU:
                    $sum_xc_3 = 0;
                    $sum_dau_duoi = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(4, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($array_kq["db"], -2) === $so_danh_each) {
                                        $sum_dau_duoi += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";
                                    }
                                }
                                if (substr($each, -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.d';
                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";
                                }
                            }
                        } else
                            if (strlen($so_danh_each) == 3) {
                                if ($dai == 2) {
                                    foreach ($array_kq['t6'] as $each) {
                                        if (substr($each, -3) === $so_danh_each) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = $so_danh_each;
                                            $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                            $key_area = $so_danh_each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                        }
                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                    }
                                } else {
                                    if (substr($array_kq['t7'], -3) === $so_danh_each) {

                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {

                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                    }
                                }
                            }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $sum_2_total += $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_duoi;
                    $DD += $sum_dau_duoi;

                    $hai_so_area = $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAU:
                    $sum_xc_dau_3 = 0;
                    $sum_dau_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(5, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(5, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";

                                        $key_area = substr($each, -2) . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t8"], -2);
                                    $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";


                                    $key_area = substr($array_kq["t8"], -2) . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }


                        } else
                            if (strlen($so_danh_each) == 3) {
                                if ($dai == 2) {
                                    foreach ($array_kq['t6'] as $each) {
                                        if (substr($each, -3) === $so_danh_each) {
                                            $sum_xc_dau_3 += $tien_danh;
                                            $so_trung[] = substr($each, -3);
                                            $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";

                                            $key_area = substr($each, -3) . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }
                                    }
                                } else {
                                    if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                        $sum_xc_dau_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq['t7'], -3);
                                        $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";

                                        $key_area = substr($array_kq['t7'], -3) . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }

                            }
                    }

                    $sum_3_total += $sum_xc_dau_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_dau_3;
                    $sum_3_fight += $sum_xc_dau_3;

                    $sum_2_total += $sum_dau_2 * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_2;
                    $DD += $sum_dau_2;

                    $hai_so_area = $sum_dau_2 * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_xc_dau_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area + $hai_so_area;
                    break;
                case TypeFight::XIU_CHU_DUOI:
                    $sum_xc_duoi = 0;
                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(6, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(6, $type);
                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];
                            }

                            if (substr($array_kq["db"], -2) === $so_danh_each) {
                                $sum_duoi_2 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -2);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";
                            }


                        } elseif (strlen($so_danh_each) == 3) {
                            if (substr($array_kq['db'], -3) === $so_danh_each) {
                                $sum_duoi_3 += $tien_danh;
                                $so_trung[] = substr($array_kq['db'], -3);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";

                                $key_area = substr($array_kq["db"], -3) . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                            }
                        } elseif (strlen($so_danh_each) == 4) {
                            if (substr($array_kq['db'], -4) === $so_danh_each) {
                                $sum_duoi_4 += $tien_danh;
                                $so_trung[] = substr($array_kq['db'], -4);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";

                                $key_area = substr($array_kq["db"], -4) . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                            }
                        }
                    }
                    $sum_3_total += $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $sum_4_total += $sum_duoi_4 * $ration_user->xiu_chu_ration;

                    $XC += $sum_duoi_3;
                    $sum_3_fight += $sum_duoi_3;

                    $sum_4_fight += $sum_duoi_4;

                    $sum_2_total += $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_duoi_2;
                    $DD += $sum_duoi_2;

                    $hai_so_area = $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $bon_so_area = $sum_duoi_4 * $ration_user->bon_so_ration;
                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    // $result_tung_dai[$dai_hien_tai]["bon_so_lai"]+=$bon_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area;
                    break;
                case TypeFight::DA:
                    if ((array_key_exists("7", $type_detail) && $type_detail[7] == "da" || $type_detail[7] == "dv")
                        && count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                        if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                            if ($status == 0) {
                                $sum_da_xien_2 = 0;
                                if (count($so_tien_danh) >= 2) {
                                    $key = array_search(7, $type);
                                    $tien_danh = $so_tien_danh[$key];
                                } else {
                                    $tien_danh = $so_tien_danh[0];
                                }
                                $array_temp_list_kq = [];
                                if ($get_dai[0] == 3) {
                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                } elseif ($get_dai[0] == 4) {
                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                    $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                                } elseif ($get_dai[0] == 5) {
                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                    $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                                    $array_temp_list_kq[3] = $this->arrayKq($array_full_kq[3]['result'], 2);
                                } else {
                                    $array_merge_multiple = null;
                                    foreach ($array_full_kq as $result_item) {
                                        $l = 0;
                                        foreach ($array_dai as $side) {
                                            if ($result_item['channel'] == 'Thừa T. Huế') {
                                                $result_item['channel'] = 'Huế';
                                            }
                                            if ($this->getSideToCheck($side, $dai, $date) == $result_item['channel']) {
                                                if ($array_merge_multiple == null) {
                                                    $array_merge_multiple = $this->arrayKq($result_item['result'], 2);
                                                }
                                                $array_temp_list_kq[$l] = $this->arrayKq($result_item['result'], 2);
                                                $array_merge_multiple = array_merge($this->arrayKq($result_item['result'], 2), $array_merge_multiple);

                                            }
                                            $l++;
                                        }
                                    }
                                }

                                $array_temp_list_kq = array_map('array_filter', $array_temp_list_kq);

                                $string_da_trung = "";
                                $get_temp_soda_1 = "";
                                $flag_change_calculate = false;
                                $array_kq_temp = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                foreach ($get_dai as $dai_detail) {
                                    if ($dai_detail == 0 || $dai_detail == 2) {
                                        if (isset(array_count_values($get_dai)[0]) && array_count_values($get_dai)[0] > 2 || isset(array_count_values($get_dai)[2]) && array_count_values($get_dai)[2] > 2) {
                                            $flag_change_calculate = true;
                                        }
                                    } else if ($dai_detail == 4) {
                                        $flag_change_calculate = true;
                                    } else if ($dai_detail == 5) {
                                        $flag_change_calculate = true;
                                    }
                                }
                                for ($i = 0; $i < count($so_danh) - 1; $i++) {
                                    for ($j = $i + 1; $j < count($so_danh); $j++) {
                                        if ($flag_change_calculate) {
                                            if ($get_dai[0] == 4 || sizeof($get_dai) == 3) {
                                                $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                    $counts = array_count_values($array_kq_temp_1);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                    $counts = array_count_values($array_kq_temp_2);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);

                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                    $counts = array_count_values($array_kq_temp_3);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                            } else if ($get_dai[0] == 5 || sizeof($get_dai) == 4) {
                                                $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                    $counts = array_count_values($array_kq_temp_1);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                    $counts = array_count_values($array_kq_temp_2);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                    $counts = array_count_values($array_kq_temp_3);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_4 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_4)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_4)) {
                                                    $counts = array_count_values($array_kq_temp_4);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_5 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_5)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_5)) {
                                                    $counts = array_count_values($array_kq_temp_5);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                                $array_kq_temp_6 = array_merge($array_temp_list_kq[2], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_6)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_6)) {
                                                    $counts = array_count_values($array_kq_temp_6);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $sum_da_xien_2 += $tien_danh * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                                    }
                                                }
                                            }
                                        } else


                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp)) {
                                                $counts = array_count_values($array_kq_temp);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;

                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";


                                                    $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";

                                                }

                                            }

                                    }
                                }

                                $so_danh_area = collect($so_danh)->sort()->values()->all();
                                $count_so_danh = count($so_danh_area);
                                $array_so_danh = [];
                                for ($i = 0; $i < $count_so_danh; $i++) {
                                    for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                        $array_so_danh[] = [$so_danh_area[$i], $so_danh_area[$j]];
                                    }
                                }
                                $array_temp_list_kq = array_combine($list_area, array_values($array_temp_list_kq));
                                $array_value_counts = [];

                                foreach ($array_temp_list_kq as $key => &$sub_array) {
                                    $array_value_counts[$key] = array_count_values($sub_array);
                                }
                                foreach ($array_value_counts as $dai1 => $kq1) {
                                    foreach ($array_value_counts as $dai2 => $kq2) {
                                        if ($dai1 < $dai2) {
                                            foreach ($array_so_danh as $so) {
                                                $so1 = $so[0];
                                                $so2 = $so[1];

                                                $count_trung = min(((isset($kq1[$so1]) ? $kq1[$so1] : 0)
                                                    + (isset($kq2[$so1]) ? $kq2[$so1] : 0)),
                                                    ((isset($kq1[$so2]) ? $kq1[$so2] : 0)
                                                        + (isset($kq2[$so2]) ? $kq2[$so2] : 0)));
                                                if ($count_trung > 0) {
                                                    $key = $so1 . '.' . $so2 . '.' . $dai1 . '.' . $dai2;
                                                    $result_tung_dai[$dai1]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $tien_danh, 'trung' => $count_trung * $tien_danh * $ration_user->da_xien_ration];
                                                    $result_tung_dai[$dai2]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $tien_danh, 'trung' => $count_trung * $tien_danh * $ration_user->da_xien_ration];
                                                    $result_tung_dai[$dai1]['da_xien_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai2]['da_xien_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai1]['total_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai2]['total_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai1]['tra_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration / 2;
                                                    $result_tung_dai[$dai2]['tra_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration / 2;
                                                }
                                            }
                                        }
                                    }
                                }
                                $so_trung[] = $string_da_trung;
                                $type_trung_and_money[] = $get_temp_soda_1;

                                $sum_2_total += $sum_da_xien_2 * $ration_user->da_xien_ration;

                                $sum_2_fight += $sum_da_xien_2;

                                $DAXIEN += $sum_da_xien_2;

                                $hai_so_area = $sum_da_xien_2 * $ration_user->da_xien_ration;
                            }
                        } else {
                            throw new \Exception("Đá xiên phải đánh từ 2 số hoặc 2 đài trở lên");
                        }
                    } else if (count($so_danh) >= 2) {

                        $sum_da_2 = 0;
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(7, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }
                        $count = 0;

                        $string_da_trung = "";
                        $get_temp_soda_1 = "";
                        // dd($counts = array_count_values($this->arrayKq($array_kq, 2)));
                        for ($i = 0; $i < count($so_danh) - 1; $i++) {

                            for ($j = $i + 1; $j < count($so_danh); $j++) {
                                $count_2 = 0;
                                if (in_array(substr($so_danh[$i], -2), $this->arrayKq($array_kq, 2))
                                    &&
                                    in_array(substr($so_danh[$j], -2), $this->arrayKq($array_kq, 2))) {
                                    $counts = array_count_values($this->arrayKq($array_kq, 2));

                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                    $temp_count_value = array_count_values($this->arrayKq($array_kq, 2));
                                    if ($temp_count_value[$so_danh[$i]] > $pairCount || $temp_count_value[$so_danh[$j]] > $pairCount) {
                                        $count++;
                                        $count_2++;
                                    }

                                    if ($pairCount > 0) {
                                        $so0 = substr($so_danh[$i], -2);
                                        $so1 = substr($so_danh[$j], -2);
                                        if ($so0 > $so1) $this->swap1($so0, $so1);
                                        $key_area = $so0 . "." . $so1;
                                        if ($ration_user->nua_vong == 1 && $count_2 > 0) {
                                            $sum_da_2 += $tien_danh * ($pairCount + 0.5);
                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount + 0.5 . ")";

                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => $pairCount, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->da_thang_ration * $pairCount];
                                        } else {
                                            $sum_da_2 += $tien_danh * $pairCount;
                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => $pairCount, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->da_thang_ration * $pairCount];
                                        }

                                        $get_temp_soda_1 = $type_detail[7] . $tien_danh . "n";
                                    }
                                }
                            }
                        }


                        $so_trung[] = $string_da_trung;
                        $type_trung_and_money[] = $get_temp_soda_1;

                        $sum_2_total += $sum_da_2 * $ration_user->da_thang_ration;

                        $sum_2_fight += $sum_da_2;

                        $DATHANG += $sum_da_2;

                        $hai_so_area = $sum_da_2 * $ration_user->da_thang_ration;
                        // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                        $result_tung_dai[$dai_hien_tai]["da_thang_lai"] += $hai_so_area;
                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area;

                    } else {
                        throw new \Exception("Đá phải đánh từ 2 số trở lên");
                    }

                    break;
                case TypeFight::DA_XIEN:

                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                        if ($status == 0) {
                            $sum_da_xien_2 = 0;

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(8, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            $array_temp_list_kq = [];
                            if ($get_dai[0] == 3) {
                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                            } elseif ($get_dai[0] == 4) {

                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                            } elseif ($get_dai[0] == 5) {

                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                                $array_temp_list_kq[3] = $this->arrayKq($array_full_kq[3]['result'], 2);
                            } else {
                                $array_merge_multiple = null;

                                foreach ($array_full_kq as $result_item) {
                                    $l = 0;
                                    foreach ($array_dai as $side) {
                                        if ($result_item['channel'] == 'Thừa T. Huế') {
                                            $result_item['channel'] = 'Huế';
                                        }
                                        if ($this->getSideToCheck($side, $dai, $date) == $result_item['channel']) {

                                            if ($array_merge_multiple == null) {
                                                $array_merge_multiple = $this->arrayKq($result_item['result'], 2);

                                            }
                                            $array_temp_list_kq[$l] = $this->arrayKq($result_item['result'], 2);
                                            $array_merge_multiple = array_merge($this->arrayKq($result_item['result'], 2), $array_merge_multiple);

                                        }
                                        $l++;
                                    }
                                }
                            }
                            $string_da_trung = "";
                            $get_temp_soda_1 = "";
                            $flag_change_calculate = false;
                            $array_temp_list_kq = array_map('array_filter', $array_temp_list_kq);

                            foreach ($get_dai as $dai_detail) {

                                if ($dai_detail == 0 || $dai_detail == 2) {
                                    if (isset(array_count_values($get_dai)[0]) && array_count_values($get_dai)[0] > 2 || isset(array_count_values($get_dai)[2]) && array_count_values($get_dai)[2] > 2) {
                                        $flag_change_calculate = true;
                                    }
                                } else if ($dai_detail == 4) {
                                    $flag_change_calculate = true;
                                } else if ($dai_detail == 5) {
                                    $flag_change_calculate = true;
                                }
                            }
//                            dd($get_dai);
                            $array_kq_temp = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                            for ($i = 0; $i < count($so_danh) - 1; $i++) {
                                for ($j = $i + 1; $j < count($so_danh); $j++) {
                                    if ($flag_change_calculate) {

                                        if ($get_dai[0] == 4 || sizeof($get_dai) == 3) {
                                            $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                $counts = array_count_values($array_kq_temp_1);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                $counts = array_count_values($array_kq_temp_2);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);

                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                $counts = array_count_values($array_kq_temp_3);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                        } else if ($get_dai[0] == 5 || sizeof($get_dai) == 4) {
                                            $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                $counts = array_count_values($array_kq_temp_1);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                $counts = array_count_values($array_kq_temp_2);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                $counts = array_count_values($array_kq_temp_3);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_4 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_4)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_4)) {
                                                $counts = array_count_values($array_kq_temp_4);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_5 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_5)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_5)) {
                                                $counts = array_count_values($array_kq_temp_5);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                            $array_kq_temp_6 = array_merge($array_temp_list_kq[2], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_6)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_6)) {
                                                $counts = array_count_values($array_kq_temp_6);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $sum_da_xien_2 += $tien_danh * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                                }
                                            }
                                        }
                                    } else

                                        if (in_array(substr($so_danh[$i], -2), $array_kq_temp)
                                            && in_array(substr($so_danh[$j], -2), $array_kq_temp)) {
                                            $counts = array_count_values($array_kq_temp);
                                            $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                            if ($pairCount > 0) {
                                                $sum_da_xien_2 += $tien_danh * $pairCount;
                                                $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                $get_temp_soda_1 = $type_detail[8] . $tien_danh . "n";
                                            }
                                        }
                                }
                            }

                            $so_danh_area = collect($so_danh)->sort()->values()->all();
                            $count_so_danh = count($so_danh_area);
                            $array_so_danh = [];
                            for ($i = 0; $i < $count_so_danh; $i++) {
                                for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                    $array_so_danh[] = [$so_danh_area[$i], $so_danh_area[$j]];
                                }
                            }
                            $array_temp_list_kq = array_combine($list_area, array_values($array_temp_list_kq));
                            $array_value_counts = [];

                            foreach ($array_temp_list_kq as $key => &$sub_array) {
                                $array_value_counts[$key] = array_count_values($sub_array);
                            }
                            foreach ($array_value_counts as $dai1 => $kq1) {
                                foreach ($array_value_counts as $dai2 => $kq2) {
                                    if ($dai1 < $dai2) {
                                        foreach ($array_so_danh as $so) {
                                            $so1 = $so[0];
                                            $so2 = $so[1];
                                            $count_trung = min(((isset($kq1[$so1]) ? $kq1[$so1] : 0)
                                                + (isset($kq2[$so1]) ? $kq2[$so1] : 0)),
                                                ((isset($kq1[$so2]) ? $kq1[$so2] : 0)
                                                    + (isset($kq2[$so2]) ? $kq2[$so2] : 0)));
                                            if ($count_trung > 0) {
                                                $key = $so1 . '.' . $so2 . '.' . $dai1 . '.' . $dai2;
                                                $result_tung_dai[$dai1]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $tien_danh, 'trung' => $count_trung * $tien_danh * $ration_user->da_xien_ration];
                                                $result_tung_dai[$dai2]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $tien_danh, 'trung' => $count_trung * $tien_danh * $ration_user->da_xien_ration];
                                                $result_tung_dai[$dai1]['da_xien_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai2]['da_xien_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai1]['total_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai2]['total_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai1]['tra_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration / 2;
                                                $result_tung_dai[$dai2]['tra_lai'] += $count_trung * $tien_danh * $ration_user->da_xien_ration / 2;
                                            }

                                        }
                                    }
                                }
                            }
                            $so_trung[] = $string_da_trung;
                            $type_trung_and_money[] = $get_temp_soda_1;
                            $sum_2_total += $sum_da_xien_2 * $ration_user->da_xien_ration;
                            $sum_2_fight += $sum_da_xien_2;
                            $DAXIEN += $sum_da_xien_2;

                            $hai_so_area = $sum_da_xien_2 * $ration_user->da_xien_ration;
                        }
                    } else {

                        throw new \Exception("Đá xiên phải đánh từ 2 số hoặc 2 đài trở lên");

                    }
                    break;
                case TypeFight::DANH_BAY_LO:
                    $sum_bay_lo_2 = 0;
                    $sum_bay_lo_3 = 0;

                    $sum_tam_lo_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(9, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if ($dai == 2) {

                                if ($dai == 2) {
                                    $number = 0;
                                    foreach ($get_dai as $each) {

                                        if ($each == 1) {
                                            $number += 7;

                                        }

                                    }
                                    if (count($so_tien_danh) >= 2) {
                                        $key = array_search(11, $type);
                                        $tien_danh = $so_tien_danh[$key];

                                    } else {
                                        $tien_danh = $so_tien_danh[0];
                                    }

                                    if (strlen($so_danh_each) == 2) {
                                        if ($so_danh_each === substr($array_kq["db"], -2)) {
                                            $sum_tam_lo_2 += $tien_danh;
                                            $so_trung[] = substr($array_kq["db"], -2);
                                            $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                            $key_area = substr($array_kq["db"], -2) . '.8l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                        }
                                        foreach ($array_kq["t6"] as $each) {
                                            if ($so_danh_each === substr($each, -2)) {
                                                $sum_tam_lo_2 += $tien_danh;
                                                $so_trung[] = substr($each, -2);
                                                $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                                $key_area = substr($array_kq["db"], -2) . '.8l';
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                            }

                                        }

                                        foreach ($array_kq["t7"] as $each) {
                                            if ($so_danh_each === substr($each, -2)) {
                                                $sum_tam_lo_2 += $tien_danh;
                                                $so_trung[] = substr($each, -2);
                                                $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                                $key_area = substr($each, -2) . '.8l';
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                            }

                                        }

                                    }


                                } else {

                                    throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                                }

                            } else {
                                if ($so_danh_each === $array_kq["t8"]) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = $array_kq["t8"];
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $array_kq["t8"] . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["t7"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t7"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t7"], -2) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["db"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["db"], -2) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                                if ($so_danh_each === substr($array_kq["t5"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t5"], -2) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_bay_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                        $key_area = substr($each, -2) . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }

                                }
                            }


                        } else if (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {

                                if ($so_danh_each === substr($array_kq["db"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["db"], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }

                                if ($so_danh_each === substr($array_kq["t5"][3], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][3], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t5"][3], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["t5"][4], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][4], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t5"][4], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["t5"][5], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][5], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t5"][5], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -3)) {
                                        $sum_bay_lo_3 += $tien_danh;
                                        $so_trung[] = substr($each, -3);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                        $key_area = substr($each, -3) . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }

                                }
                            } else {
                                if ($so_danh_each === $array_kq["t7"]) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = $array_kq["t7"];
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $array_kq["t7"] . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["t4"][0], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t4"][0], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t4"][0], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }
                                if ($so_danh_each === substr($array_kq["t5"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["t5"][0], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }
                                if ($so_danh_each === substr($array_kq["db"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = substr($array_kq["db"], -3) . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -3)) {
                                        $sum_bay_lo_3 += $tien_danh;
                                        $so_trung[] = substr($each, -3);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                        $key_area = substr($each, -3) . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }

                                }
                            }
                        }
                    }
                    $sum_2_total += $sum_bay_lo_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bay_lo_3 * $ration_user->ba_so_ration;

                    $hai_so += $sum_bay_lo_2;
                    $ba_so += $sum_bay_lo_3;


                    $sum_2_fight += $sum_bay_lo_2;
                    $sum_3_fight += $sum_bay_lo_3;


                    $sum_2_total += $sum_tam_lo_2 * $ration_user->hai_so_ration;

                    $hai_so += $sum_tam_lo_2;


                    $sum_2_fight += $sum_tam_lo_2;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;


//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
//                    $result_tung_dai[$dai_hien_tai]["bon_so_lai"]+=$bon_so_area;
//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_bay_lo_2 * $ration_user->hai_so_ration;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"] += $sum_bay_lo_3 * $ration_user->ba_so_ration;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_bay_lo_2 * $ration_user->hai_so_ration + $sum_bay_lo_3 * $ration_user->ba_so_ration;
                    break;
                case TypeFight::BAY_LO_DAO:

                    $sum_bay_lo_dao_2 = 0;

                    $sum_bay_lo_dao_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(10, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                if ($dai == 2) {
                                    $sum_tam_lo_dao_2 = 0;
                                    $number = 0;
                                    foreach ($get_dai as $each) {
                                        if ($each == 1) {
                                            $number += 7;
                                        }
                                    }
                                    if (count($so_tien_danh) >= 2) {
                                        $key = array_search(12, $type);
                                        $tien_danh = $so_tien_danh[$key];

                                    } else {
                                        $tien_danh = $so_tien_danh[0];
                                    }
                                    if (strlen($so_danh_each) == 2) {
                                        foreach ($this->permute($so_danh_each) as $each_permute) {


                                            if ($each_permute === substr($array_kq["db"], -2)) {
                                                $sum_tam_lo_dao_2 += $tien_danh;
                                                $so_trung[] = substr($array_kq["db"], -2);
                                                $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                                $key_area = $each_permute . '7l';
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                            }
                                            foreach ($array_kq["t6"] as $each1) {
                                                if ($each_permute === substr($each1, -2)) {
                                                    $sum_tam_lo_dao_2 += $tien_danh;
                                                    $key_area = $each_permute . '7l';
                                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                                    $key_area = $each_permute . '7l';
                                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                                }
                                            }
                                            foreach ($array_kq["t7"] as $each2) {
                                                if ($each_permute === substr($each2, -2)) {
                                                    $sum_tam_lo_dao_2 += $tien_danh;
                                                    $so_trung[] = substr($each2, -2);
                                                    $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                                    $key_area = $each_permute . '7l';
                                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                                }
                                            }
                                        }
                                    }

                                    $sum_2_total += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;

                                    $hai_so += $sum_tam_lo_dao_2;

                                    $sum_2_fight += $sum_tam_lo_dao_2;

                                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;
                                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;

                                } else {
                                    throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                                }
                                break;
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === $array_kq["t8"]) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = $array_kq["t8"];
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }
                                    if ($each === substr($array_kq["t7"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t7"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }
                                    if ($each === substr($array_kq["db"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                    }
                                    if ($each === substr($array_kq["t5"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each === substr($each_, -2)) {
                                            $sum_bay_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each_, -2);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                            $key_area = $each . '7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                        }

                                    }
                                }
                            }


                        } else if (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each == substr($array_kq["db"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = substr($array_kq["db"], -3) . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each == substr($array_kq["t5"][3], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][3], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = substr($array_kq["t5"][3], -3) . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each == substr($array_kq["t5"][4], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][4], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = substr($array_kq["t5"][4], -3) . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each == substr($array_kq["t5"][5], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][5], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = substr($array_kq["t5"][5], -3) . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each == substr($each_, -3)) {
                                            $sum_bay_lo_dao_3 += $tien_danh;
                                            $so_trung[] = substr($each_, -3);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                            $key_area = substr($each_, -3) . '7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                        }

                                    }
                                }
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === $array_kq["t7"]) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = $array_kq["t7"];
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each === substr($array_kq["t4"][0], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t4"][0], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each === substr($array_kq["t5"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each === substr($each_, -3)) {
                                            $sum_bay_lo_dao_3 += $tien_danh;
                                            $so_trung[] = substr($each_, -3);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                            $key_area = substr($each_, -3) . '7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                        }

                                    }
                                }
                            }


                        }
                    }
                    $sum_2_total += $sum_bay_lo_dao_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bay_lo_dao_3 * $ration_user->ba_so_ration;

                    $hai_so += $sum_bay_lo_dao_2;
                    $ba_so += $sum_bay_lo_dao_3;

//
                    $sum_2_fight += $sum_bay_lo_dao_2;
                    $sum_3_fight += $sum_bay_lo_dao_3;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;


//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_bay_lo_dao_2 * $ration_user->hai_so_ration;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"] += $sum_bay_lo_dao_3 * $ration_user->ba_so_ration;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_bay_lo_dao_3 * $ration_user->ba_so_ration + $sum_bay_lo_dao_2 * $ration_user->hai_so_ration;
                    break;
                case TypeFight::TAM_LO:
                    if ($dai == 2) {

                        $sum_tam_lo_2 = 0;


                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(11, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 2) {


                                if ($so_danh_each === substr($array_kq["db"], -2)) {
                                    $sum_tam_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -2);
                                    $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '8l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_tam_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }

                                }
                                foreach ($array_kq["t7"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_tam_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }

                                }

                            }
                        }
                        $sum_2_total += $sum_tam_lo_2 * $ration_user->hai_so_ration;

                        $hai_so += $sum_tam_lo_2;


                        $sum_2_fight += $sum_tam_lo_2;

//                        $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_tam_lo_2 * $ration_user->hai_so_ration;
//                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_tam_lo_2 * $ration_user->hai_so_ration;


                        $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total;

                    } else {
                        throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                    }

                    break;
                case TypeFight::TAM_LO_DAO:
                    if ($dai == 2) {
                        $sum_tam_lo_dao_2 = 0;


                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(12, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {
                            foreach ($this->permute($so_danh_each) as $each_permute) {


                                if (strlen($so_danh_each) == 2) {
                                    if ($each_permute === substr($array_kq["db"], -2)) {
                                        $sum_tam_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -2);
                                        $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                        $key_area = $each_permute . '8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each) {
                                        if ($each_permute === substr($each, -2)) {
                                            $sum_tam_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each, -2);
                                            $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                            $key_area = $each_permute . '8l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                        }

                                    }
                                    foreach ($array_kq["t7"] as $each) {
                                        if ($each_permute === substr($each, -2)) {
                                            $sum_tam_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each, -2);
                                            $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                            $key_area = $each_permute . '8l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                        }

                                    }
                                }
                            }
                        }
                        $sum_2_total += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;

                        $hai_so += $sum_tam_lo_dao_2;

                        $sum_2_fight += $sum_tam_lo_dao_2;

//                        $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;
//                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;


                        $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total;

                    } else {
                        throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                    }
                    break;
                case TypeFight::XIU_CHU_DAO_DAU:
                    $sum_xc_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(13, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            throw new \Exception("Xỉu chủ không được đánh 2 số");

                        }
                        if (strlen($so_danh_each) == 3) {

                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    foreach ($array_kq["t6"] as $each_t6) {
                                        if ($each === substr($each_t6, -3)) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = substr($each_t6, -3);
                                            $type_trung_and_money[] = $type_detail[13] . $tien_danh . "n";

                                            $key_area = $each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }

                                    }
                                }

                            } else {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === substr($array_kq["t7"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t7"], -3);
                                        $type_trung_and_money[] = $type_detail[13] . $tien_danh . "n";

                                        $key_area = $each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            }

                        }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAO_DUOI:
                    $sum_xc_3 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(14, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            throw new \Exception("Xỉu chủ không được đánh 2 số");
                        }

                        if (strlen($so_danh_each) == 3) {
                            foreach ($this->permute($so_danh_each) as $each) {
                                if ($each === substr($array_kq["db"], -3)) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[14] . $tien_danh . "n";

                                    $key_area = $each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }
                        }
                    }
                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAO:
                    $sum_xc_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(15, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            throw new \Exception("Xỉu chủ không được đánh 2 số");
                        } elseif (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    foreach ($array_kq["t6"] as $each_t7) {
                                        if ($each === substr($each_t7, -3)) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = $each;
                                            $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                            $key_area = $each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                        }
                                    }
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                    }
                                    if ($each === substr($array_kq["t7"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                                    }
                                }
                            }
                        }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::BAO_LO_DAO:
                    $sum_bao_dao_2 = 0;
                    $sum_bao_dao_3 = 0;
                    $sum_bao_dao_4 = 0;

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(16, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            foreach ($this->permute($so_danh_each) as $each) {
                                foreach (($this->arrayKq($array_kq, 2)) as $value) {
                                    if (substr($value, -2) === $each) {

                                        $sum_bao_dao_2 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                    }
                                }
                            }
                        } else if (strlen($so_danh_each) == 3) {

                            foreach ($this->permute($so_danh_each) as $each) {

                                foreach (($this->arrayKq($array_kq, 3)) as $value) {

                                    if (substr($value, -3) === $each) {

//                                        dd($this->arrayKq($array_kq, 3));
                                        $sum_bao_dao_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                }
                            }
                        } else if (strlen($so_danh_each) == 4) {

                            foreach ($this->permute($so_danh_each) as $each) {
                                foreach (($this->arrayKq($array_kq, 4)) as $value) {
                                    if (substr($value, -4) === $each) {

                                        $sum_bao_dao_4 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];

                                    }
                                }


                            }
                        }
                    }
                    $sum_2_total += $sum_bao_dao_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bao_dao_3 * $ration_user->ba_so_ration;
                    $sum_4_total += $sum_bao_dao_4 * $ration_user->bon_so_ration;

                    $hai_so += $sum_bao_dao_2;
                    $ba_so += $sum_bao_dao_3;
                    $bon_so += $sum_bao_dao_4;

                    $sum_2_fight += $sum_bao_dao_2;
                    $sum_3_fight += $sum_bao_dao_3;
                    $sum_4_fight += $sum_bao_dao_4;
//
//                    $hai_so_area = $sum_bao_dao_2 * $ration_user->hai_so_ration;
//                    $ba_so_area = $sum_bao_dao_3 * $ration_user->ba_so_ration;
//                    $bon_so_area = $sum_bao_dao_4 * $ration_user->bon_so_ration;
                    // $result_tung_dai[$dai_hien_tai]["hai_so_lai"]+=$hai_so_area;
                    // $result_tung_dai[$dai_hien_tai]["ba_so_lai"]+=$ba_so_area;
                    // $result_tung_dai[$dai_hien_tai]["bon_so_lai"]+=$bon_so_area;
//                    $result_tung_dai[$dai_hien_tai]["bao_lo_lai"] += $bon_so_area + $ba_so_area + $hai_so_area;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $bon_so_area + $ba_so_area + $hai_so_area;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_4_so_lai"] += $sum_4_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;

                    break;
            }
        }

        $tien_trung = ($hai_so * $ration_user->hai_so_ration) + ($ba_so * $ration_user->ba_so_ration)
            + ($bon_so * $ration_user->bon_so_ration) + ($DD * $ration_user->dau_duoi_ration) + ($XC * $ration_user->xiu_chu_ration)
            + ($DATHANG * $ration_user->da_thang_ration) + ($DAXIEN * $ration_user->da_xien_ration);

        $tien_trung_2nd = ($hai_so * $ration_customer->hai_so_ration) + ($ba_so * $ration_customer->ba_so_ration)
            + ($bon_so * $ration_customer->bon_so_ration) + ($DD * $ration_customer->dau_duoi_ration) + ($XC * $ration_customer->xiu_chu_ration)
            + ($DATHANG * $ration_customer->da_thang_ration) + ($DAXIEN * $ration_customer->da_xien_ration);
        $string_so_trung = '';

        $array_result['tong_2_so'] = $sum_2_total;
        $array_result['tong_3_so'] = $sum_3_total;
        $array_result['tong_4_so'] = $sum_4_total;
        $array_result['hai_so'] = $hai_so;
        $array_result['ba_so'] = $ba_so;
        $array_result['bon_so'] = $bon_so;
        $array_result['DD'] = $DD;
        $array_result['XC'] = $XC;
        $array_result['DATHANG'] = $DATHANG;
        $array_result['DAXIEN'] = $DAXIEN;
        $array_result['sum_2_fight'] = $sum_2_fight;
        $array_result['sum_3_fight'] = $sum_3_fight;
        $array_result['sum_4_fight'] = $sum_4_fight;
        $array_result['tien_trung'] = $tien_trung;
        $array_result['tien_trung_2nd'] = $tien_trung_2nd;
        $array_result['string_so_trung'] = $string_so_trung;
        $array_result['so_trung'] = $so_trung;
        $array_result['loai_so_trung'] = $type_trung_and_money;
        $array_result['tung_dai'] = $result_tung_dai;

        return array_filter($array_result, function ($value) {
            return $value != 0;
        });
    }


    function getDaiFromFullname($dai, $region)
    {
        if ($region == 2) {
            return AreaEnum::MB;
        } elseif ($region == 1) {
            if (in_array($dai, ['TP. HCM'])) {
                return AreaEnum::MN_HCM;
            } elseif (in_array($dai, ['Vĩnh Long'])) {
                return AreaEnum::MN_VINH_LONG;
            } elseif (in_array($dai, ['Bình Dương'])) {
                return AreaEnum::MN_BINH_DUONG;
            } elseif (in_array($dai, ['Trà Vinh'])) {
                return AreaEnum::MN_TRA_VINH;
            } elseif (in_array($dai, ['Tây Ninh'])) {
                return AreaEnum::MN_TAY_NINH;
            } elseif (in_array($dai, ['An Giang'])) {
                return AreaEnum::MN_AN_GIANG;
            } elseif (in_array($dai, ['Long An'])) {
                return AreaEnum::MN_LONG_AN;
            } elseif (in_array($dai, ['Bình Phước'])) {
                return AreaEnum::MN_BINH_PHUOC;
            } elseif (in_array($dai, ['Hậu Giang'])) {
                return AreaEnum::MN_HAU_GIANG;
            } elseif (in_array($dai, ['Tiền Giang'])) {
                return AreaEnum::MN_TIEN_GIANG;
            } elseif (in_array($dai, ['Kiên Giang'])) {
                return AreaEnum::MN_KIEN_GIANG;
            } elseif (in_array($dai, ['Đà Lạt'])) {
                return AreaEnum::MN_LAM_DONG;
            } elseif (in_array($dai, ['Đồng Nai'])) {
                return AreaEnum::MN_DONG_NAI;
            } elseif (in_array($dai, ['Cần Thơ'])) {
                return AreaEnum::MN_CAN_THO;
            } elseif (in_array($dai, ['Sóc Trăng'])) {
                return AreaEnum::MN_SOC_TRANG;
            } elseif (in_array($dai, ['Đồng Tháp'])) {
                return AreaEnum::MN_DONG_THAP;
            } elseif (in_array($dai, ['Vũng Tàu'])) {
                return AreaEnum::MN_VUNG_TAU;
            } elseif (in_array($dai, ['Bạc Liêu'])) {
                return AreaEnum::MN_BAC_LIEU;
            } elseif (in_array($dai, ['Cà Mau'])) {
                return AreaEnum::MN_CA_MAU;
            } elseif (in_array($dai, ['Bến Tre'])) {
                return AreaEnum::MN_BEN_TRE;
            } elseif (in_array($dai, ['Bình Thuận'])) {
                return AreaEnum::MN_BINH_THUAN;
            }
        } else {
            if (in_array($dai, ['Bình Định'])) {
                return AreaEnum::MT_BINH_DINH;
            } elseif (in_array($dai, ['Đắk Lắk'])) {
                return AreaEnum::MT_DAK_LAK;
            } elseif (in_array($dai, ['Đà Nẵng'])) {
                return AreaEnum::MT_DA_NANG;
            } elseif (in_array($dai, ['Đắk Nông'])) {
                return AreaEnum::MT_DAK_NONG;
            } elseif (in_array($dai, ['Thừa T. Huế', 'Huế'])) {
                return AreaEnum::MT_THUA_THIEN_HUE;
            } elseif (in_array($dai, ['Gia Lai'])) {
                return AreaEnum::MT_GIA_LAI;
            } elseif (in_array($dai, ['Khánh Hòa'])) {
                return AreaEnum::MT_KHANH_HOA;
            } elseif (in_array($dai, ['Kon Tum'])) {
                return AreaEnum::MT_KON_TUM;
            } elseif (in_array($dai, ['Ninh Thuận'])) {
                return AreaEnum::MT_NINH_THUAN;
            } elseif (in_array($dai, ['Phú Yên'])) {
                return AreaEnum::MT_PHU_YEN;
            } elseif (in_array($dai, ['Quảng Bình'])) {
                return AreaEnum::MT_QUANG_BINH;
            } elseif (in_array($dai, ['Quảng Trị'])) {
                return AreaEnum::MT_QUANG_TRI;
            } elseif (in_array($dai, ['Quảng Nam'])) {
                return AreaEnum::MT_QUANG_NAM;
            } elseif (in_array($dai, ['Quảng Ngãi'])) {
                return AreaEnum::MT_QUANG_NGAI;
            }
        }
    }

    function swap1(&$a, &$b)
    {
        $temp = $a;
        $a = $b;
        $b = $temp;
    }


    public function calculatorPrizeLimit($type,
                                         $so_danh,
                                         $so_tien_danh,
                                         $array_dai,
                                         $dai,
                                         $customer_id,
                                         $array_kq,
                                         $array_full_kq,
                                         $status,
                                         $type_detail,
                                         $date,
                                         $pairDaArr,
                                         $channel = null)
    {

        $ration_user = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::User)->where('region_id', $dai)->get()->first();
        $ration_customer = Ration::query()->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::Customer)->where('region_id', $dai)->get()->first();

        $get_dai = $this->getALLSide($array_dai, $dai);
        $so_trung = [];
        $so_trung_limit = [];
        $type_trung_and_money = [];
        $type_trung_and_money_limit = [];
        $array_result = [];
        $sum_2_total_limit = 0;
        $sum_2_total = 0;
        $sum_3_total = 0;
        $sum_4_total = 0;
        $hai_so = 0;
        $ba_so = 0;
        $bon_so = 0;
        $DD = 0;
        $XC = 0;
        $DATHANG = 0;
        $DATHANG_limit = 0;
        $DAXIEN = 0;
        $DAXIEN_limit = 0;
        $tien_trung_limit = 0;
        $tien_trung_2nd_limit = 0;
        $string_so_trung_limit = '';
        $so_trung_limit = [];

        $sum_2_fight_limit = 0;
        $sum_2_fight = 0;
        $sum_3_fight = 0;
        $sum_4_fight = 0;

        if ($dai != 2)
            $list_area = array_map(function ($d) use ($dai) {
                return $this->getDaiFromFullname($d['channel'], $dai);
            }, $array_full_kq);
        else $list_area = [AreaEnum::MB];
        $dai_hien_tai = $this->getDaiFromFullname($channel, $dai);
        $result_tung_dai = array_fill_keys($list_area, [
            'hai_so_lai' => 0,
            'ba_so_lai' => 0,
            'bon_so_lai' => 0,
            'dau_duoi_lai' => 0,
            'bao_lo_2_so_lai' => 0,
            'bao_lo_3_so_lai' => 0,
            'bao_lo_4_so_lai' => 0,
            'xiu_chu_lai' => 0,
            'da_thang_lai' => 0,
            'da_xien_lai' => 0,
            'total_lai' => 0,
            'so_trung' => [],
            'tra_lai' => 0,
        ]);
        if ($dai != 2)
            $list_area = array_map(function ($d) use ($dai, $date) {
                return $this->convertArea($d, $dai, $date);
            }, (new TicketService())->convertMultipleArea($array_dai, $date, $dai));
        else $list_area = [AreaEnum::MB];

        foreach ($type as $type_each) {
            $val_sw = $type_each;
            switch ($val_sw) {
                case TypeFight::DAU:
                    $sum_dau_2 = 0;
                    $sum_xc_3 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(0, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 3) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq['t6'] as $each) {
                                    if (substr($each, -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = substr($each, -3);
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq['db'], -3);
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }

                            } else {

                                if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = substr($so_danh_each, -3);
                                    $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }


                            }


                        } elseif (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                    }
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t8"], -2);
                                    $type_trung_and_money[] = $type_detail[0] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                                }
                            }
                        }
                    }

                    $sum_2_total += $sum_dau_2 * $ration_user->dau_duoi_ration;

                    $sum_2_fight += $sum_dau_2;
                    $DD += $sum_dau_2;

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $sum_dau_2 * $ration_user->dau_duoi_ration;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_dau_2 * $ration_user->dau_duoi_ration + $sum_xc_3 * $ration_user->xiu_chu_ration;
                    break;
                case TypeFight::DUOI:
                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(1, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if (substr($array_kq["db"], -2) === $so_danh_each) {
                                $sum_duoi_2 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -2);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = $so_danh_each . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi_ration];
                            }

                        } elseif (strlen($so_danh_each) == 3) {


                            if (substr($array_kq["db"], -3) === $so_danh_each) {
                                $sum_duoi_3 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -3);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = $so_danh_each . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];

                            }

                        } elseif (strlen($so_danh_each) == 4) {

                            if (substr($array_kq["db"], -4) === $so_danh_each) {
                                $sum_duoi_4 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -4);
                                $type_trung_and_money[] = $type_detail[1] . $tien_danh . "n";

                                $key_area = $so_danh_each . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];
                            }

                        }
                    }

                    $sum_2_total += $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $sum_3_total += $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $sum_4_total += $sum_duoi_4 * $ration_user->bon_so_ration;

                    $sum_2_fight += $sum_duoi_2;
                    $sum_3_fight += $sum_duoi_3;
                    $sum_4_fight += $sum_duoi_4;

                    $DD += $sum_duoi_2;
                    $XC += $sum_duoi_3;
                    $bon_so += $sum_duoi_4;

                    $hai_so_area = $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $bon_so_area = $sum_duoi_4 * $ration_user->bon_so_ration;

                    $result_tung_dai[$dai_hien_tai]["bon_so_lai"] += $bon_so_area;
                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area + $bon_so_area;
                    break;
                case TypeFight::DAU_DUOI:

                    $sum_dau_duoi = 0;
                    $sum_xc_3 = 0;

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(2, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 3) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }
                            if ($dai == 2) {
                                foreach ($array_kq['t6'] as $each) {
                                    if (substr($each, -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;

                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }

                                }
                                if (substr($array_kq['db'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;

                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            } else {


                                if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }

                                if (substr($array_kq['db'], -3) === $so_danh_each) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }


                        } elseif (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_duoi += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi];
                                    }

                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi];
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi];
                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[2] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->dau_duoi];
                                }
                            }
                        }
                    }

                    $sum_2_total += $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_duoi;
                    $DD += $sum_dau_duoi;

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $hai_so_area = $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;

                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area;

                    break;
                case TypeFight::BAO_LO:
                    $sum_3 = 0;
                    $sum_4 = 0;
                    $sum_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(3, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            foreach (($this->arrayKq($array_kq, 2)) as $value) {
                                if (substr($value, -2) === $so_danh_each) {

                                    $sum_2 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                            }

                        } else if (strlen($so_danh_each) == 3) {
                            foreach (($this->arrayKq($array_kq, 3)) as $value) {
                                if (substr($value, -3) === $so_danh_each) {
                                    $sum_3 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                }
                            }

                        } elseif (strlen($so_danh_each) == 4) {

                            foreach (($this->arrayKq($array_kq, 4)) as $value) {

                                if (substr($value, -4) === $so_danh_each) {
                                    $sum_4 += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[3] . $tien_danh . "n";

                                    $key_area = $so_danh_each;
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];
                                }

                            }
                        }
                    }

                    $sum_2_total += $sum_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_3 * $ration_user->ba_so_ration;
                    $sum_4_total += $sum_4 * $ration_user->bon_so_ration;

                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;

                    $sum_2_fight += $sum_2;
                    $sum_3_fight += $sum_3;
                    $sum_4_fight += $sum_4;

//                    $hai_so_area = $sum_2 * $ration_user->dau_duoi_ration;
//                    $ba_so_area = $sum_3 * $ration_user->xiu_chu_ration;
//                    $bon_so_area = $sum_4 * $ration_user->bon_so_ration;

//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_2_total;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"] += $sum_3_total;
//                    $result_tung_dai[$dai_hien_tai]["bon_so_lai"] += $sum_4_total;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;
//                    $result_tung_dai[$dai_hien_tai]["bao_lo_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_4_so_lai"] += $sum_4_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;


                    break;
                case  TypeFight::XIU_CHU:
                    $sum_xc_3 = 0;
                    $sum_dau_duoi = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(4, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($array_kq["db"], -2) === $so_danh_each) {
                                        $sum_dau_duoi += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                                if (substr($each, -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.a';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                                if (substr($array_kq["db"], -2) === $so_danh_each) {
                                    $sum_dau_duoi += $tien_danh;
                                    $so_trung[] = $so_danh_each;
                                    $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }
                        } else
                            if (strlen($so_danh_each) == 3) {
                                if ($dai == 2) {
                                    foreach ($array_kq['t6'] as $each) {
                                        if (substr($each, -3) === $so_danh_each) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = $so_danh_each;
                                            $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                            $key_area = $so_danh_each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }
                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                } else {
                                    if (substr($array_kq['t7'], -3) === $so_danh_each) {

                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                    if (substr($array_kq['db'], -3) === $so_danh_each) {

                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $so_danh_each;
                                        $type_trung_and_money[] = $type_detail[4] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $sum_2_total += $sum_dau_duoi * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_duoi;
                    $DD += $sum_dau_duoi;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;

                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAU:
                    $sum_xc_dau_3 = 0;
                    $sum_dau_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(5, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(5, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            if ($dai == 2) {
                                foreach ($array_kq["t7"] as $each) {
                                    if (substr($each, -2) === $so_danh_each) {
                                        $sum_dau_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";
                                    }
                                }
                            } else {
                                if (substr($array_kq["t8"], -2) === $so_danh_each) {
                                    $sum_dau_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t8"], -2);
                                    $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";
                                }
                            }


                        } else
                            if (strlen($so_danh_each) == 3) {
                                if ($dai == 2) {
                                    foreach ($array_kq['t6'] as $each) {
                                        if (substr($each, -3) === $so_danh_each) {
                                            $sum_xc_dau_3 += $tien_danh;
                                            $so_trung[] = substr($each, -3);
                                            $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";

                                            $key_area = $so_danh_each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }
                                    }
                                } else {
                                    if (substr($array_kq['t7'], -3) === $so_danh_each) {
                                        $sum_xc_dau_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq['t7'], -3);
                                        $type_trung_and_money[] = $type_detail[5] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }

                            }
                    }

                    $sum_3_total += $sum_xc_dau_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_dau_3;
                    $sum_3_fight += $sum_xc_dau_3;

                    $sum_2_total += $sum_dau_2 * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_dau_2;
                    $DD += $sum_dau_2;

                    $ba_so_area = $sum_xc_dau_3 * $ration_user->xiu_chu_ration;

                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DUOI:
                    $sum_xc_duoi = 0;
                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(6, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(6, $type);
                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];
                            }

                            if (substr($array_kq["db"], -2) === $so_danh_each) {
                                $sum_duoi_2 += $tien_danh;
                                $so_trung[] = substr($array_kq["db"], -2);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";
                            }


                        } elseif (strlen($so_danh_each) == 3) {
                            if (substr($array_kq['db'], -3) === $so_danh_each) {
                                $sum_duoi_3 += $tien_danh;
                                $so_trung[] = substr($array_kq['db'], -3);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";

                                $key_area = $so_danh_each . '.u';
                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                            }
                        } elseif (strlen($so_danh_each) == 4) {
                            if (substr($array_kq['db'], -4) === $so_danh_each) {
                                $sum_duoi_4 += $tien_danh;
                                $so_trung[] = substr($array_kq['db'], -4);
                                $type_trung_and_money[] = $type_detail[6] . $tien_danh . "n";
                            }
                        }
                    }
                    $sum_3_total += $sum_duoi_3 * $ration_user->xiu_chu_ration;
                    $sum_4_total += $sum_duoi_4 * $ration_user->xiu_chu_ration;

                    $XC += $sum_duoi_3;
                    $sum_3_fight += $sum_duoi_3;

                    $sum_4_fight += $sum_duoi_4;

                    $sum_2_total += $sum_duoi_2 * $ration_user->dau_duoi_ration;
                    $sum_2_fight += $sum_duoi_2;
                    $DD += $sum_duoi_2;

                    $ba_so_area = $sum_duoi_3 * $ration_user->xiu_chu_ration;

                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $ba_so_area;
                    break;
                case TypeFight::DA:
                    if ((array_key_exists("7", $type_detail) && $type_detail[7] == "da" || $type_detail[7] == "dv")
                        && count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                        if ((count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                            if ($status == 0) {
                                $sum_da_xien_2 = 0;
                                $sum_da_xien_2_limit = 0;
                                $string_da_trung = "";
                                $string_da_trung_limit = "";
                                $get_temp_soda_1 = "";

                                $flag_change_calculate = false;
                                if (count($so_tien_danh) >= 2) {
                                    $key = array_search(7, $type);
                                    $tien_danh = $so_tien_danh[$key];
                                } else {
                                    $tien_danh = $so_tien_danh[0];
                                }
                                $array_temp_list_kq = [];
                                if ($get_dai[0] == 3) {
                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);

                                    $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                    $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);

                                } elseif ($get_dai[0] == 4) {

                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                    $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);

                                    $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                    $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);
                                    $array_temp_list_kq[2][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                                } elseif ($get_dai[0] == 5) {

                                    $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                    $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                    $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                                    $array_temp_list_kq[3] = $this->arrayKq($array_full_kq[3]['result'], 2);

                                    $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                    $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);
                                    $array_temp_list_kq[2][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                                    $array_temp_list_kq[3][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                                } else {
                                    $array_merge_multiple = null;

                                    foreach ($array_full_kq as $result_item) {
                                        $l = 0;
                                        foreach ($array_dai as $side) {
                                            if ($result_item['channel'] == 'Thừa T. Huế') {
                                                $result_item['channel'] = 'Huế';
                                            }
                                            if ($this->getSideToCheck($side, $dai, $date) == $result_item['channel']) {
                                                if ($array_merge_multiple == null) {
                                                    $array_merge_multiple = $this->arrayKq($result_item['result'], 2);

                                                }
                                                $array_temp_list_kq[$l] = $this->arrayKq($result_item['result'], 2);
                                                $array_merge_multiple = array_merge($this->arrayKq($result_item['result'], 2), $array_merge_multiple);
                                                $array_temp_list_kq[$l][] = $this->convertShortDaiName($result_item['channel']);
                                            }

                                            $l++;
                                        }

                                    }
                                }
                                $array_temp_list_kq = array_map('array_filter', $array_temp_list_kq);
                                foreach ($get_dai as $dai_detail) {

                                    if ($dai_detail == 0 || $dai_detail == 2) {
                                        if (isset(array_count_values($get_dai)[0]) && array_count_values($get_dai)[0] > 2 || isset(array_count_values($get_dai)[2]) && array_count_values($get_dai)[2] > 2) {
                                            $flag_change_calculate = true;
                                        }
                                    } else if ($dai_detail == 4) {
                                        $flag_change_calculate = true;
                                    } else if ($dai_detail == 5) {
                                        $flag_change_calculate = true;
                                    }
                                }
                                $array_kq_temp = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                for ($i = 0; $i < count($so_danh) - 1; $i++) {
                                    for ($j = $i + 1; $j < count($so_danh); $j++) {

                                        if ($flag_change_calculate) {

                                            if ($get_dai[0] == 4 || sizeof($get_dai) == 3) {

                                                $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                    $counts = array_count_values($array_kq_temp_1);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[0]),
                                                            end($array_temp_list_kq[1]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }

                                                }
                                                $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                    $counts = array_count_values($array_kq_temp_2);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[0]),
                                                            end($array_temp_list_kq[2]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }

                                                }
                                                $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);

                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                    $counts = array_count_values($array_kq_temp_3);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[1]),
                                                            end($array_temp_list_kq[2]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                            } else if ($get_dai[0] == 5 || sizeof($get_dai) == 4) {
                                                $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                    $counts = array_count_values($array_kq_temp_1);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[0]),
                                                            end($array_temp_list_kq[1]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                                $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                    $counts = array_count_values($array_kq_temp_2);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[0]),
                                                            end($array_temp_list_kq[2]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                                $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                    $counts = array_count_values($array_kq_temp_3);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[1]),
                                                            end($array_temp_list_kq[2]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                                $array_kq_temp_4 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_4)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_4)) {
                                                    $counts = array_count_values($array_kq_temp_4);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[0]),
                                                            end($array_temp_list_kq[3]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                                $array_kq_temp_5 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_5)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_5)) {
                                                    $counts = array_count_values($array_kq_temp_5);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[1]),
                                                            end($array_temp_list_kq[3]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                                $array_kq_temp_6 = array_merge($array_temp_list_kq[2], $array_temp_list_kq[3]);
                                                if (in_array(substr($so_danh[$i], -2), $array_kq_temp_6)
                                                    && in_array(substr($so_danh[$j], -2), $array_kq_temp_6)) {
                                                    $counts = array_count_values($array_kq_temp_6);
                                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                    if ($pairCount > 0) {
                                                        $so_danh_j = $so_danh[$j];
                                                        $so_danh_i = $so_danh[$i];
                                                        $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                            $pairDaArr,
                                                            substr($so_danh_i, -2),
                                                            substr($so_danh_j, -2),
                                                            end($array_temp_list_kq[2]),
                                                            end($array_temp_list_kq[3]));

                                                        if ($moneyFromDaXien == $tien_danh) {
                                                            $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                            $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                        } else {
                                                            $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                            $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                            $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                            $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                            $DAXIEN_limit = $sum_da_xien_2_limit;
                                                        }

                                                    }
                                                }
                                            }
                                        } else
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp)) {
                                                $counts = array_count_values($array_kq_temp);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[1]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[7] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                    }
                                }

                                $so_danh_area = collect($so_danh)->sort()->values()->all();
                                $count_so_danh = count($so_danh_area);
                                $array_so_danh = [];
                                for ($i = 0; $i < $count_so_danh; $i++) {
                                    for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                        $array_so_danh[] = [$so_danh_area[$i], $so_danh_area[$j]];
                                    }
                                }
                                $array_temp_list_kq = array_combine($list_area, array_values($array_temp_list_kq));
                                $array_value_counts = [];

                                foreach ($array_temp_list_kq as $key => &$sub_array) {
                                    $array_value_counts[$key] = array_count_values($sub_array);
                                }
                                $newpairDaArr = [];
                                foreach ($pairDaArr as $dai_key => $dai_value) {
                                    $dais = explode(",", $dai_key);
                                    $dai0 = $this->convertArea($dais[0], $dai, $date);
                                    $dai1 = $this->convertArea($dais[1], $dai, $date);
                                    if ($dai0 > $dai1)
                                        $this->swap1($dai0, $dai1);
                                    $new_key = $dai0 . ',' . $dai1;
                                    $newpairDaArr[$new_key] = [];
                                    foreach ($dai_value as $cap_so => $tien_danh_limit) {
                                        $cap_sos = explode(",", $cap_so);
                                        $so0 = $cap_sos[0];
                                        $so1 = $cap_sos[1];
                                        if ($so0 > $so1)
                                            $this->swap1($so0, $so1);
                                        $newpairDaArr[$new_key][$so0 . ',' . $so1] = $tien_danh_limit;
                                    }
                                }
                                foreach ($array_value_counts as $dai1 => $kq1) {
                                    foreach ($array_value_counts as $dai2 => $kq2) {
                                        if ($dai1 < $dai2) {
                                            $limit_array = [];
                                            foreach ($newpairDaArr as $pair_key => $pair_value) {
                                                if ($pair_key == $dai1 . ',' . $dai2) {
                                                    $limit_array = $pair_value;
                                                    break;
                                                }
                                            }
                                            foreach ($array_so_danh as $so) {
                                                $so1 = $so[0];
                                                $so2 = $so[1];

                                                $count_trung = min(((isset($kq1[$so1]) ? $kq1[$so1] : 0)
                                                    + (isset($kq2[$so1]) ? $kq2[$so1] : 0)),
                                                    ((isset($kq1[$so2]) ? $kq1[$so2] : 0)
                                                        + (isset($kq2[$so2]) ? $kq2[$so2] : 0)));
                                                $money_danh = $tien_danh;
                                                if ($count_trung > 0) {
                                                    foreach ($limit_array as $limit_key => $limit_value) {
                                                        if ($limit_key == $so1 . ',' . $so2)
                                                            $money_danh = $limit_value;
                                                    }
                                                    $key = $so1 . '.' . $so2 . '.' . $dai1 . '.' . $dai2;
                                                    $result_tung_dai[$dai1]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $money_danh, 'trung' => $count_trung * $money_danh * $ration_user->da_xien_ration];
                                                    $result_tung_dai[$dai2]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $money_danh, 'trung' => $count_trung * $money_danh * $ration_user->da_xien_ration];
                                                    $result_tung_dai[$dai1]['da_xien_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai2]['da_xien_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai1]['total_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai2]['total_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                    $result_tung_dai[$dai1]['tra_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration / 2;
                                                    $result_tung_dai[$dai2]['tra_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration / 2;
                                                }

                                            }
                                        }
                                    }
                                }

                                $so_trung[] = $string_da_trung;
                                $type_trung_and_money[] = $get_temp_soda_1;

                                $sum_2_total += $sum_da_xien_2 * $ration_user->da_xien_ration;

                                $sum_2_fight += $sum_da_xien_2;

                                $DAXIEN += $sum_da_xien_2;


                            }
                        } else {
                            throw new \Exception("Đá xiên phải đánh từ 2 số hoặc 2 đài trở lên");
                        }
                    } else if (count($so_danh) >= 2) {

                        $sum_da_2 = 0;
                        $sum_da_2_limit = 0;
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(7, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }
                        $count = 0;

                        $string_da_trung = "";
                        $get_temp_soda_1 = "";
                        for ($i = 0; $i < count($so_danh) - 1; $i++) {

                            for ($j = $i + 1; $j < count($so_danh); $j++) {
                                $count_2 = 0;
                                if (in_array(substr($so_danh[$i], -2), $this->arrayKq($array_kq, 2))
                                    &&
                                    in_array(substr($so_danh[$j], -2), $this->arrayKq($array_kq, 2))) {
                                    $counts = array_count_values($this->arrayKq($array_kq, 2));

                                    $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                    $temp_count_value = array_count_values($this->arrayKq($array_kq, 2));
                                    if ($temp_count_value[$so_danh[$i]] > $pairCount || $temp_count_value[$so_danh[$j]] > $pairCount) {
                                        $count++;
                                        $count_2++;
                                    }
                                    if ($pairCount > 0) {
                                        $so0 = substr($so_danh[$i], -2);
                                        $so1 = substr($so_danh[$j], -2);
                                        if ($so0 > $so1) $this->swap1($so0, $so1);
                                        $so_danh_j = $so_danh[$j];
                                        $so_danh_i = $so_danh[$i];
                                        $key_area = $so0 . "." . $so1;
                                        $moneyFromDa = $this->getMoneyFromDa($tien_danh,
                                            $pairDaArr,
                                            substr($so_danh_i, -2),
                                            substr($so_danh_j, -2));
                                        if ($moneyFromDa == $tien_danh) {
                                            if ($ration_user->nua_vong == 1 && $count_2 > 0) {
                                                $sum_da_2 += $moneyFromDa * ($pairCount + 0.5);
                                                $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount + 0.5 . ")";
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $moneyFromDa, 'trung' => $moneyFromDa * ($pairCount + 0.5) * $ration_user->dau_duoi_ration];
                                                $result_tung_dai[$dai_hien_tai]["da_thang_lai"] += $moneyFromDa * ($pairCount + 0.5);
                                                $result_tung_dai[$dai_hien_tai]["total_lai"] += $moneyFromDa * ($pairCount + 0.5);
                                            } else {
                                                $sum_da_2 += $moneyFromDa * $pairCount;
                                                $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";

                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $moneyFromDa, 'trung' => $moneyFromDa * ($pairCount) * $ration_user->dau_duoi_ration];
                                                $result_tung_dai[$dai_hien_tai]["da_thang_lai"] += $moneyFromDa * ($pairCount);
                                                $result_tung_dai[$dai_hien_tai]["total_lai"] += $moneyFromDa * ($pairCount);
                                            }

                                            $get_temp_soda_1 = $type_detail[7] . $moneyFromDa . "n";
                                        } else {

                                            if ($ration_user->nua_vong == 1 && $count_2 > 0) {
                                                $sum_da_2_limit += $moneyFromDa * ($pairCount + 0.5);
                                                $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount + 0.5 . ")";
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $moneyFromDa, 'trung' => $moneyFromDa * ($pairCount + 0.5) * $ration_user->dau_duoi_ration];
                                                $result_tung_dai[$dai_hien_tai]["da_thang_lai"] += $moneyFromDa * ($pairCount + 0.5);
                                                $result_tung_dai[$dai_hien_tai]["total_lai"] += $moneyFromDa * ($pairCount + 0.5);
                                            } else {
                                                $sum_da_2_limit += $moneyFromDa * $pairCount;
                                                $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";

                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $moneyFromDa, 'trung' => $moneyFromDa * ($pairCount) * $ration_user->dau_duoi_ration];
                                                $result_tung_dai[$dai_hien_tai]["da_thang_lai"] += $moneyFromDa * ($pairCount);
                                                $result_tung_dai[$dai_hien_tai]["total_lai"] += $moneyFromDa * ($pairCount);
                                            }

                                            $type_trung_and_money_limit[] = $type_detail[7] . $moneyFromDa . "n";

                                            $sum_2_total_limit = $sum_da_2_limit * $ration_user->da_thang_ration;

                                            $sum_2_fight_limit = $sum_da_2_limit;

                                            $DATHANG_limit = $sum_da_2_limit;
                                        }

                                    }


                                }
                            }
                        }


                        $so_trung[] = $string_da_trung;
                        $type_trung_and_money[] = $get_temp_soda_1;

                        $sum_2_total += $sum_da_2 * $ration_user->da_thang_ration;

                        $sum_2_fight += $sum_da_2;

                        $DATHANG += $sum_da_2;
                        // $hai_so_area = $sum_da_2 * $ration_user->da_thang_ration;
                        // $result_tung_dai[$dai_hien_tai]["da_thang_lai"]+=$hai_so_area;
                        // $result_tung_dai[$dai_hien_tai]["total_lai"]+=$hai_so_area;

                    } else {
                        throw new \Exception("Đá phải đánh từ 2 số trở lên");
                    }

                    break;
                case TypeFight::DA_XIEN:

                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                        if ($status == 0) {
                            $sum_da_xien_2 = 0;
                            $sum_da_xien_2_limit = 0;
                            $string_da_trung = "";
                            $string_da_trung_limit = "";
                            $get_temp_soda_1 = "";

                            $flag_change_calculate = false;
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(7, $type);
                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];
                            }
                            $array_temp_list_kq = [];
                            if ($get_dai[0] == 3) {
                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);

                                $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);

                            } elseif ($get_dai[0] == 4) {

                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);

                                $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);
                                $array_temp_list_kq[2][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                            } elseif ($get_dai[0] == 5) {

                                $array_temp_list_kq[0] = $this->arrayKq($array_full_kq[0]['result'], 2);
                                $array_temp_list_kq[1] = $this->arrayKq($array_full_kq[1]['result'], 2);
                                $array_temp_list_kq[2] = $this->arrayKq($array_full_kq[2]['result'], 2);
                                $array_temp_list_kq[3] = $this->arrayKq($array_full_kq[3]['result'], 2);

                                $array_temp_list_kq[0][] = $this->convertShortDaiName($array_full_kq[0]['channel']);
                                $array_temp_list_kq[1][] = $this->convertShortDaiName($array_full_kq[1]['channel']);
                                $array_temp_list_kq[2][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                                $array_temp_list_kq[3][] = $this->convertShortDaiName($array_full_kq[2]['channel']);
                            } else {
                                $array_merge_multiple = null;

                                foreach ($array_full_kq as $result_item) {
                                    $l = 0;
                                    foreach ($array_dai as $side) {
                                        if ($result_item['channel'] == 'Thừa T. Huế') {
                                            $result_item['channel'] = 'Huế';
                                        }
                                        if ($this->getSideToCheck($side, $dai, $date) == $result_item['channel']) {
                                            if ($array_merge_multiple == null) {
                                                $array_merge_multiple = $this->arrayKq($result_item['result'], 2);

                                            }
                                            $array_temp_list_kq[$l] = $this->arrayKq($result_item['result'], 2);
                                            $array_merge_multiple = array_merge($this->arrayKq($result_item['result'], 2), $array_merge_multiple);
                                            $array_temp_list_kq[$l][] = $this->convertShortDaiName($result_item['channel']);
                                        }

                                        $l++;
                                    }

                                }
                            }
                            $array_temp_list_kq = array_map('array_filter', $array_temp_list_kq);

                            foreach ($get_dai as $dai_detail) {

                                if ($dai_detail == 0 || $dai_detail == 2) {
                                    if (isset(array_count_values($get_dai)[0]) && array_count_values($get_dai)[0] > 2 || isset(array_count_values($get_dai)[2]) && array_count_values($get_dai)[2] > 2) {
                                        $flag_change_calculate = true;
                                    }
                                } else if ($dai_detail == 4) {
                                    $flag_change_calculate = true;
                                } else if ($dai_detail == 5) {
                                    $flag_change_calculate = true;
                                }
                            }
                            $array_kq_temp = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                            for ($i = 0; $i < count($so_danh) - 1; $i++) {
                                for ($j = $i + 1; $j < count($so_danh); $j++) {

                                    if ($flag_change_calculate) {

                                        if ($get_dai[0] == 4 || sizeof($get_dai) == 3) {

                                            $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                $counts = array_count_values($array_kq_temp_1);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[1]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }

                                            }
                                            $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                $counts = array_count_values($array_kq_temp_2);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[2]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }

                                            }
                                            $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);

                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                $counts = array_count_values($array_kq_temp_3);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;

                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[1]),
                                                        end($array_temp_list_kq[2]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                        } else if ($get_dai[0] == 5 || sizeof($get_dai) == 4) {
                                            $array_kq_temp_1 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[1]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_1)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_1)) {
                                                $counts = array_count_values($array_kq_temp_1);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[1]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                            $array_kq_temp_2 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_2)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_2)) {
                                                $counts = array_count_values($array_kq_temp_2);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[2]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                            $array_kq_temp_3 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[2]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_3)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_3)) {
                                                $counts = array_count_values($array_kq_temp_3);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[1]),
                                                        end($array_temp_list_kq[2]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                            $array_kq_temp_4 = array_merge($array_temp_list_kq[0], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_4)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_4)) {
                                                $counts = array_count_values($array_kq_temp_4);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[0]),
                                                        end($array_temp_list_kq[3]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                            $array_kq_temp_5 = array_merge($array_temp_list_kq[1], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_5)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_5)) {
                                                $counts = array_count_values($array_kq_temp_5);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[1]),
                                                        end($array_temp_list_kq[3]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                            $array_kq_temp_6 = array_merge($array_temp_list_kq[2], $array_temp_list_kq[3]);
                                            if (in_array(substr($so_danh[$i], -2), $array_kq_temp_6)
                                                && in_array(substr($so_danh[$j], -2), $array_kq_temp_6)) {
                                                $counts = array_count_values($array_kq_temp_6);
                                                $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                                if ($pairCount > 0) {
                                                    $so_danh_j = $so_danh[$j];
                                                    $so_danh_i = $so_danh[$i];
                                                    $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                        $pairDaArr,
                                                        substr($so_danh_i, -2),
                                                        substr($so_danh_j, -2),
                                                        end($array_temp_list_kq[2]),
                                                        end($array_temp_list_kq[3]));

                                                    if ($moneyFromDaXien == $tien_danh) {
                                                        $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                        $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                    } else {
                                                        $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                        $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                        $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                        $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                        $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                        $DAXIEN_limit = $sum_da_xien_2_limit;
                                                    }

                                                }
                                            }
                                        }
                                    } else
                                        if (in_array(substr($so_danh[$i], -2), $array_kq_temp)
                                            && in_array(substr($so_danh[$j], -2), $array_kq_temp)) {
                                            $counts = array_count_values($array_kq_temp);
                                            $pairCount = isset($counts[substr($so_danh[$i], -2)]) && isset($counts[substr($so_danh[$j], -2)]) ? min($counts[substr($so_danh[$i], -2)], $counts[substr($so_danh[$j], -2)]) : 0;
                                            if ($pairCount > 0) {
                                                $so_danh_j = $so_danh[$j];
                                                $so_danh_i = $so_danh[$i];
                                                $moneyFromDaXien = $this->getMoneyFromDaXien($tien_danh,
                                                    $pairDaArr,
                                                    substr($so_danh_i, -2),
                                                    substr($so_danh_j, -2),
                                                    end($array_temp_list_kq[0]),
                                                    end($array_temp_list_kq[1]));

                                                if ($moneyFromDaXien == $tien_danh) {
                                                    $sum_da_xien_2 += $moneyFromDaXien * $pairCount;
                                                    $string_da_trung .= substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $get_temp_soda_1 = $type_detail[8] . $moneyFromDaXien . "n";
                                                } else {
                                                    $sum_da_xien_2_limit += $moneyFromDaXien * $pairCount;
                                                    $so_trung_limit[] = substr($so_danh[$i], -2) . "." . substr($so_danh[$j], -2) . "(" . $pairCount . ")";
                                                    $type_trung_and_money_limit[] = $type_detail[8] . $moneyFromDaXien . "n";

                                                    $sum_2_total_limit = $sum_da_xien_2_limit * $ration_user->da_xien_ration;

                                                    $sum_2_fight_limit = $sum_da_xien_2_limit;

                                                    $DAXIEN_limit = $sum_da_xien_2_limit;
                                                }

                                            }
                                        }
                                }
                            }


                            $so_danh_area = collect($so_danh)->sort()->values()->all();
                            $count_so_danh = count($so_danh_area);
                            $array_so_danh = [];
                            for ($i = 0; $i < $count_so_danh; $i++) {
                                for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                    $array_so_danh[] = [$so_danh_area[$i], $so_danh_area[$j]];
                                }
                            }
                            $array_temp_list_kq = array_combine($list_area, array_filter(array_values($array_temp_list_kq)));
                            $array_value_counts = [];

                            foreach ($array_temp_list_kq as $key => &$sub_array) {
                                $array_value_counts[$key] = array_count_values($sub_array);
                            }

                            $newpairDaArr = [];
                            foreach ($pairDaArr as $dai_key => $dai_value) {
                                $dais = explode(",", $dai_key);
                                $dai0 = $this->convertArea($dais[0], $dai, $date);
                                $dai1 = $this->convertArea($dais[1], $dai, $date);
                                if ($dai0 > $dai1)
                                    $this->swap1($dai0, $dai1);
                                $new_key = $dai0 . ',' . $dai1;
                                $newpairDaArr[$new_key] = [];
                                foreach ($dai_value as $cap_so => $tien_danh_limit) {
                                    $cap_sos = explode(",", $cap_so);
                                    $so0 = $cap_sos[0];
                                    $so1 = $cap_sos[1];
                                    if ($so0 > $so1)
                                        $this->swap1($so0, $so1);
                                    $newpairDaArr[$new_key][$so0 . ',' . $so1] = $tien_danh_limit;
                                }
                            }
                            foreach ($array_value_counts as $dai1 => $kq1) {
                                foreach ($array_value_counts as $dai2 => $kq2) {
                                    if ($dai1 < $dai2) {
                                        $limit_array = [];
                                        foreach ($newpairDaArr as $pair_key => $pair_value) {
                                            if ($pair_key == $dai1 . ',' . $dai2) {
                                                $limit_array = $pair_value;
                                                break;
                                            }
                                        }
                                        foreach ($array_so_danh as $so) {
                                            $so1 = $so[0];
                                            $so2 = $so[1];

                                            $count_trung = min(((isset($kq1[$so1]) ? $kq1[$so1] : 0)
                                                + (isset($kq2[$so1]) ? $kq2[$so1] : 0)),
                                                ((isset($kq1[$so2]) ? $kq1[$so2] : 0)
                                                    + (isset($kq2[$so2]) ? $kq2[$so2] : 0)));
                                            $money_danh = $tien_danh;
                                            if ($count_trung > 0) {
                                                foreach ($limit_array as $limit_key => $limit_value) {
                                                    if ($limit_key == $so1 . ',' . $so2)
                                                        $money_danh = $limit_value;
                                                }
                                                $key = $so1 . '.' . $so2 . '.' . $dai1 . '.' . $dai2;
                                                $result_tung_dai[$dai1]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $money_danh, 'trung' => $count_trung * $money_danh * $ration_user->da_xien_ration];
                                                $result_tung_dai[$dai2]['so_trung'][$key][] = ['count' => $count_trung, 'danh' => $money_danh, 'trung' => $count_trung * $money_danh * $ration_user->da_xien_ration];
                                                $result_tung_dai[$dai1]['da_xien_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai2]['da_xien_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai1]['total_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai2]['total_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration;
                                                $result_tung_dai[$dai1]['tra_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration / 2;
                                                $result_tung_dai[$dai2]['tra_lai'] += $count_trung * $money_danh * $ration_user->da_xien_ration / 2;
                                            }

                                        }
                                    }
                                }
                            }

                            $so_trung[] = $string_da_trung;
                            $type_trung_and_money[] = $get_temp_soda_1;

                            $sum_2_total += $sum_da_xien_2 * $ration_user->da_xien_ration;

                            $sum_2_fight += $sum_da_xien_2;

                            $DAXIEN += $sum_da_xien_2;

                        }
                    } else {

                        throw new \Exception("Đá xiên phải đánh từ 2 số hoặc 2 đài trở lên");

                    }
                    break;
                case TypeFight::DANH_BAY_LO:
                    $sum_bay_lo_2 = 0;
                    $sum_bay_lo_3 = 0;

                    $sum_tam_lo_2 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(9, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            if ($dai == 2) {

                                if ($dai == 2) {
                                    $number = 0;
                                    foreach ($get_dai as $each) {

                                        if ($each == 1) {
                                            $number += 7;

                                        }

                                    }
                                    if (count($so_tien_danh) >= 2) {
                                        $key = array_search(11, $type);
                                        $tien_danh = $so_tien_danh[$key];

                                    } else {
                                        $tien_danh = $so_tien_danh[0];
                                    }

                                    if (strlen($so_danh_each) == 2) {
                                        if ($so_danh_each === substr($array_kq["db"], -2)) {
                                            $sum_tam_lo_2 += $tien_danh;
                                            $so_trung[] = substr($array_kq["db"], -2);
                                            $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                            $key_area = $so_danh_each . '.7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                        }
                                        foreach ($array_kq["t6"] as $each) {
                                            if ($so_danh_each === substr($each, -2)) {
                                                $sum_tam_lo_2 += $tien_danh;
                                                $so_trung[] = substr($each, -2);
                                                $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                                $key_area = $so_danh_each . '.7l';
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                            }

                                        }

                                        foreach ($array_kq["t7"] as $each) {
                                            if ($so_danh_each === substr($each, -2)) {
                                                $sum_tam_lo_2 += $tien_danh;
                                                $so_trung[] = substr($each, -2);
                                                $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                                $key_area = $so_danh_each . '.7l';
                                                $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                            }

                                        }

                                    }


                                } else {

                                    throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                                }

                            } else {
                                if ($so_danh_each === $array_kq["t8"]) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = $array_kq["t8"];
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["t7"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t7"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                }
                                if ($so_danh_each === substr($array_kq["db"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                                if ($so_danh_each === substr($array_kq["t5"], -2)) {
                                    $sum_bay_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"], -2);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.7l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_bay_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                    }

                                }
                            }


                        } else if (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {

                                if ($so_danh_each === substr($array_kq["db"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }

                                if ($so_danh_each === substr($array_kq["t5"][3], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][3], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                if ($so_danh_each === substr($array_kq["t5"][4], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][4], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                if ($so_danh_each === substr($array_kq["t5"][5], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"][5], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -3)) {
                                        $sum_bay_lo_3 += $tien_danh;
                                        $so_trung[] = substr($each, -3);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    }

                                }
                            } else {
                                if ($so_danh_each === $array_kq["t7"]) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = $array_kq["t7"];
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                if ($so_danh_each === substr($array_kq["t4"][0], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t4"][0], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                if ($so_danh_each === substr($array_kq["t5"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["t5"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                if ($so_danh_each === substr($array_kq["db"], -3)) {
                                    $sum_bay_lo_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -3)) {
                                        $sum_bay_lo_3 += $tien_danh;
                                        $so_trung[] = substr($each, -3);
                                        $type_trung_and_money[] = $type_detail[9] . $tien_danh . "n";

                                    }

                                }
                            }
                        }
                    }
                    $sum_2_total += $sum_bay_lo_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bay_lo_3 * $ration_user->ba_so_ration;

                    $hai_so += $sum_bay_lo_2;
                    $ba_so += $sum_bay_lo_3;


                    $sum_2_fight += $sum_bay_lo_2;
                    $sum_3_fight += $sum_bay_lo_3;


                    $sum_2_total += $sum_tam_lo_2 * $ration_user->hai_so_ration;

                    $hai_so += $sum_tam_lo_2;


                    $sum_2_fight += $sum_tam_lo_2;

//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_2_total;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"] += $sum_3_total;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;
//                    $result_tung_dai[$dai_hien_tai]["bao_lo_lai"] += $sum_2_total + $sum_3_total;

                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;

                    break;
                case TypeFight::BAY_LO_DAO:

                    $sum_bay_lo_dao_2 = 0;

                    $sum_bay_lo_dao_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(10, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            if ($dai == 2) {
                                if ($dai == 2) {
                                    $sum_tam_lo_dao_2 = 0;
                                    $number = 0;
                                    foreach ($get_dai as $each) {
                                        if ($each == 1) {
                                            $number += 7;
                                        }
                                    }
                                    if (count($so_tien_danh) >= 2) {
                                        $key = array_search(12, $type);
                                        $tien_danh = $so_tien_danh[$key];

                                    } else {
                                        $tien_danh = $so_tien_danh[0];
                                    }
                                    if (strlen($so_danh_each) == 2) {
                                        foreach ($this->permute($so_danh_each) as $each_permute) {


                                            if ($each_permute === substr($array_kq["db"], -2)) {
                                                $sum_tam_lo_dao_2 += $tien_danh;
                                                $so_trung[] = substr($array_kq["db"], -2);
                                                $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";
                                            }
                                            foreach ($array_kq["t6"] as $each1) {
                                                if ($each_permute === substr($each1, -2)) {
                                                    $sum_tam_lo_dao_2 += $tien_danh;
                                                    $so_trung[] = substr($each1, -2);
                                                    $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";
                                                }
                                            }
                                            foreach ($array_kq["t7"] as $each2) {
                                                if ($each_permute === substr($each2, -2)) {
                                                    $sum_tam_lo_dao_2 += $tien_danh;
                                                    $so_trung[] = substr($each2, -2);
                                                    $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                                }
                                            }
                                        }
                                    }

                                    $sum_2_total += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;

                                    $hai_so += $sum_tam_lo_dao_2;

                                    $sum_2_fight += $sum_tam_lo_dao_2;

                                } else {
                                    throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                                }
                                break;
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === $array_kq["t8"]) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = $array_kq["t8"];
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];

                                    }
                                    if ($each === substr($array_kq["t7"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t7"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                    }
                                    if ($each === substr($array_kq["db"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                    }
                                    if ($each === substr($array_kq["t5"], -2)) {
                                        $sum_bay_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"], -2);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each === substr($each_, -2)) {
                                            $sum_bay_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each_, -2);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        }

                                    }
                                }
                            }


                        } else if (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each == substr($array_kq["db"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each == substr($array_kq["t5"][3], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][3], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each == substr($array_kq["t5"][4], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][4], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each == substr($array_kq["t5"][5], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"][5], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each == substr($each_, -3)) {
                                            $sum_bay_lo_dao_3 += $tien_danh;
                                            $so_trung[] = substr($each_, -3);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                            $key_area = $each . '.7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                        }

                                    }
                                }
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === $array_kq["t7"]) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = $array_kq["t7"];
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];

                                    }
                                    if ($each === substr($array_kq["t4"][0], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t4"][0], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each === substr($array_kq["t5"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t5"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_bay_lo_dao_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -3);
                                        $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                        $key_area = $each . '.7l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each_) {
                                        if ($each === substr($each_, -3)) {
                                            $sum_bay_lo_dao_3 += $tien_danh;
                                            $so_trung[] = substr($each_, -3);
                                            $type_trung_and_money[] = $type_detail[10] . $tien_danh . "n";

                                            $key_area = $each . '.7l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                        }

                                    }
                                }
                            }


                        }
                    }
                    $sum_2_total += $sum_bay_lo_dao_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bay_lo_dao_3 * $ration_user->ba_so_ration;

                    $hai_so += $sum_bay_lo_dao_2;
                    $ba_so += $sum_bay_lo_dao_3;

//
//                    $sum_2_fight += $sum_bay_lo_dao_2;
//                    $sum_3_fight += $sum_bay_lo_dao_3;
//                    $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $sum_2_total;
//                    $result_tung_dai[$dai_hien_tai]["ba_so_lai"] += $sum_3_total;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;
//                    $result_tung_dai[$dai_hien_tai]["bao_lo_lai"] += $sum_2_total + $sum_3_total;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total;

                    break;
                case TypeFight::TAM_LO:
                    if ($dai == 2) {

                        $sum_tam_lo_2 = 0;


                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(11, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 2) {


                                if ($so_danh_each === substr($array_kq["db"], -2)) {
                                    $sum_tam_lo_2 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -2);
                                    $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                    $key_area = $so_danh_each . '.8l';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                }
                                foreach ($array_kq["t6"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_tam_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }

                                }
                                foreach ($array_kq["t7"] as $each) {
                                    if ($so_danh_each === substr($each, -2)) {
                                        $sum_tam_lo_2 += $tien_danh;
                                        $so_trung[] = substr($each, -2);
                                        $type_trung_and_money[] = $type_detail[11] . $tien_danh . "n";

                                        $key_area = $so_danh_each . '.8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }

                                }

                            }
                        }
                        $sum_2_total += $sum_tam_lo_2 * $ration_user->hai_so_ration;

                        $hai_so += $sum_tam_lo_2;


                        $sum_2_fight += $sum_tam_lo_2;

//                        $hai_so_area = $sum_tam_lo_2 * $ration_user->hai_so_ration;
//                        $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $hai_so_area;

                        $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total;

                    } else {
                        throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                    }

                    break;
                case TypeFight::TAM_LO_DAO:
                    if ($dai == 2) {
                        $sum_tam_lo_dao_2 = 0;


                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(12, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {
                            foreach ($this->permute($so_danh_each) as $each_permute) {


                                if (strlen($so_danh_each) == 2) {
                                    if ($each_permute === substr($array_kq["db"], -2)) {
                                        $sum_tam_lo_dao_2 += $tien_danh;
                                        $so_trung[] = substr($array_kq["db"], -2);
                                        $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                        $key_area = $each_permute . '.8l';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                    foreach ($array_kq["t6"] as $each) {
                                        if ($each_permute === substr($each, -2)) {
                                            $sum_tam_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each, -2);
                                            $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                            $key_area = $each_permute . '.8l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                        }

                                    }
                                    foreach ($array_kq["t7"] as $each) {
                                        if ($each_permute === substr($each, -2)) {
                                            $sum_tam_lo_dao_2 += $tien_danh;
                                            $so_trung[] = substr($each, -2);
                                            $type_trung_and_money[] = $type_detail[12] . $tien_danh . "n";

                                            $key_area = $each_permute . '.8l';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                        }

                                    }
                                }
                            }
                        }
                        $sum_2_total += $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;

                        $hai_so += $sum_tam_lo_dao_2;

                        $sum_2_fight += $sum_tam_lo_dao_2;

//                        $hai_so_area = $sum_tam_lo_dao_2 * $ration_user->hai_so_ration;
//                        $result_tung_dai[$dai_hien_tai]["hai_so_lai"] += $hai_so_area;


                        $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                        $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total;
                    } else {
                        throw new \Exception("Tám lô chỉ đánh ở đài miền bắc");

                    }
                    break;
                case TypeFight::XIU_CHU_DAO_DAU:
                    $sum_xc_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(13, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            throw new \Exception("Xỉu chủ không được đánh 2 số");

                        }
                        if (strlen($so_danh_each) == 3) {

                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    foreach ($array_kq["t6"] as $each_t6) {
                                        if ($each === substr($each_t6, -3)) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = substr($each_t6, -3);
                                            $type_trung_and_money[] = $type_detail[13] . $tien_danh . "n";

                                            $key_area = $each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }

                                    }
                                }

                            } else {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === substr($array_kq["t7"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = substr($array_kq["t7"], -3);
                                        $type_trung_and_money[] = $type_detail[13] . $tien_danh . "n";

                                        $key_area = $each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            }

                        }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAO_DUOI:
                    $sum_xc_3 = 0;
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(14, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            throw new \Exception("Xỉu chủ không được đánh 2 số");
                        }

                        if (strlen($so_danh_each) == 3) {
                            foreach ($this->permute($so_danh_each) as $each) {
                                if ($each === substr($array_kq["db"], -3)) {
                                    $sum_xc_3 += $tien_danh;
                                    $so_trung[] = substr($array_kq["db"], -3);
                                    $type_trung_and_money[] = $type_detail[14] . $tien_danh . "n";

                                    $key_area = $each . '.u';
                                    $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                }
                            }
                        }
                    }
                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;

                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
                    break;
                case TypeFight::XIU_CHU_DAO:
                    $sum_xc_3 = 0;


                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(15, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            throw new \Exception("Xỉu chủ không được đánh 2 số");
                        } elseif (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {
                                foreach ($this->permute($so_danh_each) as $each) {
                                    foreach ($array_kq["t6"] as $each_t7) {
                                        if ($each === substr($each_t7, -3)) {
                                            $sum_xc_3 += $tien_danh;
                                            $so_trung[] = $each;
                                            $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                            $key_area = $each . '.a';
                                            $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                        }
                                    }
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            } else {

                                foreach ($this->permute($so_danh_each) as $each) {
                                    if ($each === substr($array_kq["db"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.u';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                    if ($each === substr($array_kq["t7"], -3)) {
                                        $sum_xc_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[15] . $tien_danh . "n";

                                        $key_area = $each . '.a';
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->xiu_chu_ration];
                                    }
                                }
                            }
                        }
                    }

                    $sum_3_total += $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $XC += $sum_xc_3;

                    $sum_3_fight += $sum_xc_3;
                    $ba_so_area = $sum_xc_3 * $ration_user->xiu_chu_ration;
                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;

                    break;
                case TypeFight::BAO_LO_DAO:
                    $sum_bao_dao_2 = 0;
                    $sum_bao_dao_3 = 0;
                    $sum_bao_dao_4 = 0;

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(16, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            foreach ($this->permute($so_danh_each) as $each) {
                                foreach (($this->arrayKq($array_kq, 2)) as $value) {
                                    if (substr($value, -2) === $each) {

                                        $sum_bao_dao_2 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $so_danh_each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->hai_so_ration];
                                    }
                                }
                            }
                        } else if (strlen($so_danh_each) == 3) {

                            foreach ($this->permute($so_danh_each) as $each) {

                                foreach (($this->arrayKq($array_kq, 3)) as $value) {

                                    if (substr($value, -3) === $each) {

//                                        dd($this->arrayKq($array_kq, 3));
                                        $sum_bao_dao_3 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $so_danh_each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->ba_so_ration];
                                    }
                                }
                            }
                        } else if (strlen($so_danh_each) == 4) {

                            foreach ($this->permute($so_danh_each) as $each) {
                                foreach (($this->arrayKq($array_kq, 4)) as $value) {
                                    if (substr($value, -4) === $each) {

                                        $sum_bao_dao_4 += $tien_danh;
                                        $so_trung[] = $each;
                                        $type_trung_and_money[] = $type_detail[16] . $tien_danh . "n";

                                        $key_area = $so_danh_each;
                                        $result_tung_dai[$dai_hien_tai]['so_trung'][$key_area][] = ['count' => 1, 'danh' => $tien_danh, 'trung' => $tien_danh * $ration_user->bon_so_ration];
                                    }
                                }


                            }
                        }
                    }
                    $sum_2_total += $sum_bao_dao_2 * $ration_user->hai_so_ration;
                    $sum_3_total += $sum_bao_dao_3 * $ration_user->ba_so_ration;
                    $sum_4_total += $sum_bao_dao_4 * $ration_user->bon_so_ration;

                    $hai_so += $sum_bao_dao_2;
                    $ba_so += $sum_bao_dao_3;
                    $bon_so += $sum_bao_dao_4;

                    $sum_2_fight += $sum_bao_dao_2;
                    $sum_3_fight += $sum_bao_dao_3;
                    $sum_4_fight += $sum_bao_dao_4;

//                    $hai_so_area = $sum_bao_dao_2 * $ration_user->dau_duoi_ration;
//                    $ba_so_area = $sum_bao_dao_3 * $ration_user->xiu_chu_ration;
//                    $bon_so_area = $sum_bao_dao_4 * $ration_user->bon_so_ration;

//                    $result_tung_dai[$dai_hien_tai]["bon_so_lai"] += $bon_so_area;
//                    $result_tung_dai[$dai_hien_tai]["dau_duoi_lai"] += $hai_so_area;
//                    $result_tung_dai[$dai_hien_tai]["xiu_chu_lai"] += $ba_so_area;
//                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $hai_so_area + $ba_so_area;


                    $result_tung_dai[$dai_hien_tai]["bao_lo_2_so_lai"] += $sum_2_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_3_so_lai"] += $sum_3_total;
                    $result_tung_dai[$dai_hien_tai]["bao_lo_4_so_lai"] += $sum_4_total;
                    $result_tung_dai[$dai_hien_tai]["total_lai"] += $sum_2_total + $sum_3_total + $sum_4_total;
                    break;
            }
        }

        $tien_trung = ($hai_so * $ration_user->hai_so_ration) + ($ba_so * $ration_user->ba_so_ration)
            + ($bon_so * $ration_user->bon_so_ration) + ($DD * $ration_user->dau_duoi_ration) + ($XC * $ration_user->xiu_chu_ration)
            + ($DATHANG * $ration_user->da_thang_ration) + ($DAXIEN * $ration_user->da_xien_ration);

        $tien_trung_2nd = ($hai_so * $ration_customer->hai_so_ration) + ($ba_so * $ration_customer->ba_so_ration)
            + ($bon_so * $ration_customer->bon_so_ration) + ($DD * $ration_customer->dau_duoi_ration) + ($XC * $ration_customer->xiu_chu_ration)
            + ($DATHANG * $ration_customer->da_thang_ration) + ($DAXIEN * $ration_customer->da_xien_ration);
        $string_so_trung = '';


        $tien_trung_limit = ($DATHANG_limit * $ration_user->da_thang_ration) + ($DAXIEN_limit * $ration_user->da_xien_ration);

        $tien_trung_2nd_limit = ($DATHANG_limit * $ration_customer->da_thang_ration) + ($DAXIEN_limit * $ration_customer->da_xien_ration);
        $string_so_trung = '';

        $array_result_limit = [];

        $array_result_limit['tong_2_so'] = $sum_2_total_limit;
        $array_result_limit['DAXIEN'] = $DAXIEN_limit;
        $array_result_limit['sum_2_fight'] = $sum_2_fight_limit;
        $array_result_limit['tien_trung'] = $tien_trung_limit;
        $array_result_limit['tien_trung_2nd'] = $tien_trung_2nd_limit;
        $array_result_limit['string_so_trung'] = $string_so_trung_limit;
        $array_result_limit['so_trung'] = $so_trung_limit;
        $array_result_limit['loai_so_trung'] = $type_trung_and_money_limit;


        $array_result['tong_2_so'] = $sum_2_total;
        $array_result['tong_3_so'] = $sum_3_total;
        $array_result['tong_4_so'] = $sum_4_total;
        $array_result['hai_so'] = $hai_so;
        $array_result['ba_so'] = $ba_so;
        $array_result['bon_so'] = $bon_so;
        $array_result['DD'] = $DD;
        $array_result['XC'] = $XC;
        $array_result['DATHANG'] = $DATHANG;
        $array_result['DAXIEN'] = $DAXIEN;
        $array_result['sum_2_fight'] = $sum_2_fight;
        $array_result['sum_3_fight'] = $sum_3_fight;
        $array_result['sum_4_fight'] = $sum_4_fight;
        $array_result['tien_trung'] = $tien_trung;
        $array_result['tien_trung_2nd'] = $tien_trung_2nd;
        $array_result['string_so_trung'] = $string_so_trung;
        $array_result['so_trung'] = $so_trung;
        $array_result['loai_so_trung'] = $type_trung_and_money;
        $array_result['tung_dai'] = $result_tung_dai;
        $array_result = array_filter($array_result, function ($value) {
            return $value != 0;
        });
        $array_result_limit = array_filter($array_result_limit, function ($value) {
            return $value != 0;
        });
        return [$array_result, $array_result_limit];
    }

    public function getMoneyFromDa($money, $pairDaArr, $num1, $num2)
    {
        if (isset($pairDaArr[$num1 . ',' . $num2])) {

            return $pairDaArr[$num1 . ',' . $num2];
        } else if (isset($pairDaArr[$num2 . ',' . $num1])) {
            return $pairDaArr[$num2 . ',' . $num1];
        }
        return $money;
    }

    public function getMoneyFromDaXien($money, $pairDaArr, $num1, $num2, $d1, $d2)
    {

        $k1 = $d1 . ',' . $d2;
        $k2 = $num1 . ',' . $num2;
        $k3 = $d2 . ',' . $d1;
        $k4 = $num2 . ',' . $num1;

        if (isset($pairDaArr[$k1][$k2])) {

            return $pairDaArr[$k1][$k2];
        } else if (isset($pairDaArr[$k3][$k4])) {

            return $pairDaArr[$k3][$k4];
        } else if (isset($pairDaArr[$k1][$k4])) {

            return $pairDaArr[$k1][$k4];
        } else if (isset($pairDaArr[$k3][$k2])) {
            return $pairDaArr[$k3][$k2];
        }
        return $money;
    }

    function calculateSumAndString($tien_danh, $pairDaArr, $so_danh_i, $so_danh_j, $array_temp_list_kq_1, $array_temp_list_kq_2, $pairCount, $type_detail)
    {


        $result = [
            'sum_da_xien_2' => 0,
            'string_da_trung' => '',
            'get_temp_soda_1' => '',
            'sum_da_xien_2_limit' => 0,
            'string_da_trung_limit' => '',
            'get_temp_soda_1_limit' => ''
        ];

        $moneyFromDaXien = $this->getMoneyFromDaXien(
            $tien_danh,
            $pairDaArr,
            substr($so_danh_i, -2),
            substr($so_danh_j, -2),
            end($array_temp_list_kq_1),
            end($array_temp_list_kq_2)
        );

        if ($moneyFromDaXien == $tien_danh) {
            $result['sum_da_xien_2'] = $moneyFromDaXien * $pairCount;


            $result['string_da_trung'] = substr($so_danh_i, -2) . "." . substr($so_danh_j, -2) . "(" . $pairCount . ")";

            $result['get_temp_soda_1'] = $type_detail . $moneyFromDaXien . "n";
        } else {
            $result['sum_da_xien_2_limit'] = $moneyFromDaXien * $pairCount;


            $result['string_da_trung_limit'] = substr($so_danh_i, -2) . "." . substr($so_danh_j, -2) . "(" . $pairCount . ")";

            $result['get_temp_soda_1_limit'] = $type_detail . $moneyFromDaXien . "n";
        }

        return $result;
    }
}
