<?php

namespace App\Services;

use App\Enums\TypeFight;
use App\Models\Limit;

class LimitService extends TicketService
{


    public function getLimit($cus_id, $machine_id)
    {
        $limits = Limit::query()
            ->where('customer_id', $cus_id)
            ->get();

        if ($limits->isEmpty()) {
            $limits = Limit::query()
                ->where('machine_id', $machine_id)
                ->get();
        }
        return $limits;
    }

    function parseMessageLimit($first_str, $dai, $temp_dai, $date_check)
    {

//        dd($first_str);

        if ($dai == 1) {
            $first_str = $this->replaceWithRules($first_str, $this->rules_south);
        } else if ($dai == 3) {
            $first_str = $this->replaceWithRules($first_str, $this->rules_middle);
        } else if ($dai == 2) {
            $first_str = str_ireplace($this->ignoreCharWithMienBac, '', $first_str);
        }

        $first_str = preg_replace("/^phu/", "dp", $first_str);
        $first_str = $this->replaceWithRules($first_str, $this->type_dav);
        $first_str = preg_replace("/(?<=\w)\s?\s?\s?[.|,]\s?\s?\s?(?=\d{2,}|[a-zA-Z])|_|-]+/", " ", $first_str);
        $first_str = preg_replace("/\s*\.\s*\.\s*/", " ", $first_str);

        $str_first = preg_replace("/(?<=n)\s*(?=[a-zA-Z])/", "", $first_str);

        $str_first = trim($this->parseUTF8(preg_replace("/[`|'| |_|\-|:|\/]+/", " ", str_replace("..", " ", strtolower($str_first)))));

        $str_first = preg_replace("/[^a-zA-Z.,0-9\s]/", "", $str_first);

        $str_first = preg_replace("/\.*,{2,}\.*|,?+\.{2,},?+|\.,|,\./", " ", $str_first);
        $str_first = str_replace("-", " ", $str_first);
        $str_first = str_replace("—", " ", $str_first);
        $str_first = preg_replace("/^,/", "", $str_first);

        $str_first = preg_replace("/(\.\s|\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s|\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s\s|\s\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s\s\s|\s\s\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/\.(?![^\.])/", " ", $str_first);
        $str_first = preg_replace("/^[.,]+/", "", $str_first);
        $str_first = preg_replace("/[.,]\s*(?=\s*(7lo|8lo|7l|8l))/", " ", $str_first);

        $length = 0;
        $this->block7loAnd8loType($str_first);
        $str_first = $this->convertTypeLeChan($str_first);


        if ($dai == 1 && preg_match('/(^([^\d]+)|(?<=\s|^)2d |(?<=\s|^)3d |(?<=\s|^)4d |2dai |3dai|4dai)/', ltrim($str_first, " . "), $matches)) {

            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        } else if ($dai == 3 && preg_match('/(^([^\d]+)|(?<=\s|^)2d |(?<=\s|^)3d |2dai |3dai)/', ltrim($str_first, " . "), $matches)) {
            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        } elseif ($dai == 2 && preg_match('/^([^\d]+)/', $str_first, $matches)) {
            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        }

        $index_slice_side = $length;
        if ($index_slice_side == 0 && trim($temp_dai) == '' && $dai != 2) {
            throw new \Exception("Không tìm thấy nhà đài");
        }
        $index_slice_percent = $this->getIndexSlicePercent($str_first);

        $str_sub = substr($str_first, $index_slice_side);

        $str_replace_keo = $str_sub;
//        if (preg_match("/\s\s?\s?\s?\s?k\s\s?\s?\s?\s?/", $str_replace_keo, $matches)) {
//            throw new \Exception("Cú pháp đánh không phù hợp");
//        }

        if (preg_match_all('/(\d+\s*k\s*\d+)|(\d+\s?+keo\s?+\d+)/', $str_replace_keo, $matches)) {
            $temp_str_keo = $str_sub;

            foreach ($matches[0] as $match) {

                $temp_str_keo = $this->convertStringForKeo($temp_str_keo, $match);
            }

            $str_replace_keo = $temp_str_keo;
        }

        if (preg_match_all('/(\d+\s?+kt\s?+\d+)|(\d+\s?+kd\s?+\d+)|(\d+\s?+keotoi\s?+\d+)|(\d+\s?+keoden\s?+\d+)/', $str_replace_keo, $matches)) {
            $temp_str_keo = $str_sub;

            foreach ($matches[0] as $match) {
                $temp_str_keo = $this->convertStringForKeoDen($temp_str_keo, $match);
            }

            $str_replace_keo = $temp_str_keo;
        }

        if (trim($temp_dai) !== '' && $dai != 2 && $index_slice_side == 0) {

            if (preg_match("/^[a-zA-Z]+/", $str_replace_keo, $matches)) {

                throw new \Exception("Không tìm thấy nhà đài");
            }

        }

        $str_replace_keo = preg_replace('/d d/', 'dd', $str_replace_keo);

        $str_2 = preg_replace("/(?<=\d)ng/", "n", preg_replace("/ngan/", "n", $str_replace_keo));
        $this->checkMoneyRedundant($str_2);
        $this->checkTypeNotAllowWithN($str_2);
        /// fix bug 27-10-2023
        $str = preg_replace('/(?<=\d)n|(?<=\d)k|\bng|\bk|(?<=\d)ng|\bn/', ' ', $str_2);
//        $str = $str_2;

        $str_side = substr($str_first, $index_slice_percent, $index_slice_side);

        $array_side = $this->convertArraySide($str_side, $dai, $temp_dai);
        $this->checkAllowSideFightInDay($array_side, $dai, $date_check);
        $all_type = $this->getALLType($dai, $date_check);
        //d\s*(\d+)\s*d
        //change: add pattern for (ex: d 2 d 3) 26-7-2023
        $pattern = "/({$all_type}|d\s*(\d+(ngan|ng|n|,|\.)?\d?+)\s*d)\s*\d+(ngan|ng|n|,|\.)?/i";

        $array_empty = [];

        if (isset($str[0]) && $str[0] != " ") {
            $str = " " . $str;
        }

        //ngay 28/6/2023
        $str = preg_replace('/(?<=\D)\.\s*|(?<=\D)\,\s*/', '', $str);

//        dd($str, preg_replace('/\s+(?=\S)|(?<=\S)\s+/', ' ', $str));
//        $str = preg_replace('/\s+(?=\S)|(?<=\S)\s+/', ' ', $str);


//      dd($pattern);
        $this->checkStringHasOnlyD($str);
        $str = $this->addSpaces($str);

        if (preg_match($pattern, $str, $matches, PREG_OFFSET_CAPTURE)) {

            $end_pos = $matches[0][1] + strlen($matches[0][0]) - 1;
            $end_pos++;
            $last_p = 0;


            if (
//                preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos + 1))
//                &&
            ($matches[sizeof($matches) - 1][0] == "," || $matches[sizeof($matches) - 1][0] == ".")
            ) {
                $end_pos += 1;
            }

            $get_last_index = $this->getLastIndex_2($end_pos, $str, 0, $last_p, $all_type);


            if ($end_pos == $get_last_index) {

                if ($this->checkNumberHasFirstString(trim(substr($str, $get_last_index, $get_last_index))) == 1) {
                    // change 23-08-2023 old code is     $end_pos = $end_pos + $get_last_index +  1 ;
                    $end_pos = $end_pos + $get_last_index;
                }
            } else {
                if ($end_pos != $get_last_index) {
                    $end_pos = $end_pos + $get_last_index;
                }
            }

        } else {
//            echo "Không tìm thấy chuỗi match regex";
            throw new \Exception("Không tìm thấy loại đánh phù hợp");

        }

        $str = preg_replace("/(?<!\S)\.|,(?!\S)/", "", $str);

        if ($dai == 1) {
            $this->catchRegionInline($str, $this->convertArrayToStringRegex(array_merge($this->array_dai_mn, $this->array_dai_mn_group)));
        } else if ($dai == 3) {
            $this->catchRegionInline($str, $this->convertArrayToStringRegex(array_merge($this->array_dai_mt, $this->array_dai_mt_group)));
        }
        $object = [];

        if ($array_side == null && $dai == 2) {

            $object['tp'] = ['hn'];

        } else {
            $object['tp'] = $array_side;
            $this->checkDaiExist($array_side, $dai);
        }

        $this->checkLastStringRedundant($str);

        if (preg_match("/^\s*[a-zA-Z]+/", $str, $matches)) {
            throw new \Exception("Cú pháp đánh không phù hợp");
        }
        $this->validateTypeValidBeforeMoney($str);

        $array_fight = $this->splitString(($str), $end_pos, 0, $array_empty, $all_type);

        $replacements = array(
            "dao xc" => "daoxc",
            "xc dao" => "xcdao",
            "dao b" => "bdao",
            "b dao" => "bdao",
            "daobao" => "bdao",
            "daob" => "bdao",
        );

        $l = 0;

        foreach ($array_fight as $each) {
            $arrayTypeTemp = [];
            foreach ($replacements as $substring => $replacement) {
                $each = str_replace($substring, $replacement, $each);
            }

            $pattern_parse = '/(?<=\d)(?=[a-zA-Z])(?!n|ng|ngan|k)|(?<=[a-zA-Z])(?=\d)(?!n|ng|ngan|k)/';

            $result = preg_split($pattern_parse, $each);
            $joined_string = implode(" ", $result);

            $arr_slice = array_map('trim', explode(" ", trim($joined_string)));

            $arr_slice = array_values(array_filter($arr_slice, 'strlen'));
//dd($arr_slice);
            $array_result = $this->getTypeFight($arr_slice);

            $this->checkDandD($arr_slice);
            $this->checkTypeDuplication($arr_slice);

            if (sizeof($array_result) > 0) {
                $words = $this->parseTypeFight($each);

                $result_type_fight = [];
                $result_type_fight_detail = [];

                foreach ($words as $word) {
                    //18-11-2023 bua` 2 loai da
                    $array_type_key_word = $this->getEnumByName($word);
                    if (isset($array_type_key_word[0])) {
                        $arrayTypeTemp[] = $array_type_key_word[0];
                    }
                    if (in_array(TypeFight::XIU_CHU_DAO, $array_type_key_word)) {

//                        $result_type_fight[] = 0;
                        $result_type_fight[] = TypeFight::XIU_CHU_DAO;
                        $result_type_fight_detail[TypeFight::XIU_CHU_DAO_DAU] = 'xcddau';
                        $result_type_fight_detail[TypeFight::XIU_CHU_DAO_DUOI] = 'xcdduoi';

                    } else
                        if (in_array(TypeFight::DAU_DUOI, $array_type_key_word)) {

//                        $result_type_fight[] = 0;
                            $result_type_fight[] = 2;
                            $result_type_fight_detail[0] = 'dau';
                            $result_type_fight_detail[1] = 'duoi';

                        } else if (in_array(TypeFight::XIU_CHU, $array_type_key_word)) {

//                        $result_type_fight[] = 0;
                            $result_type_fight[] = TypeFight::XIU_CHU;
                            $result_type_fight_detail[TypeFight::XIU_CHU_DAU] = 'xcdau';
                            $result_type_fight_detail[TypeFight::XIU_CHU_DUOI] = 'xcduoi';

                        } else if (array_key_exists(7, $result_type_fight_detail) && $array_type_key_word[0] == 7 && $result_type_fight_detail[7] == "dathang") {
                            $result_type_fight[] = $array_type_key_word[sizeof($array_type_key_word) - 1];
                            $result_type_fight_detail[$array_type_key_word[sizeof($array_type_key_word) - 1] + 1] = $word;
                        } else {

                            $result_type_fight[] = $array_type_key_word[sizeof($array_type_key_word) - 1];
                            $result_type_fight_detail[$array_type_key_word[sizeof($array_type_key_word) - 1]] = $word;
                        }

                }

                if (in_array("da", $words) && in_array(7, $result_type_fight) && array_count_values($result_type_fight)[7] == 2) {

                    unset($result_type_fight[array_search(7, $result_type_fight)]);
                    $result_type_fight = array_values($result_type_fight);
                    $result_type_fight[] = 8;
                    $result_type_fight_detail[8] = "da";

                    $index = array_search("da", $arr_slice);


                    if ($index !== false && array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 1] == "da") {

                        $index_temp = array_search(array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 2], $arr_slice);
                        $this->swap($arr_slice, $index + 1, $index_temp + 1);
                    }

                } elseif (in_array("dv", $words) && in_array(7, $result_type_fight) && array_count_values($result_type_fight)[7] == 2) {
                    unset($result_type_fight[array_search(7, $result_type_fight)]);
                    $result_type_fight = array_values($result_type_fight);
                    $result_type_fight[] = 8;
                    $result_type_fight_detail[8] = "dv";

                    $index = array_search("dv", $arr_slice);


                    if ($index !== false && array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 1] == "dv") {

                        $index_temp = array_search(array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 2], $arr_slice);
                        $this->swap($arr_slice, $index + 1, $index_temp + 1);
                    }
                }

                if (in_array(TypeFight::DA, $result_type_fight) ||
                    in_array(TypeFight::DA_XIEN, $result_type_fight)) {
                    $arrSo = $this->getAllNumber($arr_slice, $array_result[0], $all_type);
                    $isCheck = false;
                    $index = array_search('dx', $arr_slice);
                    if ($this->isNumberForXCD($arrSo) && $index !== false) {
                        $isCheck = true;
                        $arr_slice[$index] = 'xcd';
                        $index2 = array_search(TypeFight::DA_XIEN, $result_type_fight);
                        if ($index2 !== false) {

                            $result_type_fight[$index2] = TypeFight::XIU_CHU_DAO;
                        }
                        $index3 = array_search('dx', $words);
                        $words[$index3] = 'xcd';
                        $result_type_fight_detail[TypeFight::XIU_CHU_DAO] = 'xcd';
                        unset($result_type_fight_detail[TypeFight::DA_XIEN]);

                    } else if (count($arrSo) < 2) {

                        $str_so_err = implode(" ", $arrSo);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 con trở lên");

                    }
                    if (!$isCheck) {
                        $this->checkArrayDa($arrSo);

                    }
                }

                if ($this->checkArrayElements4Digit(array_slice($arr_slice, 0, $array_result[0]))) {

                    $this->checkDulicationTypeFight3And4Digit($arrayTypeTemp, $result_type_fight_detail);
                    foreach (array_slice($arr_slice, 0, $array_result[0]) as $value_4_digit) {
                        if (strlen($value_4_digit) == 4 && (in_array("dau", $words)
                                || !empty(array_intersect(['dauduoi', 'dd', 'dđ', 'đđ', 'đd'], $words)))) {
                            throw new \Exception("Loại đánh Đầu, Đầu đuôi không nhận 4 số");
                        }
                        if (strlen($value_4_digit) == 4) {
                            $number_save = $value_4_digit;
                            $myArray = [
                                ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                ['dui', 'duoi'],
                                ['daodui', 'daoduoi']
                            ];
                            $array_temp_type = [];
                            $count = 0;
                            $index = 0;
                            foreach ($words as $value) {

                                $type_digit_4 = $value;
                                foreach ($myArray as $key => $subArray) {
                                    $keyToRemove = array_search($type_digit_4, $subArray);
                                    if (in_array($value, $array_temp_type)) {
                                        $count++;
                                        break;
                                    }
                                    if ($count == 0) {
                                        if ($keyToRemove !== false) {
                                            if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                $array_temp_type[3] = $value;
                                            } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {

                                                $array_temp_type[16] = $value;
                                            } else if (in_array($value, ['dui', 'duoi'])) {

                                                $array_temp_type[1] = $value;
                                            } else if (in_array($value, ['daodui', 'daoduoi'])) {

                                                $array_temp_type[14] = $value;
                                            }
                                            $index++;
                                            unset($myArray[$key]);
                                            break;
                                        }
                                    }
                                }
                                if ($count != 0) {
                                    break;
                                }
                            }

                            $arr_slice_4_digit = array_values(array_filter($arr_slice, 'strlen'));
                            $index_slice_money = 0;
                            foreach ($array_temp_type as $key => $value) {
                                $object['result'][] = ['so_danh' => [$number_save],
                                    'loai_danh' => [$key],
                                    'loai_danh_detail' => [$key => $value],
                                    'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_4_digit[array_search($value, $arr_slice_4_digit) + 1])]
                                ];
                                $index_slice_money = array_search($value, $arr_slice_4_digit) + 1;
                            }

                            $arr_slice_3_digit = array_slice($arr_slice_4_digit, $index_slice_money + 1);
                            $words_2 = $index != -1 ? array_slice($words, $index) : null;
                            $arr_slice_2_digit = null;
                            if ($words_2 != null) {
                                $count_2 = 0;
                                $array_temp_type_2 = [];
                                $arr_xc_check = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld',
                                    'tieulodao', 'xd', 'dao xc', 'xc dao', 'xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi',
                                    'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi', 'xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'xc', 'x',
                                    'xiu', 'tl', 'tieulo', 'xcdau', 'dauxc', 'xdau', 'xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'
                                ];
                                $arr_dd_check = ['dau', 'dau', 'dui', 'duoi', 'dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
                                $myArray_2 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                    ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'],
                                    ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'],
                                    ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'],
                                    ['xc', 'x', 'xiu', 'tl', 'tieulo'],
                                    ['xcdau', 'dauxc', 'xdau'],
                                    ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'],
                                    ['dau', 'dau'],
                                    ['dui', 'duoi'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                ];
                                foreach ($words_2 as $value) {
                                    $type_digit_3 = $value;
                                    foreach ($myArray_2 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_3, $subArray);
                                        $commonValues = array_intersect($array_temp_type_2, $arr_dd_check);
                                        $commonValues_xc = array_intersect($array_temp_type_2, $arr_xc_check);
                                        if (!empty($commonValues)) {

                                            if (in_array($value, $arr_xc_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues) . " và " . $value . " không đánh chung được");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {

                                            if (in_array($value, $arr_dd_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues_xc) . " và " . $value . " không đánh chung được");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {
                                            if (in_array($value, $arr_dd_check)) {
                                                $index--;
                                                break;
                                            }
                                        }
                                        if (in_array($value, $array_temp_type_2)) {
                                            $count_2++;
                                            break;
                                        }
                                        if ($count_2 == 0) {
                                            if ($keyToRemove !== false) {
                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_2[3] = $value;
                                                } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {
                                                    $array_temp_type_2[16] = $value;
                                                } else if (in_array($value, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'])) {
                                                    $array_temp_type_2[15] = $value;
                                                } else if (in_array($value, ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'])) {
                                                    $array_temp_type_2[14] = $value;
                                                } else if (in_array($value, ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'])) {
                                                    $array_temp_type_2[13] = $value;
                                                } else if (in_array($value, ['xc', 'x', 'xiu', 'tl', 'tieulo'])) {
                                                    $array_temp_type_2[4] = $value;
                                                } else if (in_array($value, ['xcdau', 'dauxc', 'xdau'])) {
                                                    $array_temp_type_2[5] = $value;
                                                } else if (in_array($value, ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'])) {
                                                    $array_temp_type_2[6] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_2[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_2[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_2[2] = $value;
                                                }

                                                if (in_array($value, $arr_dd_check)) {
                                                    unset($myArray_2[2]);
                                                    unset($myArray_2[3]);
                                                    unset($myArray_2[4]);
                                                    unset($myArray_2[5]);
                                                    unset($myArray_2[6]);
                                                    unset($myArray_2[7]);
                                                } else if (in_array($value, $arr_xc_check)) {
                                                    unset($myArray_2[8]);
                                                    unset($myArray_2[9]);
                                                    unset($myArray_2[10]);
                                                } else {
                                                    unset($myArray_2[$key]);
                                                }
                                                $index++;
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_2 != 0) {
                                        break;
                                    }
                                }

                                $index_slice_money_2 = 0;
                                $this->convertDdtoDauDuoiForSliceFight($arr_slice, $array_temp_type_2);
                                $this->convertXCtoXCDDForSliceFight($arr_slice, $array_temp_type_2);
                                $this->convertXCDaotoXCDDDForSliceFight($arr_slice, $array_temp_type_2);
                                foreach ($array_temp_type_2 as $key => $value) {
//                                    $object['result'][] = ['so_danh' => [substr($number_save, 1, 3)],
//                                        'loai_danh' => [$key],
//                                        'loai_danh_detail' => [$key => $value],
//                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_3_digit[array_search($value, $arr_slice_3_digit) + 1])]
//                                    ];
                                    $this->processResult($value, substr($number_save, 1, 3), $arr_slice_3_digit, $key, $object);


                                    $index_slice_money_2 = array_search($value, $arr_slice_3_digit) + 1;
                                }
                                $arr_slice_2_digit = array_slice($arr_slice_3_digit, $index_slice_money_2 + 1);
                            }

                            $words_3 = $index != -1 ? array_slice($words, $index) : null;
                            if ($words_3 != null) {
                                $myArray_3 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['dau', 'dau'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                    ['dui', 'duoi']
                                ];
                                $count_3 = 0;
                                $array_temp_type_3 = [];
                                foreach ($words_3 as $value) {
                                    $type_digit_2 = $value;
                                    foreach ($myArray_3 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_2, $subArray);

                                        if (in_array($value, $array_temp_type_3)) {
                                            $count_3++;
                                            break;
                                        }
                                        if ($count_3 == 0) {
                                            if ($keyToRemove !== false) {
                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_3[3] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_3[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_3[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_3[2] = $value;
                                                }
                                                $index++;
                                                unset($myArray_3[$key]);
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_3 != 0) {
                                        break;
                                    }
                                }

                                $this->convertDdtoDauDuoiForSliceFight($arr_slice, $array_temp_type_3);
                                foreach ($array_temp_type_3 as $key => $value) {
//                                    $object['result'][] = ['so_danh' => [substr($number_save, 2, 2)],
//                                        'loai_danh' => [$key],
//                                        'loai_danh_detail' => [$key => $value],
//                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_2_digit[array_search($value, $arr_slice_2_digit) + 1])]
//                                    ];

                                    $this->processResult($value, substr($number_save, 2, 2), $arr_slice, $key, $object);
                                }
                            }

                        }

                    }
                    $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);

                } else
                    if ($this->checkArrayElements3Digit(array_slice($arr_slice, 0, $array_result[0]))) {
                        $this->checkDao3digit($arr_slice);
                        $this->checkDulicationTypeFight3And4Digit($arrayTypeTemp, $result_type_fight_detail);
                        foreach (array_slice($arr_slice, 0, $array_result[0]) as $value_3_digit)
                            if (strlen($value_3_digit) == 3) {
                                $number_save = $value_3_digit;
                                $myArray_2 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                    ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'],
                                    ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'],
                                    ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'],
                                    ['xc', 'x', 'xiu', 'tl', 'tieulo'],
                                    ['xcdau', 'dauxc', 'xdau'],
                                    ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'],
                                    ['dau', 'dau'],
                                    ['dui', 'duoi'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                    ['baylo'],
                                    ['baylodao', 'daobaylo'],
                                ];
                                $index = 0;
                                $count_2 = 0;
                                $array_temp_type_2 = [];
                                $arr_xc_check = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld',
                                    'tieulodao', 'xd', 'dao xc', 'xc dao', 'xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi',
                                    'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi', 'xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'xc', 'x',
                                    'xiu', 'tl', 'tieulo', 'xcdau', 'dauxc', 'xdau', 'xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'
                                ];
                                $arr_dd_check = ['dau', 'dau', 'dui', 'duoi', 'dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
                                foreach ($words as $value) {
                                    $type_digit_3 = $value;
                                    foreach ($myArray_2 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_3, $subArray);
                                        $commonValues = array_intersect($array_temp_type_2, $arr_dd_check);
                                        $commonValues_xc = array_intersect($array_temp_type_2, $arr_xc_check);
                                        if (!empty($commonValues)) {

                                            if (in_array($value, $arr_xc_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues) . " và " . $value . " không cho phép đánh chung");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {

                                            if (in_array($value, $arr_dd_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues_xc) . " và " . $value . " không cho phép đánh chung");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {
                                            if (in_array($value, $arr_dd_check)) {
                                                $index--;
                                                break;
                                            }
                                        }
                                        if (in_array($value, $array_temp_type_2)) {
                                            $count_2++;
                                            break;
                                        }
                                        if ($count_2 == 0) {
                                            if ($keyToRemove !== false) {

                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_2[3] = $value;
                                                } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {
                                                    $array_temp_type_2[16] = $value;
                                                } else if (in_array($value, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'])) {
                                                    $array_temp_type_2[15] = $value;
                                                } else if (in_array($value, ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'])) {
                                                    $array_temp_type_2[14] = $value;
                                                } else if (in_array($value, ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'])) {
                                                    $array_temp_type_2[13] = $value;
                                                } else if (in_array($value, ['xc', 'x', 'xiu', 'tl', 'tieulo'])) {
                                                    $array_temp_type_2[4] = $value;
                                                } else if (in_array($value, ['xcdau', 'dauxc', 'xdau'])) {
                                                    $array_temp_type_2[5] = $value;
                                                } else if (in_array($value, ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'])) {
                                                    $array_temp_type_2[6] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_2[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_2[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_2[2] = $value;
                                                } else if (in_array($value, ['baylo'])) {
                                                    $array_temp_type_2[9] = $value;
                                                } else if (in_array($value, ['baylodao', 'daobaylo'])) {
                                                    $array_temp_type_2[10] = $value;
                                                }
                                                if (in_array($value, $arr_dd_check)) {
                                                    unset($myArray_2[2]);
                                                    unset($myArray_2[3]);
                                                    unset($myArray_2[4]);
                                                    unset($myArray_2[5]);
                                                    unset($myArray_2[6]);
                                                    unset($myArray_2[7]);
                                                }
                                                if (in_array($value, $arr_xc_check)) {
                                                    unset($myArray_2[8]);
                                                    unset($myArray_2[9]);
                                                    unset($myArray_2[10]);
                                                }
                                                $index++;
                                                unset($myArray_2[$key]);
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_2 != 0) {
                                        break;
                                    }
                                }
                                $index_slice_money_2 = 0;
                                $counts_d = array_count_values($arr_slice);

                                if (isset($counts_d["d"]) && $counts_d["d"] == 2) {
                                    $this->convertDdtoDauDuoiForSliceFight($arr_slice, $array_temp_type_2);
                                    $this->convertXCtoXCDDForSliceFight($arr_slice, $array_temp_type_2);
                                    $this->convertXCDaotoXCDDDForSliceFight($arr_slice, $array_temp_type_2);

                                    $array_convert_dd = $this->convert_array_to_dd($arr_slice);
                                    foreach ($array_temp_type_2 as $key => $value) {
//                                    $object['result'][] = ['so_danh' => [$number_save],
//                                        'loai_danh' => [$key],
//                                        'loai_danh_detail' => [$key => $value],
//                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($array_convert_dd[array_search($value, $array_convert_dd) + 1])]
//                                    ];
                                        $this->processResult($value, $number_save, $array_convert_dd, $key, $object);

                                    }

                                } else {

                                    $this->convertXCtoXCDDForSliceFight($arr_slice, $array_temp_type_2);
                                    // Chèn phần tử vào mảng
                                    $this->convertDdtoDauDuoiForSliceFight($arr_slice, $array_temp_type_2);
                                    $this->convertXCDaotoXCDDDForSliceFight($arr_slice, $array_temp_type_2);

                                    foreach ($array_temp_type_2 as $key => $value) {

//                                    $object['result'][] = ['so_danh' => [$number_save],
//                                        'loai_danh' => [$key],
//                                        'loai_danh_detail' => [$key => $value],
//                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search($value, $arr_slice) + 1])]
//                                    ];
                                        $this->processResult($value, $number_save, $arr_slice, $key, $object);


                                        $index_slice_money_2 = array_search($value, $arr_slice) + 1;
                                    }
                                }


                                $arr_slice_2_digit = array_slice($arr_slice, $index_slice_money_2 + 1);
                                $words_3 = $index != -1 ? array_slice($words, $index) : null;
                                if ($words_3 != null) {
                                    $myArray_3 = [
                                        ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                        ['dau', 'dau'],
                                        ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                        ['dui', 'duoi']
                                    ];
                                    $count_3 = 0;
                                    $array_temp_type_3 = [];
                                    foreach ($words_3 as $value) {
                                        $type_digit_2 = $value;
                                        foreach ($myArray_3 as $key => $subArray) {
                                            $keyToRemove = array_search($type_digit_2, $subArray);
                                            if (in_array($value, $array_temp_type_3)) {
                                                $count_3++;
                                                break;
                                            }
                                            if ($count_3 == 0) {
                                                if ($keyToRemove !== false) {
                                                    if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                        $array_temp_type_3[3] = $value;
                                                    } else if (in_array($value, ['dau', 'dau'])) {
                                                        $array_temp_type_3[0] = $value;
                                                    } else if (in_array($value, ['dui', 'duoi'])) {
                                                        $array_temp_type_3[1] = $value;
                                                    } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                        $array_temp_type_3[2] = $value;
                                                    }
                                                    $index++;
                                                    unset($myArray_3[$key]);
                                                    break;
                                                }
                                            }
                                        }
                                        if ($count_3 != 0) {
                                            break;
                                        }

                                    }
                                    $this->convertDdtoDauDuoiForSliceFight($arr_slice, $array_temp_type_3);


                                    foreach ($array_temp_type_3 as $key => $value) {
//                                        $object['result'][] = ['so_danh' => [substr($number_save, 1, 2)],
//                                            'loai_danh' => [$key],
//                                            'loai_danh_detail' => [$key => $value],
//                                            'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_2_digit[array_search($value, $arr_slice_2_digit) + 1])]
//                                        ];
                                        $this->processResult($value, substr($number_save, 1, 2), $arr_slice, $key, $object);

                                    }
                                }
                            }
                        if (isset($object['result'])) {
                            $this->checkDuplicationNumberInArray($object['result'][$l]['so_danh'], $object['result'][$l]['loai_danh_detail']);

                        }
                        $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);
                    } else {


                        $this->convertDdtoDauDuoi($result_type_fight, $arr_slice);

                        $this->checkDulicationTypeFight($arrayTypeTemp, $result_type_fight_detail);


                        $arrSoDanh = $this->getAllNumber($arr_slice, $array_result[0], $all_type);
                        if (
                            array_filter($arrSoDanh, function ($value) {
                                return strlen($value) == 4;
                            })
                            && (in_array(TypeFight::DAU, $result_type_fight) || in_array(TypeFight::DAU_DUOI, $result_type_fight))
                        ) {
                            throw new \Exception("Loại đánh Đầu, Đầu đuôi không nhận 4 số");
                        }


                        $result_map = ['so_danh' => $arrSoDanh,
                            'loai_danh' => array_unique($result_type_fight),
                            'loai_danh_detail' => $result_type_fight_detail,
                            'so_tien_danh' => $this->getPrice($arr_slice)
                        ];

                        foreach ($result_type_fight as $index => $loai_danh_value) {
                            $type_detail = null;
                            $type_detail[$loai_danh_value] = $result_map['loai_danh_detail'][$loai_danh_value];

                            $object['result'][] = [
                                'so_danh' => $result_map['so_danh'],
                                'loai_danh' => [$loai_danh_value],
                                'loai_danh_detail' => $type_detail,
                                'so_tien_danh' => [$result_map['so_tien_danh'][$index]],
                            ];
                        }
                        $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);
                        if (isset($object['result'])) {

                            $this->checkDuplicationNumberInArray($arrSoDanh, $result_type_fight_detail);

                        }


                    }


            }
            $l++;

        }


        return $object;
    }

    function processResult($value, $number_save, $arr_slice, $key, &$object)
    {

        $xcd_values = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'];
        $dd_values = ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
        $xc_values = ['xc', 'x', 'xiu', 'tl', 'tieulo'];

        if (in_array($value, $xcd_values)) {
            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::XIU_CHU_DAO_DAU],
                'loai_danh_detail' => [TypeFight::XIU_CHU_DAO_DAU => "xcddau"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("xcddau", $arr_slice) + 1])]
            ];

            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::XIU_CHU_DAO_DUOI],
                'loai_danh_detail' => [TypeFight::XIU_CHU_DAO_DUOI => "xcdduoi"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("xcdduoi", $arr_slice) + 1])]
            ];
        } else if (in_array($value, $xc_values)) {
            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::XIU_CHU_DAU],
                'loai_danh_detail' => [TypeFight::XIU_CHU_DAU => "xcdau"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("xcdau", $arr_slice) + 1])]
            ];

            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::XIU_CHU_DUOI],
                'loai_danh_detail' => [TypeFight::XIU_CHU_DUOI => "xcduoi"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("xcduoi", $arr_slice) + 1])]
            ];

        } else if (in_array($value, $dd_values)) {
            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::DAU],
                'loai_danh_detail' => [TypeFight::DAU => "dau"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("dau", $arr_slice) + 1])]
            ];

            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [TypeFight::DUOI],
                'loai_danh_detail' => [TypeFight::DUOI => "duoi"],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search("duoi", $arr_slice) + 1])]
            ];

        } else {
            $object['result'][] = [
                'so_danh' => [$number_save],
                'loai_danh' => [$key],
                'loai_danh_detail' => [$key => $value],
                'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search($value, $arr_slice) + 1])]
            ];
        }


    }
}
