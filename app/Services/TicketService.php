<?php

namespace App\Services;

use App\Enums\AreaEnum;
use App\Enums\FiredTypeEnum;
use App\Enums\TypeFight;
use App\Enums\TypeLimitEnum;
use App\Enums\TypeSideEnum;
use App\Enums\TypeUser;
use App\Jobs\HandleFiredTicketJob;
use App\Models\Ration;
use App\Models\Ticket;
use App\Traits\TraitsUtil;
use Illuminate\Support\Facades\Log;


class TicketService extends BaseService
{
    use TraitsUtil;

    public $type_dav =
        [
            'keo den' => 'keo',
            'xiu chu dao' => 'xcd',
            'daolo' => 'bld',
            'bldao' => 'bld',
            'bl dao' => 'bld',
            "dao b" => "bdao",
            "b dao" => "bdao",
            "baodao" => "bdao",
            "daob" => "bdao",
            'daoxc' => 'xcdao',
            'xc dao' => 'xcdao',
            'dao xc' => 'xcdao',
//            'baylodao' => 'baylo',
            'tlo dao' => 'xcd',
            'tl dao' => 'xcd',
            'xiu chu' => 'xc',
            'xcduidao' => 'xcdduoi',
            'xcduoidao' => 'xcdduoi',
            'xduidao' => 'xcdduoi',
            'xcdaoduoi' => 'xcdduoi',
            'xcdaodui' => 'xcdduoi',
            'xduoidao' => 'xcdduoi',
            'xdaoduoi' => 'xcdduoi',
            'xdaodui' => 'xcdduoi',
            'xdaudao' => 'xcddau',
            'xcdaudao' => 'xcddau',
            'xcdaodau' => 'xcddau',
            'xdaodau' => 'xcddau',
            'xcdao' => 'xcd',
            'tldao' => 'xcd',
            'davong' => 'dv',
            'davog' => 'dv',
            'dvog' => 'dv',
            'dxcdau' => 'xcddau',
            'dav' => 'dv',
            'db' => 'bld',
            'dvx' => 'dx',
            'dxv' => 'dx',
            'bao lo' => 'bl',
//            'lodao' => 'bld',
            'dao bl' => 'bld',
            'dao lo' => 'bld',
            'bao dao' => 'bld',
            'da thang' => 'dathang',
//            '7lo' => 'baylo',
//            '8lo' => 'tamlo',
            'daudui' => 'dd',
            'dauduoi' => 'dd',

            'dau duoi' => 'dd',
            'xc duoi' => 'xcduoi',
            'xc dui' => 'xcduoi',
            'bdao' => 'bld',
            'khc' => 'keo',
            'kht' => 'keo',
            'da xien' => 'dx',
            'xchu' => 'xc',
            'sc' => 'xc',
            'dbao' => 'bld',
            'xiuchu' => 'xc',
            'tlo' => 'xc',
            'daxv' => 'dx',
            'xv' => 'dx',
            'xc dau' => 'xcdau',
            'xdao' => 'xcd',
            'bdl' => 'bld',
            'da x' => 'dx',
            'dlo' => 'bld',
            'den' => 'keo',


        ];
    public $rules_south =
        [
            'kien giang' => 'kg',
            'ctho' => 'ct',
            'ca mau' => 'cm',
            'c mau' => 'cm',
            'dthap' => 'dt',
            'da lat' => 'dl',
            '2 dai' => '2d',
            '3 dai' => '3d',
            '4 dai' => '4d',
            'dai thanh pho' => 'tp',
            'dai dong thap' => 'dt',
            'dai dong nai' => 'dn',
            'dai can tho' => 'ct',
            'dai ben tre' => 'bt',
            'dai an giang' => 'ag',
            'dai soc trang' => 'st',
            'dai binh thuan' => 'bt',
            'ang giang' => 'ag',
            'agiang' => 'ag',
            'agiag' => 'ag',
            'dai da lat' => 'dl',
            'dal' => 'dl',
            'b lieu' => 'bl',

            'binhd' => 'bd',


            'thanh pho' => 'tp',
            'thanhpho' => 'tp',
            'tphcm' => 'tp',
            'tpho' => 'tp',
            'hcm' => 'tp',
//            'tph' => 'tp',

            'cmau' => 'cm',

            'dnai' => 'dn',

            'cth' => 'ct',
            'strang' => 'st',
            's trang' => 'st',
            'str' => 'st',
            'dchanh' => 'dc',

            'dai chanh' => 'dc',
            'dai chinh' => 'dc',
            'dchinh' => 'dc',


            'daichanh' => 'dc',
            'chanh' => 'dc',
            'dphu' => 'dp',
            'daiphu' => 'dp',
            'dai phu' => 'dp',
            'blieu' => 'bl',
            'bli' => 'baclieu',
            'btre' => 'bt',
            'bentre' => 'bt',
            'ben tre' => 'bt',
            'binh phuoc' => 'bp',
            'binhphuoc' => 'bp',
            'bphuoc' => 'bp',

            'bthuan' => 'bt',
            'bduong' => 'bd',
            'binhthuan' => 'bt',
            'binh thuan' => 'bt',
            'cantho' => 'ct',
            'can tho' => 'ct',
            'dlat' => 'dl',
            'dongthap' => 'dt',
            'dong thap' => 'dt',
            'h giang' => 'hg',
            'hgiang' => 'hg',
            'hau giang' => 'hg',
            'haugiang' => 'hg',
            'kgiang' => 'kg',
            'longan' => 'la',
            'long an' => 'la',
            'tninh' => 'tn',
            'tgiang' => 'tg',
            'tvinh' => 'tv',
            'trv' => 'tv',
            'vlong' => 'vl',
            'vinhlong' => 'vl',
            'vinh long' => 'vl',
            'vtau' => 'vt',
            'sb' => 'bd',
            'dth' => 'dongthap',
            'btr' => 'bt',
            'bth' => 'bt',
            'phu' => 'dp',
            'dch' => 'dc',
            'dph' => 'dp',
            'pu' => 'phu',
            'l an' => 'la',
            'l a' => 'la'

        ];

    public $rules_middle =
        [

            '2 dai' => '2d',
            '3 dai' => '3d',


            'p yen' => 'py',
            'dnag' => 'dn',
            'dng' => 'dn',
            'dchanh' => 'dc',
            'dai binh thuan' => 'bt',
            'dai quang ngai' => 'qn',
            'dai binh dinh' => 'bd',
            'dai quang tri' => 'qt',
            'dai quang binh' => 'qb',
            'dai dak nong' => 'dk',
            'dacnong' => 'dk',
            'Qbi' => 'qb',


            'dac nong' => 'dk',


            'dai chanh' => 'dc',
            'dai chinh' => 'dc',
            'dchinh' => 'dc',

            'daichinh' => 'dc',
            'daichanh' => 'dc',
            'chanh' => 'dc',
            'dphu' => 'dp',
            'daiphu' => 'dp',
            'dai phu' => 'dp',
            'binhdinh' => 'bd',
            'binh dinh' => 'bd',
            'dlak' => 'dl',
            'daklak' => 'dl',
            'dak lak' => 'dl',
            'dlac' => 'dl',
            'daclac' => 'dl',
            'dac lac' => 'dl',
            'dnang' => 'dn',
            'danang' => 'dn',
            'da nang' => 'dn',
            'dknong' => 'dk',
            'daknong' => 'dk',
            'dak nong' => 'dk',
            'dnong' => 'dk',
            'gia lai' => 'gl',
            'gialai' => 'gl',
            'glai' => 'gl',
            'khoa' => 'kh',
            'khanhhoa' => 'kh',
            'khanh hoa' => 'kh',
            'k hoa' => 'kh',
            'ktum' => 'kt',
            'kontum' => 'kt',
            'kon tum' => 'kt',
            'nthuan' => 'nt',
            'ninhthuan' => 'nt',
            'ninh thuan' => 'nt',
            'pyen' => 'py',
            'phyen' => 'py',
            'phuyen' => 'py',
            'phu yen' => 'py',
            'qbinh' => 'qb',
            'quangbinh' => 'qb',
            'quang binh' => 'qb',
            'qngai' => 'qn',
            'quangngai' => 'qn',
            'quang ngai' => 'qn',
            'qnam' => 'qn',
            'qng' => 'qn',
            'quangnam' => 'qn',
            'quang nam' => 'qn',
            'qtri' => 'qt',
            'qtr' => 'qt',
            'quangtri' => 'qt',
            'quang tri' => 'qt',
            'thuathienhue' => 'tt',
            'tthue' => 'tt',
            'thua thien hue' => 'tt',
            'hue' => 'tt',

            'bdinh' => 'bd',
            'bdi' => 'binhdinh',
            'tth' => 'tt',
            'qbi' => 'qb',
            'phu' => 'dp',
            'dno' => 'dk',
            'nth' => 'nt',
            'dch' => 'dc',
            'dph' => 'dp',
            'pu' => 'phu',
            'khtt' => 'kh tt',

        ];
    public $array_dai_mn = ['dc ', 'dp ', 'daichanh', 'dai chanh ', 'daiphu', 'dai phu', 'ag', 'angiang', 'an giang', 'blieu', 'baclieu', 'bac lieu', 'bt', 'btre', 'bentre', 'ben tre', 'bduong', 'sb', 'binhduong', 'binh duong', 'bp', 'bphuoc', 'binhphuoc', 'binh phuoc', 'cm', 'camau', 'ca mau', 'ct', 'ctho', 'cantho', 'can tho',
        'dl', 'dlat', 'dalat', 'da lat', 'dn', 'dnai', 'dongnai', 'dn', 'dong nai', 'dthap', 'dongthap', 'dong thap', 'hg', 'hgiang', 'haugiang', 'kg', 'kgiang', 'kiengiang', 'kien giang', 'la', 'lan', 'longan', 'long an', 'st', 'strang', 'soctrang', 'soc trang', 'tn', 'tninh', 'tayninh', 'tay ninh', 'tg', 'tgiang', 'tien giang', 'tiengiang', 'tp', 'hcm', 'tv', 'tvinh', 'travinh', 'tra vinh', 'vl', 'vlong', 'vinhlong', 'vinh long', 'vt', 'vtau', 'vungtau', 'vung tau', 'vung tau'];
    public $array_dai_mn_group = ['2d ', '3d ', '4d ', '2dai', '3dai', '4dai'];


    public $array_dai_mt = ['dc ', 'dp ', 'daichanh', 'dai chanh ', 'daiphu', 'dai phu', 'bdinh', 'binhdinh', 'binh dinh', 'dlak', 'daklak', 'dak lak', 'dlac', 'daclac', 'dac lac', 'dl', 'dnang', 'danang', 'dk', 'da nang', 'dn', 'dknong', 'daknong', 'dak nong', 'glai', 'gialai', 'gia lai', 'gl', 'khoa', 'khanhhoa', 'khanh hoa', 'kh', 'ktum', 'kontum', 'kon tum', 'kt', 'nthuan', 'ninhthuan', 'ninh thuan', 'nt', 'pyen', 'phyen', 'phuyen', 'phu yen', 'py',
        'qbinh', 'quangbinh', 'quang binh', 'qb', 'qngai', 'quangngai', 'quang ngai', 'qg', 'qn', 'qnam', 'quangnam', 'quang nam', 'qtri', 'quangtri', 'quang tri', 'qt', 'thuathienhue', 'tt', 'thua thien hue', 'hue', '(?<![a-zA-Z])th'];
    public $array_dai_mt_group = ['2d ', '3d ', '2dai', '3dai'];
    public $ignoreCharWithMienBac =
        ['hn', 'mb', 'mien bac', 'ha noi', 'hanoi', 'mienbac'];

    function convertTypeLeChan($str_first)
    {


        if (preg_match('/chanle/', $str_first)) {
            $str_first = preg_replace("/chanle/", "01 03 05 07 09 21 23 25 27 29 41 43 45 47 49 61 63 65 67 69 81 83 85 87 89", $str_first);

        }
        if (preg_match('/lechan/', $str_first)) {
            $str_first = preg_replace("/lechan/", "10 12 14 16 18 30 32 34 36 38 50 52 54 56 58 70 72 74 76 78 90 92 94 96 98", $str_first);

        }
        if (preg_match('/lele/', $str_first)) {
            $str_first = preg_replace("/lele/", "11 13 15 17 19 31 33 35 37 39 51 53 55 57 59 71 73 75 77 79 91 93 95 97 99", $str_first);

        }
        if (preg_match('/chanchan/', $str_first)) {
            $str_first = preg_replace("/chanchan/", "00 02 04 06 08 20 22 24 26 28 40 42 44 46 48 60 62 64 66 68 80 82 84 86 88", $str_first);

        }
        if (preg_match('/\ble\b/', $str_first)) {
            $str_first = preg_replace("/\ble\b/", "01 03 05 07 09 11 13 15 17 19 21 23 25 27 29 31 33 35 37 39 41 43 45 47 49 51 53 55 57 59 61 63 65 67 69 71 73 75 77 79 81 83 85 87 89 91 93 95 97 99", $str_first);
        }
        if (preg_match('/\bchan\b/', $str_first)) {
            $str_first = preg_replace("/\bchan\b/", "00 02 04 06 08 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40 42 44 46 48 50 52 54 56 58 60 62 64 66 68 70 72 74 76 78 80 82 84 86 88 90 92 94 96 98", $str_first);
        }
        if (preg_match('/\bgiap\b/', $str_first)) {
            $str_first = preg_replace("/giap/", "06 07 09 10 11 12 14 15 18 23 26 28 32 35 46 47 49 50 51 52 54 55 58 63 66 68 72 75 86 87 89 90 91 92 94 95 98", $str_first);
        }
        return $str_first;
    }

    function block7loAnd8loType($str)
    {
        $array = [" 8l", " 8lo", " 7lo", " 7l"];

        foreach ($array as $value) {
            if (strpos($str, $value) !== false) {
                throw new \Exception("Cú pháp đánh ko hợp lệ");
            }
        }
    }

    function addSpaces($input)
    {
        return preg_replace('/(?<=[a-zA-Z])(?=\d)|(?<=\d)(?=[a-zA-Z])/', ' ', $input);
    }

    function parseMessage($first_str, $dai, $temp_dai, $date_check)
    {

        unset($this->type_dav['dl']);
        if ($dai == 1) {
            if (date('N', strtotime($date_check)) != 7) {
                $this->type_dav['dl'] = 'bld';
            }
            $first_str = $this->replaceWithRules($first_str, $this->rules_south);
        } else if ($dai == 3) {
            $first_str = $this->replaceWithRules($first_str, $this->rules_middle);
        } else if ($dai == 2) {
            $first_str = str_ireplace($this->ignoreCharWithMienBac, '', $first_str);
        }
        $first_str = preg_replace("/^phu/", "dp", $first_str);

        $first_str = $this->replaceWithRules($first_str, $this->type_dav);

        $first_str = preg_replace("/(?<=\w)\s?\s?\s?[.|,]\s?\s?\s?(?=\d{2,}|[a-zA-Z])|_|-]+/", " ", $first_str);
        $first_str = preg_replace("/\s*\.\s*\.\s*/", " ", $first_str);
//dd($first_str);
        $str_first = preg_replace("/(?<=n)\s*(?=[a-zA-Z])/", "", $first_str);
        $str_first = preg_replace("/\s+(mt|mn)\s+|\s+(mt|mn)|(mt|mn)\s+/", "", $str_first);

        $str_first = trim($this->parseUTF8(preg_replace("/[`|'| |_|\-|:|\/]+/", " ", str_replace("..", " ", strtolower($str_first)))));

        $str_first = preg_replace("/[^a-zA-Z.,0-9\s]/", "", $str_first);

        $str_first = preg_replace("/\.*,{2,}\.*|,?+\.{2,},?+|\.,|,\./", " ", $str_first);
        $str_first = str_replace("-", " ", $str_first);
        $str_first = str_replace("—", " ", $str_first);
        $str_first = preg_replace("/^,/", "", $str_first);

        $str_first = preg_replace("/(\.\s|\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s|\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s\s|\s\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/(\.\s\s\s\s|\s\s\s\s\.)/", " ", $str_first);
        $str_first = preg_replace("/\.(?![^\.])/", " ", $str_first);
        $str_first = preg_replace("/^[.,]+/", "", $str_first);
        $str_first = preg_replace("/[.,]\s*(?=\s*(7lo|8lo|7l|8l))/", " ", $str_first);
//        dd($str_first);
        $length = 0;
        $this->block7loAnd8loType($str_first);
        $str_first = $this->convertTypeLeChan($str_first);
        if ($dai == 1 && preg_match('/(^([^\d]+)|(?<=\s|^)2d |(?<=\s|^)3d |(?<=\s|^)4d |2dai |3dai|4dai)/', ltrim($str_first, " . "), $matches)) {

            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        } else if ($dai == 3 && preg_match('/(^([^\d]+)|(?<=\s|^)2d |(?<=\s|^)3d |2dai |3dai)/', ltrim($str_first, " . "), $matches)) {
            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        } elseif ($dai == 2 && preg_match('/^([^\d]+)/', $str_first, $matches)) {
            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        }

        $index_slice_side = $length;
        if ($index_slice_side == 0 && trim($temp_dai) == '' && $dai != 2) {
            throw new \Exception("Không tìm thấy nhà đài");
        }
        $index_slice_percent = $this->getIndexSlicePercent($str_first);

        $str_sub = substr($str_first, $index_slice_side);

        $str_replace_keo = $str_sub;

//        dd($str_replace_keo);
//        if (preg_match("/\s\s?\s?\s?\s?k\s\s?\s?\s?\s?/", $str_replace_keo, $matches)) {
//
//            throw new \Exception("Cú pháp đánh không phù hợp");
//        }
        if (preg_match_all('/(\d+k\d+)|(\d+\s?+keo\s?+\d+)/', $str_replace_keo, $matches)) {

            $temp_str_keo = $str_sub;

            foreach ($matches[0] as $match) {

                $temp_str_keo = $this->convertStringForKeo($temp_str_keo, $match);
            }
//            dd($temp_str_keo);

            $str_replace_keo = $temp_str_keo;
        }

        if (preg_match_all('/(\d+\s?+kt\s?+\d+)|(\d+\s?+kd\s?+\d+)|(\d+\s?+keotoi\s?+\d+)|(\d+\s?+keoden\s?+\d+)/', $str_replace_keo, $matches)) {
            $temp_str_keo = $str_sub;

            foreach ($matches[0] as $match) {
                $temp_str_keo = $this->convertStringForKeoDen($temp_str_keo, $match);
            }
            $str_replace_keo = $temp_str_keo;
        }
        if (trim($temp_dai) !== '' && $dai != 2 && $index_slice_side == 0) {

            if (preg_match("/^[a-zA-Z]+/", $str_replace_keo, $matches)) {

                throw new \Exception("Không tìm thấy nhà đài");
            }

        }
//        dd($str_replace_keo);

        $str_replace_keo = preg_replace('/d d/', 'dd', $str_replace_keo);
        $str_2 = preg_replace("/(?<=\d)ng/", "n", preg_replace("/ngan/", "n", $str_replace_keo));

        $this->checkMoneyRedundant($str_2);

        $this->checkTypeNotAllowWithN($str_2);
        /// fix bug 27-10-2023
        $str = preg_replace('/(?<=\d)n|(?<=\d)k|\bng|\bk|(?<=\d)ng|\bn/', ' ', $str_2);
//        $str = $str_2;
        $str_side = substr($str_first, $index_slice_percent, $index_slice_side);
//        dd($str_side);
        $array_side = $this->convertArraySide($str_side, $dai, $temp_dai);


        $this->checkAllowSideFightInDay($array_side, $dai, $date_check);
        $all_type = $this->getALLType($dai, $date_check);
        //d\s*(\d+)\s*d
        //change: add pattern for (ex: d 2 d 3) 26-7-2023
        $pattern = "/({$all_type}|d\s*(\d+(ngan|ng|n|,|\.)?\d?+)\s*d)\s*\d+(ngan|ng|n|,|\.)?/i";

        $array_empty = [];

        if (isset($str[0]) && $str[0] != " ") {
            $str = " " . $str;
        }

        //ngay 28/6/2023
        $str = preg_replace('/(?<=\D)\.\s*|(?<=\D)\,\s*/', '', $str);

//        dd($str, preg_replace('/\s+(?=\S)|(?<=\S)\s+/', ' ', $str));
//        $str = preg_replace('/\s+(?=\S)|(?<=\S)\s+/', ' ', $str);

        $this->checkStringHasOnlyD($str);
        $str = $this->addSpaces($str);

        if (preg_match($pattern, $str, $matches, PREG_OFFSET_CAPTURE)) {
            $end_pos = $matches[0][1] + strlen($matches[0][0]) - 1;
            $end_pos++;
            $last_p = 0;
//            if (!preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos))
//                && ($matches[sizeof($matches) - 1][0] == "," || $matches[sizeof($matches) - 1][0] == ".")) {
//                $end_pos += 1;
//            }
//            if (preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos + 1))
//                || ($matches[sizeof($matches) - 1][0] == "," || $matches[sizeof($matches) - 1][0] == ".")
//            ) {
//                dd($str);
//                $end_pos += 1;
//            }
//            dd($str,$end_pos, substr($str, 0, $end_pos+1));
            if (
//                !preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos))
//                &&
            ($matches[sizeof($matches) - 1][0] == "," || $matches[sizeof($matches) - 1][0] == ".")
            ) {
                $end_pos += 1;
            }
//            if (preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos + 1))
//                && ($matches[sizeof($matches) - 1][0] == "," || $matches[sizeof($matches) - 1][0] == ".")
//            ) {
//                $end_pos += 1;
//            }

            $get_last_index = $this->getLastIndex_2($end_pos, $str, 0, $last_p, $all_type);

            if ($end_pos == $get_last_index) {
                if ($this->checkNumberHasFirstString(trim(substr($str, $get_last_index, $get_last_index))) == 1) {
                    // change 23-08-2023 old code is     $end_pos = $end_pos + $get_last_index +  1 ;
                    $end_pos = $end_pos + $get_last_index;
                }
            } else {
                if ($end_pos != $get_last_index) {
                    $end_pos = $end_pos + $get_last_index;
                }
            }

        } else {
//            echo "Không tìm thấy chuỗi match regex";
//            dd($str);
//            dd($str);
            throw new \Exception("Không tìm thấy loại đánh phù hợp");

        }

        $str = preg_replace("/(?<!\S)\.|,(?!\S)/", "", $str);

        if ($dai == 1) {
            $this->catchRegionInline($str, $this->convertArrayToStringRegex(array_merge($this->array_dai_mn, $this->array_dai_mn_group)));
        } else if ($dai == 3) {
            $this->catchRegionInline($str, $this->convertArrayToStringRegex(array_merge($this->array_dai_mt, $this->array_dai_mt_group)));
        }
        $object = [];

        if ($array_side == null && $dai == 2) {

            $object['tp'] = ['hn'];

        } else {
            $object['tp'] = $array_side;
            $this->checkDaiExist($array_side, $dai);
        }
//        dd($str);
//        dd($object);

        $this->checkLastStringRedundant($str);


        if (preg_match("/^\s*[a-zA-Z]+/", $str, $matches)) {
            throw new \Exception("Cú pháp đánh không phù hợp");
        }
//dd($str);
        $this->validateTypeValidBeforeMoney($str);
        $array_fight = $this->splitString(($str), $end_pos, 0, $array_empty, $all_type);

//dd($array_fight);
        $this->checkValidateAfterSplit($str, implode(" ", $array_fight));

        $replacements = array(
            "dao xc" => "daoxc",
            "xc dao" => "xcdao",
            "dao b" => "bdao",
            "b dao" => "bdao",
            "daobao" => "bdao",
            "daob" => "bdao",
        );

        $l = 0;
//        dd($array_fight);
        foreach ($array_fight as $each) {
            $arrayTypeTemp = [];
            foreach ($replacements as $substring => $replacement) {
                $each = str_replace($substring, $replacement, $each);
            }

            $pattern_parse = '/(?<=\d)(?=[a-zA-Z])(?!n|ng|ngan|k)|(?<=[a-zA-Z])(?=\d)(?!n|ng|ngan|k)/';

            $result = preg_split($pattern_parse, $each);
            $joined_string = implode(" ", $result);

            $arr_slice = array_map('trim', explode(" ", trim($joined_string)));

            $arr_slice = array_values(array_filter($arr_slice, 'strlen'));
            $array_result = $this->getTypeFight($arr_slice);

            $this->checkDandD($arr_slice);
            $this->checkTypeDuplication($arr_slice);

            if (sizeof($array_result) > 0) {
                $words = $this->parseTypeFight($each);

                $result_type_fight = [];
                $result_type_fight_detail = [];
                foreach ($words as $word) {
                    //18-11-2023 bua` 2 loai da
                    $array_type_key_word = $this->getEnumByName($word);
                    if (isset($array_type_key_word[0])) {
                        $arrayTypeTemp[] = $array_type_key_word[0];
                    }
                    if (array_key_exists(7, $result_type_fight_detail) && $array_type_key_word[0] == 7 && $result_type_fight_detail[7] == "dathang") {
                        $result_type_fight[] = $array_type_key_word[sizeof($array_type_key_word) - 1];
                        $result_type_fight_detail[$array_type_key_word[sizeof($array_type_key_word) - 1] + 1] = $word;
                    } else {
                        $result_type_fight[] = $array_type_key_word[sizeof($array_type_key_word) - 1];
                        $result_type_fight_detail[$array_type_key_word[sizeof($array_type_key_word) - 1]] = $word;
                    }
                }

                $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);

                if (in_array("da", $words) && in_array(7, $result_type_fight) && array_count_values($result_type_fight)[7] == 2) {

                    unset($result_type_fight[array_search(7, $result_type_fight)]);
                    $result_type_fight = array_values($result_type_fight);
                    $result_type_fight[] = 8;
                    $result_type_fight_detail[8] = "da";

                    $index = array_search("da", $arr_slice);


                    if ($index !== false && array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 1] == "da") {

                        $index_temp = array_search(array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 2], $arr_slice);
                        $this->swap($arr_slice, $index + 1, $index_temp + 1);
                    }

                } elseif (in_array("dv", $words) && in_array(7, $result_type_fight) && array_count_values($result_type_fight)[7] == 2) {
                    unset($result_type_fight[array_search(7, $result_type_fight)]);
                    $result_type_fight = array_values($result_type_fight);
                    $result_type_fight[] = 8;
                    $result_type_fight_detail[8] = "dv";

                    $index = array_search("dv", $arr_slice);


                    if ($index !== false && array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 1] == "dv") {

                        $index_temp = array_search(array_values($result_type_fight_detail)[sizeof($result_type_fight_detail) - 2], $arr_slice);
                        $this->swap($arr_slice, $index + 1, $index_temp + 1);
                    }
                }
                if (in_array(TypeFight::DA, $result_type_fight) ||
                    in_array(TypeFight::DA_XIEN, $result_type_fight)) {
                    $arrSo = $this->getAllNumber($arr_slice, $array_result[0], $all_type);
                    $isCheck = false;
                    $index = array_search('dx', $arr_slice);
                    if ($this->isNumberForXCD($arrSo) && $index !== false) {
                        $isCheck = true;
                        $arr_slice[$index] = 'xcd';
                        $index2 = array_search(TypeFight::DA_XIEN, $result_type_fight);
                        if ($index2 !== false) {

                            $result_type_fight[$index2] = TypeFight::XIU_CHU_DAO;
                        }
                        $index3 = array_search('dx', $words);
                        $words[$index3] = 'xcd';
                        $result_type_fight_detail[TypeFight::XIU_CHU_DAO] = 'xcd';
                        unset($result_type_fight_detail[TypeFight::DA_XIEN]);

                    } else if (count($arrSo) < 2) {

                        $str_so_err = implode(" ", $arrSo);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 con trở lên");

                    }
                    if (!$isCheck) {
                        $this->checkArrayDa($arrSo);

                    }
                }

                if ($this->checkArrayElements4Digit(array_slice($arr_slice, 0, $array_result[0]))) {

                    $this->checkDulicationTypeFight3And4Digit($arrayTypeTemp, $result_type_fight_detail);
                    foreach (array_slice($arr_slice, 0, $array_result[0]) as $value_4_digit) {

                        if (strlen($value_4_digit) == 4 && (in_array("dau", $words)
                                || !empty(array_intersect(['dauduoi', 'dd', 'dđ', 'đđ', 'đd'], $words)))) {
                            throw new \Exception("Loại đánh Đầu, Đầu đuôi không nhận 4 số");
                        }

                        if (strlen($value_4_digit) == 4) {
                            $number_save = $value_4_digit;
                            $myArray = [
                                ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                ['dui', 'duoi'],
                                ['daodui', 'daoduoi']
                            ];
                            $array_temp_type = [];
                            $count = 0;
                            $index = 0;

                            foreach ($words as $value) {

                                $type_digit_4 = $value;
                                foreach ($myArray as $key => $subArray) {
                                    $keyToRemove = array_search($type_digit_4, $subArray);
                                    if (in_array($value, $array_temp_type)) {
                                        $count++;
                                        break;
                                    }
                                    if ($count == 0) {
                                        if ($keyToRemove !== false) {
                                            if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                $array_temp_type[3] = $value;
                                            } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {

                                                $array_temp_type[16] = $value;
                                            } else if (in_array($value, ['dui', 'duoi'])) {

                                                $array_temp_type[1] = $value;
                                            } else if (in_array($value, ['daodui', 'daoduoi'])) {

                                                $array_temp_type[14] = $value;
                                            }
                                            $index++;
                                            unset($myArray[$key]);
                                            break;
                                        }
                                    }
                                }
                                if ($count != 0) {
                                    break;
                                }
                            }

                            $arr_slice_4_digit = array_values(array_filter($arr_slice, 'strlen'));
                            $index_slice_money = 0;

                            foreach ($array_temp_type as $key => $value) {
                                $object['result'][] = ['so_danh' => [$number_save],
                                    'loai_danh' => [$key],
                                    'loai_danh_detail' => [$key => $value],
                                    'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_4_digit[array_search($value, $arr_slice_4_digit) + 1])]
                                ];
                                $index_slice_money = array_search($value, $arr_slice_4_digit) + 1;
                            }

                            $arr_slice_3_digit = array_slice($arr_slice_4_digit, $index_slice_money + 1);
                            $words_2 = $index != -1 ? array_slice($words, $index) : null;
                            $arr_slice_2_digit = null;
                            if ($words_2 != null) {
                                $count_2 = 0;
                                $array_temp_type_2 = [];
                                $arr_xc_check = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld',
                                    'tieulodao', 'xd', 'dao xc', 'xc dao', 'xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi',
                                    'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi', 'xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'xc', 'x',
                                    'xiu', 'tl', 'tieulo', 'xcdau', 'dauxc', 'xdau', 'xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'
                                ];
                                $arr_dd_check = ['dau', 'dau', 'dui', 'duoi', 'dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
                                $myArray_2 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                    ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'],
                                    ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'],
                                    ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'],
                                    ['xc', 'x', 'xiu', 'tl', 'tieulo'],
                                    ['xcdau', 'dauxc', 'xdau'],
                                    ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'],
                                    ['dau', 'dau'],
                                    ['dui', 'duoi'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                ];
                                foreach ($words_2 as $value) {
                                    $type_digit_3 = $value;
                                    foreach ($myArray_2 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_3, $subArray);
                                        $commonValues = array_intersect($array_temp_type_2, $arr_dd_check);
                                        $commonValues_xc = array_intersect($array_temp_type_2, $arr_xc_check);
                                        if (!empty($commonValues)) {

                                            if (in_array($value, $arr_xc_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues) . " và " . $value . " không đánh chung được");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {

                                            if (in_array($value, $arr_dd_check)) {
                                                throw new \Exception("Loại đánh " . implode(', ', $commonValues_xc) . " và " . $value . " không đánh chung được");
                                            }
                                        }
                                        if (!empty($commonValues_xc)) {
                                            if (in_array($value, $arr_dd_check)) {
                                                $index--;
                                                break;
                                            }
                                        }
                                        if (in_array($value, $array_temp_type_2)) {
                                            $count_2++;
                                            break;
                                        }
                                        if ($count_2 == 0) {
                                            if ($keyToRemove !== false) {
                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_2[3] = $value;
                                                } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {
                                                    $array_temp_type_2[16] = $value;
                                                } else if (in_array($value, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'])) {
                                                    $array_temp_type_2[15] = $value;
                                                } else if (in_array($value, ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'])) {
                                                    $array_temp_type_2[14] = $value;
                                                } else if (in_array($value, ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'])) {
                                                    $array_temp_type_2[13] = $value;
                                                } else if (in_array($value, ['xc', 'x', 'xiu', 'tl', 'tieulo'])) {
                                                    $array_temp_type_2[4] = $value;
                                                } else if (in_array($value, ['xcdau', 'dauxc', 'xdau'])) {
                                                    $array_temp_type_2[5] = $value;
                                                } else if (in_array($value, ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'])) {
                                                    $array_temp_type_2[6] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_2[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_2[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_2[2] = $value;
                                                }

                                                if (in_array($value, $arr_dd_check)) {
                                                    unset($myArray_2[2]);
                                                    unset($myArray_2[3]);
                                                    unset($myArray_2[4]);
                                                    unset($myArray_2[5]);
                                                    unset($myArray_2[6]);
                                                    unset($myArray_2[7]);
                                                } else if (in_array($value, $arr_xc_check)) {
                                                    unset($myArray_2[8]);
                                                    unset($myArray_2[9]);
                                                    unset($myArray_2[10]);
                                                } else {
                                                    unset($myArray_2[$key]);
                                                }
                                                $index++;
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_2 != 0) {
                                        break;
                                    }
                                }

                                $index_slice_money_2 = 0;
                                foreach ($array_temp_type_2 as $key => $value) {
                                    $object['result'][] = ['so_danh' => [substr($number_save, 1, 3)],
                                        'loai_danh' => [$key],
                                        'loai_danh_detail' => [$key => $value],
                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_3_digit[array_search($value, $arr_slice_3_digit) + 1])]
                                    ];
                                    $index_slice_money_2 = array_search($value, $arr_slice_3_digit) + 1;
                                }
                                $arr_slice_2_digit = array_slice($arr_slice_3_digit, $index_slice_money_2 + 1);
                            }

                            $words_3 = $index != -1 ? array_slice($words, $index) : null;
                            if ($words_3 != null) {
                                $myArray_3 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['dau', 'dau'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                    ['dui', 'duoi']
                                ];
                                $count_3 = 0;
                                $array_temp_type_3 = [];
                                foreach ($words_3 as $value) {
                                    $type_digit_2 = $value;
                                    foreach ($myArray_3 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_2, $subArray);

                                        if (in_array($value, $array_temp_type_3)) {
                                            $count_3++;
                                            break;
                                        }
                                        if ($count_3 == 0) {
                                            if ($keyToRemove !== false) {
                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_3[3] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_3[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_3[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_3[2] = $value;
                                                }
                                                $index++;
                                                unset($myArray_3[$key]);
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_3 != 0) {
                                        break;
                                    }
                                }
                                foreach ($array_temp_type_3 as $key => $value) {
                                    $object['result'][] = ['so_danh' => [substr($number_save, 2, 2)],
                                        'loai_danh' => [$key],
                                        'loai_danh_detail' => [$key => $value],
                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_2_digit[array_search($value, $arr_slice_2_digit) + 1])]
                                    ];
                                }
                            }

                        }

                    }


                } else if ($this->checkArrayElements3Digit(array_slice($arr_slice, 0, $array_result[0]))) {

                    $this->checkDao3digit($arr_slice);
                    $this->checkDulicationTypeFight3And4Digit($arrayTypeTemp, $result_type_fight_detail);
                    foreach (array_slice($arr_slice, 0, $array_result[0]) as $value_3_digit)
                        if (strlen($value_3_digit) == 3) {
                            $number_save = $value_3_digit;
                            $myArray_2 = [
                                ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'],
                                ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'],
                                ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'],
                                ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'],
                                ['xc', 'x', 'xiu', 'tl', 'tieulo'],
                                ['xcdau', 'dauxc', 'xdau'],
                                ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'],
                                ['dau', 'dau'],
                                ['dui', 'duoi'],
                                ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                ['baylo'],
                                ['baylodao', 'daobaylo'],
                            ];
                            $index = 0;
                            $count_2 = 0;
                            $array_temp_type_2 = [];
                            $arr_xc_check = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld',
                                'tieulodao', 'xd', 'dao xc', 'xc dao', 'xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi',
                                'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi', 'xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'xc', 'x',
                                'xiu', 'tl', 'tieulo', 'xcdau', 'dauxc', 'xdau', 'xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'
                            ];
                            $arr_dd_check = ['dau', 'dau', 'dui', 'duoi', 'dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
                            foreach ($words as $value) {
                                $type_digit_3 = $value;
                                foreach ($myArray_2 as $key => $subArray) {
                                    $keyToRemove = array_search($type_digit_3, $subArray);
                                    $commonValues = array_intersect($array_temp_type_2, $arr_dd_check);
                                    $commonValues_xc = array_intersect($array_temp_type_2, $arr_xc_check);
                                    if (!empty($commonValues)) {

                                        if (in_array($value, $arr_xc_check)) {
                                            throw new \Exception("Loại đánh " . implode(', ', $commonValues) . " và " . $value . " không cho phép đánh chung");
                                        }
                                    }
                                    if (!empty($commonValues_xc)) {

                                        if (in_array($value, $arr_dd_check)) {
                                            throw new \Exception("Loại đánh " . implode(', ', $commonValues_xc) . " và " . $value . " không cho phép đánh chung");
                                        }
                                    }
                                    if (!empty($commonValues_xc)) {
                                        if (in_array($value, $arr_dd_check)) {
                                            $index--;
                                            break;
                                        }
                                    }
                                    if (in_array($value, $array_temp_type_2)) {
                                        $count_2++;
                                        break;
                                    }
                                    if ($count_2 == 0) {
                                        if ($keyToRemove !== false) {

                                            if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                $array_temp_type_2[3] = $value;
                                            } else if (in_array($value, ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'])) {
                                                $array_temp_type_2[16] = $value;
                                            } else if (in_array($value, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'])) {
                                                $array_temp_type_2[15] = $value;
                                            } else if (in_array($value, ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'])) {
                                                $array_temp_type_2[14] = $value;
                                            } else if (in_array($value, ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'])) {
                                                $array_temp_type_2[13] = $value;
                                            } else if (in_array($value, ['xc', 'x', 'xiu', 'tl', 'tieulo'])) {
                                                $array_temp_type_2[4] = $value;
                                            } else if (in_array($value, ['xcdau', 'dauxc', 'xdau'])) {
                                                $array_temp_type_2[5] = $value;
                                            } else if (in_array($value, ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'])) {
                                                $array_temp_type_2[6] = $value;
                                            } else if (in_array($value, ['dau', 'dau'])) {
                                                $array_temp_type_2[0] = $value;
                                            } else if (in_array($value, ['dui', 'duoi'])) {
                                                $array_temp_type_2[1] = $value;
                                            } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                $array_temp_type_2[2] = $value;
                                            } else if (in_array($value, ['baylo'])) {
                                                $array_temp_type_2[9] = $value;
                                            } else if (in_array($value, ['baylodao', 'daobaylo'])) {
                                                $array_temp_type_2[10] = $value;
                                            }
                                            if (in_array($value, $arr_dd_check)) {
                                                unset($myArray_2[2]);
                                                unset($myArray_2[3]);
                                                unset($myArray_2[4]);
                                                unset($myArray_2[5]);
                                                unset($myArray_2[6]);
                                                unset($myArray_2[7]);
                                            }
                                            if (in_array($value, $arr_xc_check)) {
                                                unset($myArray_2[8]);
                                                unset($myArray_2[9]);
                                                unset($myArray_2[10]);
                                            }
                                            $index++;
                                            unset($myArray_2[$key]);
                                            break;
                                        }
                                    }
                                }
                                if ($count_2 != 0) {
                                    break;
                                }
                            }
                            $index_slice_money_2 = 0;
                            $counts_d = array_count_values($arr_slice);
                            if (isset($counts_d["d"]) && $counts_d["d"] == 2) {
                                $array_convert_dd = $this->convert_array_to_dd($arr_slice);
                                foreach ($array_temp_type_2 as $key => $value) {
                                    $object['result'][] = ['so_danh' => [$number_save],
                                        'loai_danh' => [$key],
                                        'loai_danh_detail' => [$key => $value],
                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($array_convert_dd[array_search($value, $array_convert_dd) + 1])]
                                    ];
                                }

                            } else {
                                foreach ($array_temp_type_2 as $key => $value) {
                                    $object['result'][] = ['so_danh' => [$number_save],
                                        'loai_danh' => [$key],
                                        'loai_danh_detail' => [$key => $value],
                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice[array_search($value, $arr_slice) + 1])]
                                    ];
                                    $index_slice_money_2 = array_search($value, $arr_slice) + 1;
                                }
                            }


                            $arr_slice_2_digit = array_slice($arr_slice, $index_slice_money_2 + 1);
                            $words_3 = $index != -1 ? array_slice($words, $index) : null;
                            if ($words_3 != null) {
                                $myArray_3 = [
                                    ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'],
                                    ['dau', 'dau'],
                                    ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'],
                                    ['dui', 'duoi']
                                ];
                                $count_3 = 0;
                                $array_temp_type_3 = [];
                                foreach ($words_3 as $value) {
                                    $type_digit_2 = $value;
                                    foreach ($myArray_3 as $key => $subArray) {
                                        $keyToRemove = array_search($type_digit_2, $subArray);
                                        if (in_array($value, $array_temp_type_3)) {
                                            $count_3++;
                                            break;
                                        }
                                        if ($count_3 == 0) {
                                            if ($keyToRemove !== false) {
                                                if (in_array($value, ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'])) {
                                                    $array_temp_type_3[3] = $value;
                                                } else if (in_array($value, ['dau', 'dau'])) {
                                                    $array_temp_type_3[0] = $value;
                                                } else if (in_array($value, ['dui', 'duoi'])) {
                                                    $array_temp_type_3[1] = $value;
                                                } else if (in_array($value, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'])) {
                                                    $array_temp_type_3[2] = $value;
                                                }
                                                $index++;
                                                unset($myArray_3[$key]);
                                                break;
                                            }
                                        }
                                    }
                                    if ($count_3 != 0) {
                                        break;
                                    }

                                }

                                foreach ($array_temp_type_3 as $key => $value) {
                                    $object['result'][] = ['so_danh' => [substr($number_save, 1, 2)],
                                        'loai_danh' => [$key],
                                        'loai_danh_detail' => [$key => $value],
                                        'so_tien_danh' => [$this->getPriceForSplit4Digit($arr_slice_2_digit[array_search($value, $arr_slice_2_digit) + 1])]
                                    ];
                                }
                            }
                        }
                    if (isset($object['result'])) {
                        $this->checkDuplicationNumberInArray($object['result'][$l]['so_danh'], $object['result'][$l]['loai_danh_detail']);

                    }
                    $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);

                } else {


//                    $this->convertDdtoDauDuoi($result_type_fight, $arr_slice);

                    $this->checkDulicationTypeFight($arrayTypeTemp, $result_type_fight_detail);

                    $arrSoDanh = $this->getAllNumber($arr_slice, $array_result[0], $all_type);


                    if (
                        array_filter($arrSoDanh, function ($value) {
                            return strlen($value) == 4;
                        })
                        && (in_array(TypeFight::DAU, $result_type_fight) || in_array(TypeFight::DAU_DUOI, $result_type_fight))
                    ) {
                        throw new \Exception("Loại đánh Đầu, Đầu đuôi không nhận 4 số");
                    }

                    $object['result'][] = ['so_danh' => $arrSoDanh,
                        'loai_danh' => array_unique($result_type_fight),
                        'loai_danh_detail' => $result_type_fight_detail,
                        'so_tien_danh' => $this->getPrice($arr_slice)
                    ];

                    $this->checkTypeAndMoneyValid($arr_slice, $result_type_fight_detail);


                    if (isset($object['result'])) {

                        $this->checkDuplicationNumberInArray($arrSoDanh, $result_type_fight_detail);

                    }

                }


            }
            $l++;

        }
//dd($object);
        return $object;
    }

    function validateTypeValidBeforeMoney($str)
    {
        if (str_contains($str, ',') && preg_match("/[0-9](?=\s+[0-9]+,[0-9]\s+)/", $str)
            ||
            str_contains($str, '.') && preg_match("/[0-9](?=\s+[0-9]+\.[0-9]\s+)/", $str)) {
            throw new \Exception("Cú pháp không hợp lệ vì không tìm thấy loại đánh ở trước tiền đánh");

        }
    }

    private function checkValidateAfterSplit($string1, $string2)
    {
        $count1 = $this->countNumberInString($string1);
        $count2 = $this->countNumberInString($string2);

        if ($count1 != $count2) {

//dd($string1,$string2);
            throw new \Exception("Cú pháp đánh không hợp lệ vui lòng thử lại");
        }
    }

    private function countNumberInString($string)
    {
        preg_match_all('/\d+/', $string, $matches);
        $count = count($matches[0]);
        return $count;
    }

    /**
     * @throws \Exception
     */

    function convertDdtoDauDuoi(&$result_type_fight, &$arr_slice)
    {

        if (in_array(TypeFight::DAU_DUOI, $result_type_fight)) {

            $indexes = $this->findAllIndexes($arr_slice, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd']);
            if (isset($indexes[0])) {
                $index_dd = $indexes[0];
                $newElements = array('dau', $arr_slice[$index_dd + 1], 'duoi');
                array_splice($arr_slice, $index_dd, 1, $newElements);


                $this->appendValueInArray($result_type_fight, [0, 1], TypeFight::DAU_DUOI);
//                $this->appendValueInArrayByKey($result_type_fight_detail, [0 => "dau", 1 => "duoi"], TypeFight::DAU_DUOI);

            }
        }

        if (in_array(TypeFight::XIU_CHU, $result_type_fight)) {

            $indexes = $this->findAllIndexes($arr_slice, ['xc', 'x', 'xiu', 'tl', 'tieulo']);
            if (isset($indexes[0])) {
                $index_dd = $indexes[0];
                $newElements = array('xcdau', $arr_slice[$index_dd + 1], 'xcduoi');
                array_splice($arr_slice, $index_dd, 1, $newElements);
                $this->appendValueInArray($result_type_fight, [TypeFight::XIU_CHU_DAU, TypeFight::XIU_CHU_DUOI], TypeFight::XIU_CHU);
            }
        }
        if (in_array(TypeFight::XIU_CHU_DAO, $result_type_fight)) {

            $indexes = $this->findAllIndexes($arr_slice, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao']);
            if (isset($indexes[0])) {
                $index_dd = $indexes[0];
                $newElements = array('xcddau', $arr_slice[$index_dd + 1], 'xcdduoi');
                array_splice($arr_slice, $index_dd, 1, $newElements);
                $this->appendValueInArray($result_type_fight, [TypeFight::XIU_CHU_DAO_DAU, TypeFight::XIU_CHU_DAO_DUOI], TypeFight::XIU_CHU_DAO);
            }
        }
    }

    function appendValueInArray(&$array_temp, $newElement, $keySearch)
    {

        $index = array_search($keySearch, $array_temp);

        if ($index !== false) {
            array_splice($array_temp, $index, 1, $newElement);
        }

    }

    function convertDdtoDauDuoiForSliceFight(&$arr_slice, &$array_temp)
    {
//        $newElement = [0 => "dau", 1 => "duoi"];

//        $indexes = $this->findAllIndexes($array_temp, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd']);
//        if (isset($indexes[0])) {
//            $resultArray = [];
//            foreach ($array_temp as $key => $value) {
//                $resultArray[$key] = $value;
//                if ($key == $indexes[0]) {
//                    foreach ($newElement as $newKey => $newValue) {
//                        $resultArray[$newKey] = $newValue;
//                    }
//                }
//            }
//
//            $array_temp = $resultArray;
//            unset($array_temp[TypeFight::DAU_DUOI]);

        $z = $this->findAllIndexes($arr_slice, ['dauduoi', 'dd', 'dđ', 'đđ', 'đd']);
        if (isset($z[0])) {
            $index_dd = $z[0];
            $newElements = array('dau', $arr_slice[$index_dd + 1], 'duoi');
            array_splice($arr_slice, $index_dd, 1, $newElements);
        }

//        }
    }

    function convertXCtoXCDDForSliceFight(&$arr_slice, &$array_temp)
    {
//        $newElement = [TypeFight::XIU_CHU_DAU => "xcdau", TypeFight::XIU_CHU_DUOI => "xcduoi"];

//        $indexes = $this->findAllIndexes($array_temp, ['xc', 'x', 'xiu', 'tl', 'tieulo']);
//        if (isset($indexes[0])) {
//            $resultArray = [];
//            foreach ($array_temp as $key => $value) {
//                $resultArray[$key] = $value;
//                if ($key == $indexes[0]) {
//                    foreach ($newElement as $newKey => $newValue) {
//                        $resultArray[$newKey] = $newValue;
//                    }
//                }
//            }
//
//            $array_temp = $resultArray;
//            unset($array_temp[TypeFight::XIU_CHU]);

        $z = $this->findAllIndexes($arr_slice, ['xc', 'x', 'xiu', 'tl', 'tieulo']);
        if (isset($z[0])) {
            $index_dd = $z[0];
            $newElements = array('xcdau', $arr_slice[$index_dd + 1], 'xcduoi');
            array_splice($arr_slice, $index_dd, 1, $newElements);
        }

//        }
    }

    function convertXCDaotoXCDDDForSliceFight(&$arr_slice, &$array_temp)
    {
//        dd($arr_slice);
//        $newElement = [TypeFight::XIU_CHU_DAO_DAU => "xcddau", TypeFight::XIU_CHU_DAO_DUOI => "xcdduoi"];

        $indexes = $this->findAllIndexes($array_temp, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao']);
        if (isset($indexes[0])) {
//            $resultArray = [];
//            foreach ($array_temp as $key => $value) {
//                $resultArray[$key] = $value;
//                if ($key == $indexes[0]) {
//                    foreach ($newElement as $newKey => $newValue) {
//                        $resultArray[$newKey] = $newValue;
//                    }
//                }
//            }
//
//            $array_temp = $resultArray;
//            unset($array_temp[TypeFight::XIU_CHU_DAO]);

            $z = $this->findAllIndexes($arr_slice, ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao']);
            if (isset($z[0])) {
                $index_dd = $z[0];
                $newElements = array('xcddau', $arr_slice[$index_dd + 1], 'xcdduoi');
                array_splice($arr_slice, $index_dd, 1, $newElements);

            }

        }
    }

    function checkDulicationTypeFight3And4Digit($array, $result_type_fight_detail)
    {

        $highCountValues = array_filter(array_count_values($array), function ($count, $key) {
            //4 is XC and XCD
            return $count > 1 && ($key == 4 || $key == 15);
        }, ARRAY_FILTER_USE_BOTH);
//
        $typeString = "";
        $arrayKey = array_keys($highCountValues);
        $i = 0;

        foreach ($result_type_fight_detail as $key => $value) {

            if (isset($arrayKey[$i]) && $key == $arrayKey[$i]) {
                if ($typeString != "") {
                    $typeString .= ", ";
                }
                $typeString .= $value;
            }

            $i++;
        }

        if ($highCountValues != null) {
            throw new \Exception("Cú pháp đã trùng loại đánh {$typeString} hãy kiểm tra lại. ");
        }


    }

    function checkDulicationTypeFight($array, $result_type_fight_detail)
    {
        $highCountValues = array_filter(array_count_values($array), function ($count) {
            return $count > 1;
        });
        $typeString = "";
        $arrayKey = array_keys($highCountValues);
        $i = 0;

        foreach ($result_type_fight_detail as $key => $value) {

            if (isset($arrayKey[$i]) && $key == $arrayKey[$i]) {
                if ($typeString != "") {
                    $typeString .= ", ";
                }
                $typeString .= $value;
            }

            $i++;
        }
//dd($array,$arrayKey,$result_type_fight_detail);
        if ($highCountValues != null) {
            throw new \Exception("Cú pháp đã trùng loại đánh {$typeString} hãy kiểm tra lại. ");
        }

    }

    function checkValueOfArrayInArrayOther($array1, $array2)
    {
        foreach ($array1 as $d) {
            if (!in_array($d, $array2)) {
                return true;
            }
        }
        return false;
    }

    function throwMessageSideNotAllow($check)
    {
        if ($check) {
            throw new \Exception("Đài không hợp lệ trong ngày");
        }
    }

    function checkAllowSideFightInDay($arrayDai, $dai, $date_check)
    {

        $date = new \DateTime($date_check);
        $weekday = $date->format('N');
        $arrayCommon = ['2d', '3d', '4d', '2dai', '3dai', '4dai', 'dc', 'daichanh', 'daichinh', 'dchanh', 'dp', 'daiphu', 'dphu'];
        if ($dai == 1) {
            $arrayVl = ['vl', 'vlong', 'vinhlong', 'vinh long',];
            $arrayBd = ['bd', 'bduong', 'binhduong', 'binh duong'];
            $arrayTv = ['tv', 'tvinh', 'travinh', 'tra vinh'];
            $arrayTn = ['tn', 'tninh', 'tayninh', 'tay ninh'];
            $arrayAg = ['ag', 'angiang', 'an giang'];
            $arrayBthuan = ['bthuan', 'binhthuan', 'binh thuan', 'bt'];
            $arrayHcm = ['tp', 'hcm'];
            $arrayLa = ['la', 'lan', 'longan', 'long an'];
            $arrayBp = ['bp', 'bphuoc', 'binhphuoc', 'binh phuoc'];
            $arrayHg = ['hg', 'hgiang', 'haugiang'];
            $arrayTg = ['tg', 'tgiang', 'tien giang', 'tiengiang'];
            $arrayKg = ['kg', 'kgiang', 'kiengiang', 'kien giang'];
            $arrayDl = ['dl', 'dlat', 'dalat', 'da lat'];
            $arrayDn = ['dn', 'dnai', 'dongnai', 'dn', 'dong nai'];
            $arrayCt = ['ct', 'ctho', 'cantho', 'can tho'];
            $arraySt = ['st', 'strang', 'soctrang', 'soc trang'];
            $arrayDt = ['dt', 'dthap', 'dongthap', 'dong thap'];
            $arrayVt = ['vt', 'vtau', 'vungtau', 'vung tau', 'vung tau'];
            $arrayBl = ['bl', 'blieu', 'baclieu', 'bac lieu'];
            $arrayBtre = ['btre', 'bentre', 'ben tre', 'bt'];
            $arrayCm = ['cm', 'camau', 'ca mau'];


            foreach ($arrayDai as $d) {
                if ($weekday != 6 && in_array($d, ['4dai', '4d'])) {
                    throw new \Exception("Đài không hợp lệ trong ngày");
                }
            }
            switch ($weekday) {
                case 1:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayCm, ...$arrayDt, ...$arrayHcm, ...$arrayCommon]));
                    break;
                case 2:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayBl, ...$arrayBtre, ...$arrayVt, ...$arrayCommon]));
                    break;
                case 3:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayCt, ...$arrayDn, ...$arraySt, ...$arrayCommon]));

                    break;
                case 4:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayAg, ...$arrayBthuan, ...$arrayTn, ...$arrayCommon]));
                    break;
                case 5:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayBd, ...$arrayTv, ...$arrayVl, ...$arrayCommon]));

                    break;
                case 6:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayHcm, ...$arrayBp, ...$arrayHg, ...$arrayLa, ...$arrayCommon]));

                    break;
                case 7:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayKg, ...$arrayDl, ...$arrayTg, ...$arrayCommon]));
                    break;
            }
        } elseif ($dai == 3) {
            $arrayBd = ['bdinh', 'binhdinh', 'binh dinh', 'bd'];
            $arrayDl = ['dlak', 'daklak', 'dak lak', 'dlac', 'daclac', 'dac lac', 'dl'];
            $arrayDnang = ['dnang', 'danang', 'da nang', 'dn'];
            $arrayDnong = ['dk', 'dknong', 'daknong', 'dak nong'];
            $arrayGl = ['glai', 'gialai', 'gia lai', 'gl'];
            $arrayKh = ['khoa', 'khanhhoa', 'khanh hoa', 'kh'];
            $arrayKt = ['ktum', 'kontum', 'kon tum', 'kt'];
            $arrayNt = ['nthuan', 'ninhthuan', 'ninh thuan', 'nt'];
            $arrayPy = ['pyen', 'phyen', 'phuyen', 'phu yen', 'py'];
            $arrayQb = ['qbinh', 'quangbinh', 'quang binh', 'qb'];
            $arrayQngai = ['qg', 'qngai', 'quangngai', 'quang ngai', 'qn'];
            $arrayQnam = ['qnam', 'quangnam', 'quang nam', 'qn'];
            $arrayQt = ['qtri', 'quangtri', 'quang tri', 'qt'];
            $arrayTth = ['thuathienhue', 'tt', 'thua thien hue', 'th', 'hue'];
            foreach ($arrayDai as $d) {
                if ($weekday != 7 && $weekday != 6 && $weekday != 4 && in_array($d, ['3dai', '3d'])) {
                    throw new \Exception("Đài không hợp lệ trong ngày");
                }
            }
            switch ($weekday) {
                case 1:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayPy, ...$arrayTth, ...$arrayCommon]));

                    break;
                case 2:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayDl, ...$arrayQnam, ...$arrayCommon]));

                    break;
                case 3:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayKh, ...$arrayDnang, ...$arrayCommon]));

                    break;
                case 4:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayBd, ...$arrayQb, ...$arrayQt, ...$arrayCommon]));

                    break;
                case 5:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayGl, ...$arrayNt, ...$arrayCommon]));

                    break;
                case 6:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayDnang, ...$arrayDnong, ...$arrayQngai, ...$arrayCommon]));

                    break;
                case 7:
                    $this->throwMessageSideNotAllow($this->checkValueOfArrayInArrayOther($arrayDai, [...$arrayKt, ...$arrayKh, ...$arrayTth, ...$arrayCommon]));
                    break;
            }
        }


    }

    function checkDaiExist($arrayDai, $dai)
    {

        $array_dai_mn = ['dc', 'dp', 'daichanh', 'dai chanh', 'daiphu', 'dai phu', 'ag', 'angiang', 'an giang', 'bl', 'blieu', 'baclieu', 'bac lieu', 'btre', 'bt', 'bentre', 'ben tre', 'bd', 'bduong', 'sb', 'binhduong', 'binh duong', 'bp', 'bphuoc', 'binhphuoc', 'binh phuoc', 'cm', 'camau', 'ca mau', 'ct', 'ctho', 'cantho', 'can tho',
            'dl', 'dlat', 'dalat', 'da lat', 'dn',
            'dnai', 'dongnai', 'dn', 'dong nai', 'dt', 'dthap', 'dongthap',
            'dong thap', 'hg', 'hgiang', 'haugiang', 'kg', 'kgiang', 'kiengiang',
            'kien giang', 'la', 'lan', 'longan', 'long an', 'st', 'strang', 'soctrang',
            'soc trang', 'tn', 'tninh', 'tayninh', 'tay ninh', 'tg', 'tgiang', 'tien giang', 'tiengiang', 'tp', 'hcm',
            'tv', 'tvinh', 'travinh', 'tra vinh', 'vl', 'vlong', 'vinhlong', 'vinh long', 'vt', 'vtau', 'vungtau', 'vung tau', 'vung tau', '2d', '3d', '4d',
            '2dai', '3dai', '4dai'];

        $array_dai_mt = ['dc', 'dp', 'daichanh', 'dai chanh', 'daiphu', 'dai phu', 'bdinh', 'binhdinh', 'binh dinh', 'bd', 'dlak', 'daklak', 'dak lak',
            'dlac', 'daclac', 'dac lac', 'dl', 'dnang', 'danang', 'da nang', 'dk', 'dknong', 'daknong', 'dak nong', 'dn', 'glai', 'gialai', 'gia lai', 'gl', 'khoa', 'khanhhoa', 'khanh hoa', 'kh', 'ktum', 'kontum', 'kon tum', 'kt', 'nthuan', 'ninhthuan', 'ninh thuan', 'nt', 'pyen', 'phyen', 'phuyen', 'phu yen', 'py',
            'qbinh', 'quangbinh', 'quang binh', 'qb', 'qngai', 'quangngai', 'quang ngai', 'qg', 'qn', 'qnam', 'quangnam', 'quang nam', 'qtri', 'quangtri',
            'quang tri', 'qt', 'thuathienhue', 'tt', 'thua thien hue', 'th', 'hue', '2d', '3d', '2dai', '3dai'];


        foreach ($arrayDai as $d) {
            if ($dai == 1 && !in_array($d, $array_dai_mn)) {
                throw new \Exception("Đài không hợp lệ.");
            } elseif ($dai == 3 && !in_array($d, $array_dai_mt)) {
                throw new \Exception("Đài không hợp lệ.");
            }
        }
    }

    function checkTypeAndMoneyValid($array, $arrayType, $isLimit = false)
    {

        $check = 0;
        for ($i = 0; $i < count($array); $i++) {
            // Kiểm tra xem chuỗi có định dạng số thực không
            if (is_numeric(str_replace(',', '.', $array[$i]))) {
                // Nếu có, chuyển đổi thành số thực
                $array[$i] = (float)str_replace(',', '.', $array[$i]);
            }
            if ($check == 0 && $array[$i] == 'd') {
                $array[$i] = 'dau';
                $check = 1;
            }
            if ($check == 1 & $array[$i] == 'd') {
                $array[$i] = 'duoi';

            }
        }
        if (!$isLimit && !in_array("xcd", $arrayType)) {


            foreach ($arrayType as $each) {
                $keys = array_search($each, $array);

                if (isset($array[$keys + 1]) && !is_numeric($array[$keys + 1]) && is_string($array[$keys + 1])) {

                    throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra");
                }
            }
        }

    }

    function checkTypeNotAllowWithN($str)
    {
        if (preg_match("/\d+\s+\d+\s*([n,k][a-zA-Z]+)/", $str, $matches)) {

            throw new \Exception("Loại đánh ko hợp lệ: {$matches[1]}");
        }
    }

    function checkMoneyRedundant($str)
    {


        $pattern = '/(\D+)(\d+)?/';
        $result = preg_split($pattern, $str, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);


        $result = array_values(array_filter(array_map('trim', $result)));

        $positions_n = array_keys($result, "n");
        $positions_k = array_keys($result, "k");

        foreach ($positions_n as $position) {
            if (isset($result[$position - 2]) && is_numeric($result[$position - 2])) {

                throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra: {$str}");
            }
        }

        foreach ($positions_k as $position) {
            if (isset($result[$position - 2]) && is_numeric($result[$position - 2])) {
                throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra: {$str}");
            }
        }
    }

    function checkLastStringRedundant($str)
    {

        $pattern = '/(\D+)(\d+)?/';
        $result = preg_split($pattern, $str, -1, PREG_SPLIT_NO_EMPTY | PREG_SPLIT_DELIM_CAPTURE);


        $result = array_values(array_filter(array_map('trim', $result)));
        $lastIndex = count($result) - 1;
        if (is_string($result[$lastIndex]) && !is_numeric($result[$lastIndex])) {
            throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra: {$str}");
        }

        if (is_numeric($result[$lastIndex - 1])) {
            throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra: {$str}");
        }

    }


    function checkStringHasOnlyD($str)
    {
        if (preg_match("/(?<=\d)\s*d(?=\d)\s*/", $str, $matches)) {
            if (!preg_match("/((?<=\d)\s*(d\s*(\d+|\d+[,|.]?\d+)\s*d)+)\s*(\d+[.|,]\d+|\d+)/", $str, $matches)) {
                throw new \Exception("Cú pháp đánh không hợp lệ vui lòng kiểm tra: {$str}");
            }
        }
    }

    function checkDao3digit($array)
    {
        $str_so_err = implode(" ", $array);

        $valueCounts = array_count_values($array);
        $arrCheck =
            ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'
                , 'dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao',
                'xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi',
                'xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau',
                'baylodao', 'daobaylo'];
        foreach ($valueCounts as $value => $count) {
            if ($count >= 2 && in_array($value, $arrCheck)) {
                throw new \Exception("Số đánh: '" . $str_so_err . "', Loại đánh {$value} không được đánh nhiều lần với 3 số.");
            }
        }
    }

    function checkTypeDuplication($array)
    {
        $arrayCheck = ['xc', 'x', 'xiu', 'tl', 'tieulo'];
        $errorFound = false;

        for ($i = 0; $i < count($array); $i++) {

            if (in_array($array[$i], $arrayCheck)) {

                if (isset($array[$i + 1]) && in_array($array[$i + 1], $arrayCheck)) {
                    $errorFound = true;
                    break;
                }
            }
        }


        if ($errorFound) {
            $str_so_err = implode(" ", $array);

            throw new \Exception("Số đánh: '" . $str_so_err . "', Cú pháp đánh sai loại đánh đang bị dư thừa.");
        }
    }

    function checkDandD($array)
    {
        $str_so_err = implode(" ", $array);
        $position = array_search("d", $array);
        $valueCounts = array_count_values($array);

        if ($position !== false) {
            if (isset($valueCounts["d"]) && $valueCounts["d"] === 1) {

                throw new \Exception("Số đánh: '" . $str_so_err . "', Cú pháp đánh sai d không là loại đánh vui lòng thay đổi thành d d hoặc da");
            }
            if (isset($array[$position + 2]) && $array[$position + 2] !== "d") {
//                dd($array,$position);
                throw new \Exception("Số đánh: '" . $str_so_err . "', Cú pháp đánh sai d không là loại đánh vui lòng thay đổi thành d d hoặc da");
            }
        }

    }

    function checkLengthDa($so)
    {
        $doDai = strlen((string)$so);
        return ($doDai == 1 || $doDai == 3 || $doDai == 4);
    }

    function checkArrayDa($mang)
    {
        $str_so_err = implode(" ", $mang);
        foreach ($mang as $so) {
            if (is_numeric($so) && $this->checkLengthDa($so)) {
                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 số trở lên");

            }
        }
    }

    function isNumberForXCD($mang)
    {

        foreach ($mang as $so) {
            if (is_numeric($so) && $this->checkLengthDa($so)) {
                return true;

            }
        }
        return false;
    }

    function swap(&$array, $index1, $index2)
    {
        $temp = $array[$index1];
        $array[$index1] = $array[$index2];
        $array[$index2] = $temp;
    }

    function checkDuplicationNumberInArray($array, $type)
    {
        $valueCounts = array_count_values($array);

        $duplicateValues = array_filter($valueCounts, function ($count) {
            return $count > 1;
        });
        $nums = [];
        if (!empty($duplicateValues)) {
            foreach ($duplicateValues as $value => $count) {
                $nums[] = $value;
            }
        }

        if (!empty($nums)) {
            $strNum = implode(", ", $nums);
            $type = implode(", ", $type);
            throw new \Exception("Số đánh {$strNum} trong loại đánh {$type} đã bị trùng");
        }
    }

    function replaceWithRules($inputString, $rules)
    {
        $length = 0;
        if (preg_match('/(^([^\d]+)|2d |3d |4d |2dai |3dai|4dai)/', ltrim($inputString, " . "), $matches)) {

            $cum_ki_tu_dau_tien = $matches[1];
            $length = strlen($cum_ki_tu_dau_tien);
        }
        $str_sub = substr($inputString, 0, $length);
//        $str_sub = preg_replace("/\./", "", $str_sub);
        $str_sub .= "";
        $inputString = $str_sub . substr($inputString, $length);

        foreach ($rules as $before => $after) {

            $inputString = str_replace($before, $after, $inputString);
        }
        return $inputString;
    }

    function catchRegionInline($inputString, $allRegion)
    {

//        if (preg_match("/\s+({$allRegion})\s+|\.?n\.?({$allRegion})\.?\s+\.?/", $inputString, $matches)) {
//            $matched_value = $matches[0];
////            dd($inputString,"/\s+({$allRegion})\s+|\.?n\.?({$allRegion})\.?\s+\.?/");
////            echo "Giá trị khớp: " . $matched_value;
//            throw new \Exception("Lỗi liên quan đến đài trên hàng!!");
//        }


    }

    function checkArrayElements4Digit($arr)
    {
        foreach ($arr as $element) {

            if (!is_numeric($element) || strlen($element) !== 4) {
                return false;
            }
        }
        return true;
    }

    function checkArrayElements3Digit($arr)
    {
        foreach ($arr as $element) {
            // Kiểm tra mỗi phần tử trong mảng có đúng 4 chữ số không
            if (!is_numeric($element) || strlen($element) !== 3) {
                return false;
            }
        }
        return true;
    }


    private function find_different_positions($num1, $num2)
    {
        $digits1 = str_split($num1);
        $digits2 = str_split($num2);

        $length = max(count($digits1), count($digits2));

        $different_positions = [];

        for ($i = 0; $i < $length; $i++) {
            if (isset($digits1[$i]) && isset($digits2[$i]) && $digits1[$i] !== $digits2[$i]) {
                $different_positions[] = $i + 1; // Thêm số thứ tự của vị trí khác nhau vào mảng
            }
        }

        return $different_positions;
    }

    private function getNumbersSame($num1, $num2)
    {
        $current = $num1;

        $numbers = array();

        while ($current <= $num2) {
            $numbers[] = $current;
            $digits = str_split($current);

            $increment = 11;
            if (strlen($num1) == 2) {

                $increment = 11;
            } else if (strlen($num1) == 3) {
                $increment = 111;
            } else if (strlen($num1) == 4) {
                $increment = 1111;
            }

            if ($digits[0] == $digits[1]) {

                $current += $increment;
            } else {

                for ($i = 2; $i >= 0; $i--) {
                    if ($digits[$i] != 9) {
                        $digits[$i]++;
                        break;
                    } else {
                        $digits[$i] = 0;
                    }
                }

                $current = implode('', $digits);
            }
        }

        return implode(' ', $numbers);
    }

    function convertStringForKeo($str, $matchedString)
    {
        $str_number = "";
        $numbersOnly = preg_replace('/\D+/', ' ', $matchedString);

        $numbersArray = explode(' ', $numbersOnly);
        $numbers = array_slice($numbersArray, 0, 2);
        preg_match('/\D+/', $matchedString, $matches2);

        $str_target = $matches2[0];


        $num1 = $numbers[0];
        $num2 = $numbers[1];

        if ($num1 == $num2) {
            throw new \Exception("Số đánh kéo không hợp lệ");
        }

        if (!$this->has_different_digits($num1) && !$this->has_different_digits($num2)) {

            $str_number .= $this->getNumbersSame($num1, $num2);
        } else {


            $different_positions = $this->find_different_positions($num1, $num2);

            if (!empty($different_positions)) {

                if (count($different_positions) == 1) {

                    $index = $different_positions[0];
                    $num_sub = 1;
                    if ($index == 1) {
                        if (strlen($num1) == 2) {
                            $num_sub = 10;
                        } elseif (strlen($num1) == 3) {
                            $num_sub = 100;
                        } elseif (strlen($num1) == 4) {
                            $num_sub = 1000;
                        }
                    } else if ($index == 2) {
                        if (strlen($num1) == 2) {
                            $num_sub = 1;
                        } elseif (strlen($num1) == 3) {
                            $num_sub = 10;
                        } elseif (strlen($num1) == 4) {
                            $num_sub = 100;
                        }
                    } else if ($index == 3) {
                        if (strlen($num1) == 3) {
                            $num_sub = 1;
                        } elseif (strlen($num1) == 4) {
                            $num_sub = 10;
                        }
                    } else if ($index == 4) {
                        if (strlen($num1) == 4) {
                            $num_sub = 1;
                        }
                    }
                    $str_number .= $num1 . " ";
                    $temp = $num1;

                    if ($num1 === "00" && substr($num2, -1) == "0") {
                        $num1 = str_pad("1", strlen($num1), "0", STR_PAD_RIGHT);
                        $str_number .= $num1 . " ";
                    } else if ($num1 === "00" && substr($num2, -1) != "0") {
                        $num1 = str_pad("1", strlen($num1), "0", STR_PAD_LEFT);
                        $str_number .= $num1 . " ";
                    }

                    for ($i = (floor($num1 / $num_sub) % 10) + 1; $i < floor($num2 / $num_sub) % 10; $i++) {
//                        $str_number .= str_replace((string)(floor($num1 / $num_sub) % 10), (string)$i, (string)$num1) . " ";
                        if ($num_sub == 1) {
                            $str_number .= substr_replace($num1, (string)$i, strlen($num1) - 1, 1) . " ";
                        } elseif ($num_sub == 10) {
                            $str_number .= substr_replace($num1, (string)$i, strlen($num1) - 2, 1) . " ";
                        } else if ($num_sub == 100) {
                            $str_number .= substr_replace($num1, (string)$i, strlen($num1) - 3, 1) . " ";
                        } else if ($num_sub == 1000) {
                            $str_number .= substr_replace($num1, (string)$i, strlen($num1) - 4, 1) . " ";

                        }
                        $str_number . " ";

                    }
                    $str_number .= $num2 . " ";
                    $num1 = $temp;


                } elseif (substr($num1, 0, 1) == substr($num1, 1, 1)
                    && substr($num2, 0, 1) == substr($num2, 1, 1)
                    && count($different_positions) == 2 && isset($different_positions[0])
                    && isset($different_positions[1])
                    && $different_positions[0] == 1
                    && $different_positions[1] == 2) {

                    $result = "";
                    for ($i = $num1; $i <= $num2; $i += 110) {
                        $result .= sprintf('%03d', $i);

                        $result .= ' ';

                    }
                    $str_number = $result;

                } elseif (
                    substr($num1, 0, 1) == substr($num1, 1, 1) && substr($num1, 1, 1) == substr($num1, 2, 1)
                    && substr($num2, 0, 1) == substr($num2, 1, 1) && substr($num2, 1, 1) == substr($num2, 2, 1)
                    &&
                    count($different_positions) == 3 && isset($different_positions[0]) && isset($different_positions[1])
                    && isset($different_positions[2]) && $different_positions[0] == 1
                    && $different_positions[1] == 2 && $different_positions[2] == 3) {

                    $result = "";
                    for ($i = $num1; $i <= $num2; $i += 1110) {
                        $result .= sprintf('%04d', $i);

                        $result .= ' ';

                    }
                    $str_number = $result;

                } elseif (count($different_positions) == 2 && isset($different_positions[0]) && isset($different_positions[1])
                    && ($different_positions[0] == 2 && $different_positions[1] == 3)
                    ||
                    ($different_positions[0] == 3 && $different_positions[1] == 4)) {

                    $result = "";
                    for ($i = $num1; $i <= $num2; $i += 11) {
                        $result .= sprintf('%03d', $i);

                        $result .= ' ';

                    }
                    $str_number = $result;

                } else {

                    $str_number .= $num1 . " ";

                    for ($i = $num1 + 1; $i < $num2; $i++) {

                        if (strlen($i) == 1) {
                            $i = "0" . $i;
                        }
//                        else if (strlen($i) != strlen($num1) && str_starts_with($num1, "0")) {
//                            $i = "0" . $i;
//                        }
                        $str_number .= $i . " ";

                    }
                    $str_number .= $num2 . " ";

                }


            } else {
                $str_number .= $num1 . " ";
                for ($i = $num1 + 1; $i < $num2; $i++) {
                    $str_number .= $i . " ";
                }
                $str_number .= $num2 . " ";
            }
        }
        return preg_replace("/" . $num1 . $str_target . $num2 . "/", $str_number, $str);

    }

    function convertStringForKeoDen($str, $matchedString)
    {
        $str_number = "";

        $numbersOnly = preg_replace('/\D+/', ' ', $matchedString);

        $numbersArray = explode(' ', $numbersOnly);
        $numbers = array_slice($numbersArray, 0, 2);
        preg_match('/\D+/', $matchedString, $matches2);
        $str_target = $matches2[0];
        $num1 = $numbers[0];
        $num2 = $numbers[1];
        if ($num1 == $num2) {
            throw new \Exception("Số đánh kéo không hợp lệ");
        }
        $str_number .= $num1 . " ";

        for ($i = $num1 + 1; $i < $num2; $i++) {
            if (strlen($num1) == 2) {
                $str_number .= sprintf("%02d", $i) . " ";
            } else if (strlen($num1) == 3) {
                $str_number .= sprintf("%03d", $i) . " ";
            } else if (strlen($num1) == 4) {
                $str_number .= sprintf("%04d", $i) . " ";
            }


        }
        $str_number .= $num2 . " ";

        return preg_replace("/" . $num1 . $str_target . $num2 . "/", $str_number, $str);

    }

    function has_different_digits($num)
    {
        $digits = str_split($num);
        $first_digit = $digits[0];

        foreach ($digits as $digit) {
            if ($digit !== $first_digit) {

                return true;
            }
        }

        return false;
    }

    function parseTypeFight($str)
    {

        ## cai cu~
//        $pattern_pass_n = "/(?<=\d)(n|ng|ngan)\b/";

        $pattern_pass_n = "/(?<=\d)(n|ng|ngan|k)/";
        $str_first = preg_replace($pattern_pass_n, "", $str);
        if (preg_match("/[^a-zA-Z](?<=\d)?d(?=\d)?[^a-zA-Z]/", $str)) {

            $new_string_parse = preg_replace('/(?<=[^a-zA-Z])(?<=\d)?d\s*(?=\d)/', 'dau', $str_first, 1);

            $new_string_parse = preg_replace('/(?<=[^a-zA-Z])d(?=\s*\d\s*)/', ' duoi', $new_string_parse, 1);

            preg_match_all('/(?<=\d)?[a-zA-Z]+/', $new_string_parse, $matches);

            return $matches[0];

        } else {


            preg_match_all('/(?<=\d)?[a-zA-Z]+/', $str_first, $matches);

            return $matches[0];


        }


    }

    function getPriceForSplit4Digit($string)
    {

        if ($string[0] == "0") {
            if (preg_match('/^(?!.*[.,])\d+$/', $string)) {
                if (strlen($string) == 2) {
                    $string = $string[0] . "," . $string[1];

                }
            }
            $string = "0" . ltrim($string, '0');
        }


        return floatval(preg_replace('/[^0-9|^.|^,]+/', '', preg_replace('/,/', '.', $string)));
    }

    function getPrice($array)
    {

        $searchStrings = array('n', 'ng', 'ngan', 'k');
        $array_result = [];
        $array_loop = array_pop($array);

        foreach ($array as $value) {

            foreach ($searchStrings as $searchString) {

                if (str_contains($value, $searchString)) {
                    if (preg_replace('/[^0-9]/i', '', $value) != "") {
                        $array_result[] = intval(preg_replace('/[^0-9]/i', '', $value));
                    }
                    break;
                }
            }
        }
        $array_get_money = null;
//dd($array,implode('', $array));
        // lay tien ko co n vao
        if ($array_result == null) {
            preg_match_all('/(?<=[a-zA-Z])[0-9,|.]+(?=[a-zA-Z])/', implode('', $array), $matches);

            $result = [];

            foreach ($matches[0] as $number) {
                if ($number[0] == "0") {
                    if (preg_match('/^(?!.*[.,])\d+$/', $number)) {
                        if (strlen($number) == 2) {
                            $number = $number[0] . "," . $number[1];

                        }
                    }
                    $number = "0" . ltrim($number, '0');
                    $result[] = $number;
                } else {
                    $result[] = $number;
                }
            }

            $array_get_money = array_map('floatval', preg_replace('/,/', '.', $result));
        }


        if ($array_loop[0] == "0") {
            if (preg_match('/^(?!.*[.,])\d+$/', $array_loop)) {
                if (strlen($array_loop) == 2) {
                    $array_loop = $array_loop[0] . "," . $array_loop[1];

                }
            }
            $array_loop = "0" . ltrim($array_loop, '0');
        }


        $array_result [] = floatval(preg_replace('/[^0-9|^.|^,]+/', '', preg_replace('/,/', '.', $array_loop)));
//dd($array_result,$array_get_money);
        if (!empty($array_get_money)) {
            return array_merge($array_get_money, $array_result);
        } else {
            return $array_result;
        }
    }

    function convertArraySide($str, $dai, $temp_dai)
    {

        if (trim($str) == '') {
            $string = str_replace(' ', '', $temp_dai);
            //handle
            if ($dai == 2) {
                $string = "hn";
            } else
                if ($string == "2dai") {
                    $string = "2d";
                } elseif ($string == "3dai") {
                    $string = "3d";
                } elseif ($string == "4dai") {
                    $string = "4d";
                } elseif ($string == "angiang") {
                    $string = "ag";
                } elseif ($string == "baclieu") {
                    $string = "bl";
                } elseif ($string == "ben tre") {
                    $string = "bt";
                } elseif ($string == "binhduong") {
                    $string = "bd";
                } elseif ($string == "binhphuoc") {
                    $string = "bp";
                } elseif ($string == "binhthuan") {
                    $string = "bt";
                } elseif ($string == "camau") {
                    $string = "cm";
                } elseif ($string == "can tho") {
                    $string = "ct";
                } elseif ($string == "dalat") {
                    $string = "dl";
                } elseif ($string == "dongnai") {
                    $string = "dn";
                } elseif ($string == "dthap") {
                    $string = "dt";
                } elseif ($string == "kiengiang") {
                    $string = "kg";
                } elseif ($string == "long an") {
                    $string = "la";
                } elseif ($string == "lan") {
                    $string = "la";
                } elseif ($string == "soc trang") {
                    $string = "st";
                } elseif ($string == "tninh") {
                    $string = "tn";
                } elseif ($string == "tayninh") {
                    $string = "tn";
                } elseif ($string == "tiengiang") {
                    $string = "tg";
                } elseif ($string == "hcm") {
                    $string = "tp";
                } elseif ($string == "tvinh") {
                    $string = "tv";
                } elseif ($string == "travinh") {
                    $string = "tv";
                } elseif ($string == "vlong") {
                    $string = "vl";
                } elseif ($string == "vinh long") {
                    $string = "vl";
                } elseif ($string == "vungtau") {
                    $string = "vt";
                } elseif ($string == "bdinh") {
                    $string = "bd";
                } elseif ($string == "binh dinh") {
                    $string = "bd";
                } elseif ($string == "dak lak") {
                    $string = "dl";
                } elseif ($string == "dac lac") {
                    $string = "dl";
                } elseif ($string == "dnang") {
                    $string = "dn";
                } elseif ($string == "da nang") {
                    $string = "dn";
                } elseif ($string == "daknong") {
                    $string = "dk";
                } elseif ($string == "gia lai") {
                    $string = "gl";
                } elseif ($string == "khanh hoa") {
                    $string = "kh";
                } elseif ($string == "kon tum") {
                    $string = "kt";
                } elseif ($string == "ninhthuan") {
                    $string = "nt";
                } elseif ($string == "phyen") {
                    $string = "py";
                } elseif ($string == "phu yen") {
                    $string = "py";
                } elseif ($string == "qbinh") {
                    $string = "qb";
                } elseif ($string == "quangbinh") {
                    $string = "qb";
                } elseif ($string == "qngai") {
                    $string = "qn";
                } elseif ($string == "quangngai") {
                    $string = "qn";
                } elseif ($string == "quang nam") {
                    $string = "qn";
                } elseif ($string == "quang tri") {
                    $string = "qt";
                } elseif ($string == "hue") {
                    $string = "tt";
                } elseif ($string == "daichanh") {
                    $string = "dc";
                } elseif ($string == "dai chanh") {
                    $string = "dc";
                } elseif ($string == "dai phu") {
                    $string = "dp";
                } elseif ($string == "daiphu") {
                    $string = "dp";
                }
            $characters = str_split($string, 2);

            if (strlen($string) % 2 != 0) {

                throw new \Exception("Nhà đài không hợp lệ");

            }
            return ($characters);
        } else {
            $string = str_replace(' ', '', $str);

            if ($dai == 2) {
                $string = "hn";
            } else
                if ($string == "2dai") {
                    $string = "2d";
                } elseif ($string == "3dai") {
                    $string = "3d";
                } elseif ($string == "4dai") {
                    $string = "4d";
                } elseif ($string == "angiang") {
                    $string = "ag";
                } elseif ($string == "baclieu") {
                    $string = "bl";
                } elseif ($string == "ben tre") {
                    $string = "bt";
                } elseif ($string == "binhduong") {
                    $string = "bd";
                } elseif ($string == "binhphuoc") {
                    $string = "bp";
                } elseif ($string == "binhthuan") {
                    $string = "bt";
                } elseif ($string == "camau") {
                    $string = "cm";
                } elseif ($string == "can tho") {
                    $string = "ct";
                } elseif ($string == "dalat") {
                    $string = "dl";
                } elseif ($string == "dongnai") {
                    $string = "dn";
                } elseif ($string == "dthap") {
                    $string = "dt";
                } elseif ($string == "kiengiang") {
                    $string = "kg";
                } elseif ($string == "long an") {
                    $string = "la";
                } elseif ($string == "lan") {
                    $string = "la";
                } elseif ($string == "soctrang") {
                    $string = "st";
                } elseif ($string == "tninh") {
                    $string = "tn";
                } elseif ($string == "tayninh") {
                    $string = "tn";
                } elseif ($string == "tiengiang") {
                    $string = "tg";
                } elseif ($string == "hcm") {
                    $string = "tp";
                } elseif ($string == "tvinh") {
                    $string = "tv";
                } elseif ($string == "travinh") {
                    $string = "tv";
                } elseif ($string == "vlong") {
                    $string = "vl";
                } elseif ($string == "vinh long") {
                    $string = "vl";
                } elseif ($string == "vungtau") {
                    $string = "vt";
                } elseif ($string == "bdinh") {
                    $string = "bd";
                } elseif ($string == "binh dinh") {
                    $string = "bd";
                } elseif ($string == "dak lak") {
                    $string = "dl";
                } elseif ($string == "dac lac") {
                    $string = "dl";
                } elseif ($string == "dnang") {
                    $string = "dn";
                } elseif ($string == "da nang") {
                    $string = "dn";
                } elseif ($string == "daknong") {
                    $string = "dk";
                } elseif ($string == "gia lai") {
                    $string = "gl";
                } elseif ($string == "khanh hoa") {
                    $string = "kh";
                } elseif ($string == "kon tum") {
                    $string = "kt";
                } elseif ($string == "ninhthuan") {
                    $string = "nt";
                } elseif ($string == "phyen") {
                    $string = "py";
                } elseif ($string == "phu yen") {
                    $string = "py";
                } elseif ($string == "qbinh") {
                    $string = "qb";
                } elseif ($string == "quangbinh") {
                    $string = "qb";
                } elseif ($string == "qngai") {
                    $string = "qn";
                } elseif ($string == "quangngai") {
                    $string = "qn";
                } elseif ($string == "quang nam") {
                    $string = "qn";
                } elseif ($string == "quang tri") {
                    $string = "qt";
                } elseif ($string == "hue") {
                    $string = "th";
                } elseif ($string == "daichanh") {
                    $string = "dc";
                } elseif ($string == "dai chanh") {
                    $string = "dc";
                } elseif ($string == "dai phu") {
                    $string = "dp";
                } elseif ($string == "daiphu") {
                    $string = "dp";
                }

            $characters = str_split($string, 2);
            if (strlen($string) % 2 != 0) {

                throw new \Exception("Nhà đài không hợp lệ");

            }
            return ($characters);

        }


    }


    function get_special_case($string, $str_special)
    {

        $lastIndex = 0;
        $index_special_bl = strpos($string, $str_special);

        if (($index_special_bl) !== false) {
            $flag = false;

            for ($i = $index_special_bl + strlen($str_special); $i >= 0; $i--) {
                if (is_numeric($string[$i])) {
                    $flag = true;
                    break;
                }

            }

            if ($flag == false) {
                $lastIndex = strlen($str_special);
            } else {
                $lastIndex = 0;
            }
        }
        return $lastIndex;
    }

    function getIndexSlicePercent($string)
    {
        $lastIndex = 0;
        if (preg_match("/\d+%/", $string, $matches, PREG_OFFSET_CAPTURE)) {
            $lastIndex = ($matches[0][1] + strlen($matches[0][0]) - 1) + 1;

        }
        return $lastIndex;
    }

    function getLastIndex($index, $str, $count, &$last1, $all_type)
    {


        $pattern = "/^(\s*\.?({$all_type}|d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|,|\.)?|^(\.?\s?({$all_type}|d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|,|\.)?/i";
        $char = '';
        $last_index = 0;
        if ($count == 0) {
            for ($i = $index; $i < strlen($str); $i++) {
                $char .= $str[$i];
                // echo preg_match($pattern, $char);
            }
        } else {
            // vua +1 ngay 28/5
            for ($i = $last_index; $i < strlen($str); $i++) {

                $char .= $str[$i];
//                 echo preg_match($pattern, $char);
            }
        }

//        dd($index);
        if (!preg_match($pattern, $char)) {
            return $index;
        } else if (preg_match($pattern, $char, $matches_2, PREG_OFFSET_CAPTURE)) {
            $last_index += ($matches_2[0][1] + strlen($matches_2[0][0]) - 1);
            if (preg_match("/((d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|\.|,)?/", $char, $matchesf, PREG_OFFSET_CAPTURE)) {
                $last_index++;
            }
            if ($matches_2[sizeof($matches_2) - 1][0] == "." || $matches_2[sizeof($matches_2) - 1][0] == ",") {
                $last_index += 1;
            }
            $rest = substr($char, $last_index + 1);
            if ($count == 0) {

                //            dd($last_index);
                // vua them +1 ngay 28/5
                $last1 += $last_index;
            } else {
                //            dd($last_index);
                // vua them +1 ngay 28/5
                $last1 += $last_index + 1;
            }
            $this->getLastIndex($index, $rest, $count + 1, $last1, $all_type);


        }
        return $last1;
    }

    function getLastIndex_2($index, $str, $count, &$last, $all_type)
    {

        $pattern = "/^(\s*\.?({$all_type}|d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|\.|,)?|^(\.?\s?({$all_type}|d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|\.|,)?/i";
        $char = '';
        $last_index = 0;

        if ($count == 0) {
            for ($i = $index; $i < strlen($str); $i++) {
                $char .= $str[$i];
                // echo preg_match($pattern, $char);
            }
        } else {
            // vua +1 ngay 28/5
            for ($i = $last_index + 1; $i < strlen($str); $i++) {
                $char .= $str[$i];
//                 echo preg_match($pattern, $char);
            }
        }

        if (!preg_match($pattern, $char)) {

            return $index;
        } else if (preg_match($pattern, $char, $matches_2, PREG_OFFSET_CAPTURE)) {

            $last_index += ($matches_2[0][1] + strlen($matches_2[0][0]) - 1);
            if (preg_match("/((d\s*(\d+)\s*d)+)\s*\d+(n|ngan|ng|\.|,)?/", $char, $matchesf, PREG_OFFSET_CAPTURE)) {
                $last_index++;
            }
            if ($matches_2[sizeof($matches_2) - 1][0] == "." || $matches_2[sizeof($matches_2) - 1][0] == ",") {
                $last_index += 1;
            }
            $rest = substr($char, $last_index);
            if ($count == 0) {

                // vua sua ngay 24/6
//                $rest = substr($char, $last_index + 1);
                // vua them +1 ngay 28/5

                $last += $last_index;

            } else {

                //            dd($last_index);
//                $rest = substr($char, $last_index + 1);

                // vua them +1 ngay 28/5
                $last += $last_index + 1;
//                dd($matches_2,$last);

            }
            $this->getLastIndex_2($index, $rest, $count + 1, $last, $all_type);
        }
        return $last;
    }

    function checkNumberHasFirstString($str)
    {

        if ($str == "") {
            return 0;
        }
        if (ctype_alpha($str[0])) {
            return 1;
        } else if (ctype_digit($str[0])) {
            return 0;
        }

    }

    function splitString($str, $end_pos, $count, &$array, $all_type)
    {
//        echo "Index " . $end_pos . " ";
//        echo "Str " . $str . "- ";
        if ($count == 1) {
            $str = ltrim($str);
        }
        if ($count >= 1) {
//            echo "String : " . $str . "\n";
//            $pattern = "/({$all_type}|d)\s?+\d+(ngan|ng|n|.|,)?/i";
            $pattern = "/({$all_type}|d\s*(\d+(ngan|ng|n|,|\.)?\d?+)\s*d)\s*\d+(ngan|ng|n|\.|,)?/i";
//        $pattern = "/({$all_type}|d\s*(\d+(ngan|ng|n|,|\.)?\d?+)\s*d)\s*\d+(ngan|ng|n|,|\.)?/i"
            if (preg_match($pattern, $str, $matches_3, PREG_OFFSET_CAPTURE)) {
                $end_pos = $matches_3[0][1] + strlen($matches_3[0][0]) - 1;
                $end_pos++;

                if (
//                    preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos + 1))
//                    &&
                ($matches_3[sizeof($matches_3) - 1][0] == "." || $matches_3[sizeof($matches_3) - 1][0] == ",")) {
                    $end_pos += 1;
                }
                $last_1c = 0;
                $get_last_index = $this->getLastIndex($end_pos, $str, 0, $last_1c, $all_type);
                if ($end_pos == $get_last_index) {

                    if ($this->checkNumberHasFirstString(trim(substr($str, $get_last_index, $get_last_index))) == 1) {
                        $end_pos = $end_pos + $get_last_index;
                    }
                } else
                    if ($end_pos != $get_last_index) {
                        $end_pos = $end_pos + $get_last_index;
                    }

            } else {
                return;
            }
        }
        if (strlen(substr($str, $end_pos)) == 0) {

            $substring = substr($str, 0, $end_pos + 1);
            $array[] = $substring;

        } else {
            $pattern = "/({$all_type}|d\s*(\d+(ngan|ng|n|,|\.)?\d?+)\s*d)\s*\d+(ngan|ng|n|\.|,)?/i";
            if ($count >= 1) {
                if (preg_match($pattern, $str, $matches_4, PREG_OFFSET_CAPTURE)) {
                    $end_pos = $matches_4[0][1] + strlen($matches_4[0][0]) - 1;
                    $end_pos++;
                    if (
//                        preg_match("/d\s*\d+(\.\d+)?(\,\d+)?\s*d\s*\d+(\.\d+)?(\,\d+)?/", substr($str, 0, $end_pos + 1)) &&
                    ($matches_4[sizeof($matches_4) - 1][0] == "." || $matches_4[sizeof($matches_4) - 1][0] == ",")
                    ) {
                        $end_pos += 1;
                    }
                    $get_last_index = $this->getLastIndex($end_pos, $str, 0, $last, $all_type);
                    if ($end_pos == $get_last_index) {

                        if ($this->checkNumberHasFirstString(trim(substr($str, $get_last_index, $get_last_index))) == 1) {

                            $end_pos = $end_pos + $get_last_index;
                        }

                    } else
                        if ($end_pos != $get_last_index) {
                            // sua ngay 15/8  code cu  $end_pos = $end_pos + $get_last_index +1;

                            $end_pos = $end_pos + $get_last_index;
                        }
                } else {
                    return;
                }
            }

            $substring = substr($str, 0, $end_pos + 1);
            $array[] = $substring;

            $rest = substr($str, $end_pos + 1);
            // luu y trim string
            $this->splitString(($rest), $end_pos, $count + 1, $array, $all_type);


        }
        return $array;
    }

    public function getAllNumber($array, $index_start, $all_type)
    {
        $array_result = [];
        $k = 0;
        ///vua chinh sua ngay 6-5 vao 10h
        $pattern = "/\b(?!{$all_type}|d)\b[a-zA-Z]+/i";

        for ($i = $index_start - 1; $i >= 0; $i--) {
            if (preg_match($pattern, $array[$i])) {

                $k = 1;
            }
            if ($k == 0) {
                if (!preg_match("/[a-zA-Z]+/", $array[$i]) && $array[$i] != null) $array_result[] = $array[$i];

            }
        }


        return $array_result;
    }

    public function getEnumByName($name)
    {

        $array_dau = ['dau', 'dau'];
        $array_duoi = ['dui', 'duoi'];
        $array_dau_duoi = ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
        $array_bao_lo = ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'];
        $array_xiu_chu = ['xc', 'x', 'xiu', 'tl', 'tieulo'];
        $array_xiu_chu_dau = ['xcdau', 'dauxc', 'xdau'];
        $array_xiu_chu_duoi = ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'];
        $array_da = ['dt', 'da', 'dav', 'dv', 'dax', 'davg', 'davong', 'dth', 'dathang', 'thang', 'dat', 'dath',
            'dvong', 'dvog', 'davog', 'dav', 'da vong', 'da v', 'daxoay', 'da xoay', 'dacap', 'da cap', 'damoicap', 'vong da', 'vongda'];
        $array_da_xien = ['dx', 'xien', 'dx', 'dax', 'daxien', 'dc',
            'da xien', 'daxieng', 'da xieng', 'da xien', 'da x', 'da xuyen', 'da xuyen', 'daxuyen', 'dacheo', 'da cheo', 'cheo'];
        $array_danh_bay_lo = ['baylo'];
        $array_da_bay_lo_dao = ['baylodao', 'daobaylo'];
        $array_tam_lo = ['tamlo'];
        $array_tam_lo_dao = ['tamlodao', 'daotamlo'];
        $array_xiu_chu_dao_dau = ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau'];
        $array_xiu_chu_dao_duoi = ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'];
        $array_xiu_chu_dao = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'];
        $array_bao_lo_dao = ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'];


        $intersect_dau = array_search($name, $array_dau);
        $intersect_duoi = array_search($name, $array_duoi);
        $intersect_dau_duoi = array_search($name, $array_dau_duoi);
        $intersect_bao_lo = array_search($name, $array_bao_lo);
        $intersect_xiu_chu = array_search($name, $array_xiu_chu);
        $intersect_xiu_chu_dau = array_search($name, $array_xiu_chu_dau);
        $intersect_xiu_chu_duoi = array_search($name, $array_xiu_chu_duoi);
        $intersect_da = array_search($name, $array_da);
        $intersect_da_xien = array_search($name, $array_da_xien);
        $intersect_da_bay_lo = array_search($name, $array_danh_bay_lo);
        $intersect_da_bay_lo_dao = array_search($name, $array_da_bay_lo_dao);
        $intersect_tam_lo = array_search($name, $array_tam_lo);
        $intersect_tam_lo_dao = array_search($name, $array_tam_lo_dao);
        $intersect_xiu_chu_dao_dau = array_search($name, $array_xiu_chu_dao_dau);
        $intersect_xiu_chu_dao_duoi = array_search($name, $array_xiu_chu_dao_duoi);
        $intersect_xiu_chu_dao = array_search($name, $array_xiu_chu_dao);
        $intersect_bao_lo_dao = array_search($name, $array_bao_lo_dao);


        $results = array_filter([$intersect_dau, $intersect_duoi, $intersect_dau_duoi,
            $intersect_bao_lo, $intersect_xiu_chu, $intersect_xiu_chu_dau,
            $intersect_xiu_chu_duoi, $intersect_da, $intersect_da_xien, $intersect_da_bay_lo,
            $intersect_da_bay_lo_dao, $intersect_tam_lo, $intersect_tam_lo_dao, $intersect_xiu_chu_dao_dau, $intersect_xiu_chu_dao_duoi,
            $intersect_xiu_chu_dao, $intersect_bao_lo_dao], function ($item) {
            return ($item !== false && $item !== '' && $item !== null);
        });

        return array_keys($results);
    }

    public function getALLType($dai, $date)
    {


        $array_dau = ['dau', 'dau'];
        $array_duoi = ['dui', 'duoi'];
        $array_dau_duoi = ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
        $array_bao_lo = ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'baolo'];
        $array_xiu_chu = ['xc', 'x', 'xiu', 'tl', 'tieulo'];
        $array_xiu_chu_dau = ['xcdau', 'dauxc', 'xdau'];
        $array_xiu_chu_duoi = ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'];
        $array_da = ['da', 'dav', 'dv', 'dax', 'davg', 'davong', 'dth', 'dathang', 'thang', 'dat', 'dath',
            'dvong', 'dvog', 'davog', 'dav', 'da vong', 'da v', 'daxoay', 'da xoay', 'dacap', 'da cap', 'damoicap', 'vong da', 'vongda'];
        $array_da_xien = ['dx', 'xien', 'dx', 'dax', 'daxien', 'dc',
            'da xien', 'daxieng', 'da xieng', 'da xien', 'da x', 'da xuyen', 'da xuyen', 'daxuyen', 'dacheo', 'da cheo', 'cheo'];
        $array_danh_bay_lo = ['baylo'];
        $array_da_bay_lo_dao = ['baylodao', 'daobaylo'];
        $array_tam_lo = ['tamlo'];
        $array_tam_lo_dao = ['tamlodao', 'daotamlo'];
        //change 28/06/2023
        $array_xiu_chu_dao_dau = ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'daodau', 'dxcdau'];
        $array_xiu_chu_dao_duoi = ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'];
        $array_xiu_chu_dao = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'];
        $array_bao_lo_dao = ['baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'];
//dd(date('N', strtotime($date)),$date);
        if ($dai == TypeSideEnum::MN) {
            if (date('N', strtotime($date)) != 5) {
                $array_bao_lo[] = 'bd';

            }
            if (date('N', strtotime($date)) != 2) {
                $array_bao_lo[] = 'bl';

            }
            if (date('N', strtotime($date)) != 1) {
                $array_bao_lo_dao[] = 'dt';

            }
        } else if ($dai == TypeSideEnum::MT) {
            if (date('N', strtotime($date)) != 4) {
                $array_bao_lo_dao[] = 'bd';

            }
            $array_bao_lo_dao[] = 'dt';
            $array_bao_lo[] = 'bl';
        } else {
            $array_bao_lo[] = 'bd';
            $array_bao_lo_dao[] = 'dt';
            $array_bao_lo[] = 'bl';
        }


        return implode("|", array_merge($array_dau, $array_duoi, $array_dau_duoi, $array_bao_lo, $array_xiu_chu, $array_xiu_chu_dau, $array_xiu_chu_duoi,
            $array_da, $array_da_xien, $array_danh_bay_lo, $array_da_bay_lo_dao, $array_tam_lo, $array_tam_lo_dao, $array_xiu_chu_dao_dau, $array_xiu_chu_dao_duoi,
            $array_xiu_chu_dao, $array_bao_lo_dao));

    }

    public function convertArrayToStringRegex($array)
    {


        return implode("|", $array);

    }

    public function getTypeFight(
        $arr_slice
    )
    {

        $array_dau = ['dau', 'dau'];
        $array_duoi = ['dui', 'duoi'];
        $array_dau_duoi = ['dauduoi', 'dd', 'dđ', 'đđ', 'đd'];
        $array_bao_lo = ['bao', 'lo', 'b', 'bada', 'baol', 'balo', 'blo', 'bao', 'ba', 'bl', 'baolo'];
        $array_xiu_chu = ['xc', 'x', 'xiu', 'tl', 'tieulo'];
        $array_xiu_chu_dau = ['xcdau', 'dauxc', 'xdau'];
        $array_xiu_chu_duoi = ['xcduoi', 'duoixc', 'xcdui', 'xduoi', 'xdui'];
        $array_da = ['dt', 'da', 'dav', 'dv', 'dax', 'davg', 'davong', 'dth', 'dathang', 'thang', 'dat', 'dath',
            'dvong', 'dvog', 'davog', 'dav', 'da vong', 'da v', 'daxoay', 'da xoay', 'dacap', 'da cap', 'damoicap', 'vong da', 'vongda'];
        $array_da_xien = ['dx', 'dxv', 'dcv', 'dvx', 'xien', 'dx', 'daxv', 'davx', 'dax', 'daxien', 'dc',
            'da xien', 'daxieng', 'da xieng', 'da xien', 'da x', 'da xuyen', 'da xuyen', 'daxuyen', 'dacheo', 'da cheo', 'cheo'];
        $array_danh_bay_lo = ['baylo'];
        $array_da_bay_lo_dao = ['baylodao', 'daobaylo'];
        $array_tam_lo = ['tamlo'];
        $array_tam_lo_dao = ['tamlodao', 'daotamlo'];
        $array_xiu_chu_dao_dau = ['xcddau', 'xcdaud', 'tldaodau', 'tieulodaodau', 'tlddau', 'daodau', 'dxcdau'];
        $array_xiu_chu_dao_duoi = ['xcdduoi', 'xcduoid', 'tldaoduoi', 'tieulodaoduoi', 'tldduoi', 'daodui', 'daodui', 'dxcdui', 'dxcduoi', 'daodui', 'daoduoi'];
        $array_xiu_chu_dao = ['dxc', 'xcd', 'daoxc', 'xcdao', 'daox', 'xdao', 'tld', 'tieulodao', 'xd', 'dao xc', 'xc dao'];
        $array_bao_lo_dao = ['bd', 'baodao', 'bdao', 'bld', 'bldao', 'daolo', 'dao b', 'b dao', 'daolo', 'dlo', 'lodao', 'dao', 'lodao'];
        $array_dd = ['d'];


        $intersect_dau = array_intersect($arr_slice, $array_dau);
        $intersect_duoi = array_intersect($arr_slice, $array_duoi);
        $intersect_dau_duoi = array_intersect($arr_slice, $array_dau_duoi);
        $intersect_bao_lo = array_intersect($arr_slice, $array_bao_lo);
        $intersect_xiu_chu = array_intersect($arr_slice, $array_xiu_chu);
        $intersect_xiu_chu_dau = array_intersect($arr_slice, $array_xiu_chu_dau);
        $intersect_xiu_chu_duoi = array_intersect($arr_slice, $array_xiu_chu_duoi);
        $intersect_da = array_intersect($arr_slice, $array_da);
        $intersect_da_xien = array_intersect($arr_slice, $array_da_xien);
        $intersect_danh_bay_lo = array_intersect($arr_slice, $array_danh_bay_lo);
        $intersect_da_bay_lo_dao = array_intersect($arr_slice, $array_da_bay_lo_dao);
        $intersect_tam_lo = array_intersect($arr_slice, $array_tam_lo);
        $intersect_tam_lo_dao = array_intersect($arr_slice, $array_tam_lo_dao);
        $intersect_xiu_chu_dao_dau = array_intersect($arr_slice, $array_xiu_chu_dao_dau);
        $intersect_xiu_chu_dao_duoi = array_intersect($arr_slice, $array_xiu_chu_dao_duoi);
        $intersect_xiu_chu_dao = array_intersect($arr_slice, $array_xiu_chu_dao);
        $intersect_bao_lo_dao = array_intersect($arr_slice, $array_bao_lo_dao);
        $intersect_dd = array_intersect($arr_slice, $array_dd);

        // Lấy ra vị trí của các giá trị trùng nhau trong $array1
        $results = array_filter([$intersect_dau, $intersect_duoi, $intersect_dau_duoi, $intersect_bao_lo, $intersect_xiu_chu,
            $intersect_xiu_chu_dau, $intersect_xiu_chu_duoi, $intersect_da, $intersect_da_xien, $intersect_danh_bay_lo, $intersect_da_bay_lo_dao,
            $intersect_tam_lo, $intersect_tam_lo_dao, $intersect_xiu_chu_dao_dau, $intersect_xiu_chu_dao_duoi, $intersect_xiu_chu_dao, $intersect_bao_lo_dao, $intersect_dd], function ($item) {
            return count($item) > 0;
        });


        if (array_key_exists(17, $results) && sizeof($results[17]) == 2) {

            $results [0] = [array_keys($results[17])[0] => "dau"];
            $results [1] = [array_keys($results[17])[1] => "duoi"];


            unset($results[17]);
            usort($results, function ($a, $b) {
                // Lấy key của hai phần tử
                $key_a = array_keys($a)[0];
                $key_b = array_keys($b)[0];

                // So sánh key và trả về giá trị âm, bằng hoặc dương để xác định thứ tự
                if ($key_a < $key_b) {
                    return 1;
                } elseif ($key_a > $key_b) {
                    return -1;
                } else {
                    return 0;
                }
            });
        } else {
            usort($results, function ($a, $b) {
                // Lấy key của hai phần tử
                $key_a = array_keys($a)[0];
                $key_b = array_keys($b)[0];

                // So sánh key và trả về giá trị âm, bằng hoặc dương để xác định thứ tự
                if ($key_a < $key_b) {
                    return 1;
                } elseif ($key_a > $key_b) {
                    return -1;
                } else {
                    return 0;
                }
            });
        }
        if (empty($results)) {
            throw new \Exception("Cú pháp đánh sai vui lòng kiểm tra lại");
        }

        return array_keys(array_values($results)[sizeof($results) - 1]);

    }

    public function getValueWithLength($numbers, $length)
    {

        return array_filter($numbers, function ($value) use ($length) {
            return strlen((string)$value) === $length;
        });
    }


    public function calculator($type,
                               $so_danh,
                               $so_tien_danh,
                               $array_dai,
                               $dai,
                               $customer_id,
                               $date_check,
                               $type_detail,
                               $is_refresh = false,
                               $ration_user = null,
                               $numberCalculate = null)
    {
        foreach ($so_danh as $so) {
            if (strlen($so) == 1) {
                throw new \Exception("Sai cú pháp đánh");
            }
            if (strlen($so) >= 5) {
                throw new \Exception("Sai cú pháp đánh");
            }
        }
        if ($ration_user == null) {
            $ration_user = Ration::query()
                ->where('customer_id', '=', $customer_id)
                ->where('type', '=', TypeUser::User)
                ->where('region_id', $dai)->get()->first();
        }
        $get_dai = $this->getALLSide($array_dai, $dai);

        if ($dai == 3) {
            if (count($get_dai) == 3 || in_array(4, $get_dai)) {
                $this->checkMiddleFightSide($date_check);

            }
        }
        if ($dai == 1) {
            if (count($get_dai) == 4 || in_array(5, $get_dai)) {
                $this->checkSouthFightSide($date_check);

            }
        }

        $array_result = [];
        $sum_2_total = 0;
        $sum_3_total = 0;
        $sum_4_total = 0;
        $hai_so = 0;
        $ba_so = 0;
        $bon_so = 0;
        $DD = 0;
        $XC = 0;
        $DATHANG = 0;
        $DAXIEN = 0;
        $sum_2_total_percent = 0;
        $sum_3_total_percent = 0;
        $sum_4_total_percent = 0;

        $list_area = $this->convertMultipleArea($array_dai, $date_check, $dai);
        $count_area = count($list_area);
        $result_tung_dai = array_fill_keys($list_area, [
            'hai_so' => 0,
            'ba_so' => 0,
            'bon_so' => 0,
            'dau_duoi' => 0,
            'bao_lo_2_so' => 0,
            'bao_lo_3_so' => 0,
            'bao_lo_4_so' => 0,
            'xiu_chu' => 0,
            'da_thang' => 0,
            'da_xien' => 0,
            'total_danh' => 0,
            'so_danh' => [],
            'tra_lai' => 0,
        ]);

        foreach ($type as $type_each) {
            $val_sw = $type_each;

            switch ($val_sw) {

                case TypeFight::DAU:
                    $sum_xc_dau_3 = 0;
                    $sum_dau_2 = 0;
                    $number_xc = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);


                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        $number = 4;
                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(0, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    $hasTwoDigits = false;
                    $hasThreeDigits = false;
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $hasTwoDigits = true;

                            $sum_dau_2 += $tien_danh * $number;
                        } elseif (strlen($so_danh_each) == 3) {

                            $hasThreeDigits = true;

                            if ($get_dai[0] == 3) {
                                $number_xc = 2;
                            } elseif ($get_dai[0] == 4) {
                                $number_xc = 3;
                            } elseif ($get_dai[0] == 5) {
                                $number_xc = 4;
                            } else {
                                $count_dai = count($get_dai);

                                $number_xc = 1 * $count_dai;
                            }
                            if ($dai == 2) {
                                foreach ($get_dai as $each) {

                                    if ($each == 1) {
                                        $number_xc = 3;

                                    }

                                }
                            }
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(0, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }
                            $sum_xc_dau_3 += $tien_danh * $number_xc;
                        }

                    }
                    if (!$is_refresh) {
                        if ($hasTwoDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::HAI_CON_DD, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }
                    }


                    $sum_2_total += $sum_dau_2;

                    $sum_2_total_percent += $sum_dau_2 * $ration_user->dau_duoi_percent;
                    $DD += $sum_dau_2;


                    $sum_3_total += $sum_xc_dau_3;
                    $XC = $sum_xc_dau_3;

                    $sum_3_total_percent += $sum_xc_dau_3 * $ration_user->xiu_chu_percent;

                    //Tính theo từng đài
                    $hai_so_area = ($sum_dau_2 * $ration_user->dau_duoi_percent) / $count_area;
                    $ba_so_area = ($sum_xc_dau_3 * $ration_user->xiu_chu_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.a';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];

                        }
                        // $area['hai_so'] += $hai_so_area;
                        $area['dau_duoi'] += $hai_so_area;
                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area + $hai_so_area;
                    }
                    break;
                case TypeFight::DUOI:

                    $sum_xc_duoi = 0;
                    $sum_duoi_2 = 0;
                    $sum_duoi_4 = 0;
                    $number_xc = 0;

                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 1 * $count_dai;
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(1, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    $hasTwoDigits = false;
                    $hasThreeDigits = false;
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $hasTwoDigits = true;
                            $sum_duoi_2 += $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 3) {

                            $hasThreeDigits = true;

                            if ($get_dai[0] == 3) {
                                $number_xc = 2;
                            } elseif ($get_dai[0] == 4) {
                                $number_xc = 3;
                            } elseif ($get_dai[0] == 5) {
                                $number_xc = 4;
                            } else {
                                $count_dai = count($get_dai);
                                $number_xc = 1 * $count_dai;
                            }
                            if ($dai == 2) {
                                foreach ($get_dai as $each) {

                                    if ($each == 1) {
                                        $number_xc = 1;

                                    }

                                }
                            }
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(1, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            $sum_xc_duoi += $tien_danh * $number_xc;


                        } elseif (strlen($so_danh_each) == 4) {
                            $sum_duoi_4 += $tien_danh * $number;

                        }
                    }

                    if (!$is_refresh) {

                        if ($hasTwoDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::HAI_CON_DD, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }
                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }
                    }

                    $sum_2_total += $sum_duoi_2;
                    $sum_4_total += $sum_duoi_4;

                    $sum_2_total_percent += $sum_duoi_2 * $ration_user->dau_duoi_percent;
                    $sum_4_total_percent += $sum_duoi_4 * $ration_user->bon_so_percent;
                    $DD += $sum_duoi_2;
                    $bon_so += $sum_duoi_4;

                    $sum_3_total += $sum_xc_duoi;

                    $XC += $sum_xc_duoi;
                    $sum_3_total_percent += $sum_xc_duoi * $ration_user->xiu_chu_percent;

                    //Tính theo từng đài
                    $hai_so_area = ($sum_duoi_2 * $ration_user->dau_duoi_percent) / $count_area;
                    $ba_so_area = ($sum_xc_duoi * $ration_user->xiu_chu_percent) / $count_area;
                    $bon_so_area = ($sum_duoi_4 * $ration_user->bon_so_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.u';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }
                        // $area['hai_so'] += $hai_so_area;
                        $area['dau_duoi'] += $hai_so_area;
                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        // $area['bon_so'] += $bon_so_area;
                        $area['total_danh'] += $hai_so_area + $ba_so_area + $bon_so_area;
                    }
                    break;
                case TypeFight::DAU_DUOI:
                    $sum_xc_3 = 0;
                    $sum_dau_duoi = 0;
                    $number_xc = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2 * 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3 * 2;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4 * 2;
                    } else {
                        $count_dai = count($get_dai);

                        $number = 2 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 5;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(2, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    $hasTwoDigits = false;
                    $hasThreeDigits = false;

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $hasTwoDigits = true;
                            $sum_dau_duoi += $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 3) {
                            $hasThreeDigits = true;

                            if ($get_dai[0] == 3) {
                                $number_xc = 2 * 2;
                            } elseif ($get_dai[0] == 4) {
                                $number_xc = 3 * 2;
                            } elseif ($get_dai[0] == 5) {
                                $number_xc = 4 * 2;
                            } else {
                                $count_dai = count($get_dai);

                                $number_xc = 2 * $count_dai;
                            }
                            if ($dai == 2) {
                                foreach ($get_dai as $each) {
                                    if ($each == 1) {
                                        $number_xc = 4;
                                    }
                                }
                            }
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(2, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }


                            $sum_xc_3 += $tien_danh * $number_xc;
                        }
                    }
                    if (!$is_refresh) {

                        if ($hasTwoDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::HAI_CON_DD, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));


                        }
                    }
                    $sum_2_total += $sum_dau_duoi;
                    $sum_2_total_percent += $sum_dau_duoi * $ration_user->dau_duoi_percent;
                    $DD += $sum_dau_duoi;

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;
                    $sum_3_total_percent += $sum_3_total * $ration_user->xiu_chu_percent;

                    //Tính theo từng đài
                    $hai_so_area = ($sum_dau_duoi * $ration_user->dau_duoi_percent) / $count_area;
                    $ba_so_area = ($sum_xc_3 * $ration_user->xiu_chu_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.d';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        // $area['hai_so'] += $hai_so_area;
                        $area['dau_duoi'] += $hai_so_area;

                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $hai_so_area + $ba_so_area;
                    }
                    break;
                case TypeFight::BAO_LO:
                    $sum_3 = 0;

                    $sum_4 = 0;
                    $sum_2 = 0;
                    $number = 0;

                    foreach ($get_dai as $each) {

                        if ($each == 1) {
                            $number = 27;

                        } elseif ($each == 0 || $each == 2) {
                            $number += 18;

                        } elseif ($each == 3) {
                            $number = 36;
                        } elseif ($each == 4) {
                            $number = 54;
                        } elseif ($each == 5) {
                            $number = 72;
                        }

                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(3, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    $hasTwoDigits = false;
                    $hasThreeDigits = false;
                    $hasFourDigits = false;

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $hasTwoDigits = true;
                            if ($numberCalculate != null) {
                                $sum_2 += $tien_danh * $numberCalculate;

                            } else {
                                $sum_2 += $tien_danh * $number;

                            }


                        } else if (strlen($so_danh_each) == 3) {
                            $hasThreeDigits = true;
                            if ($dai == 2) {
                                $sum_3 += $tien_danh * ($number - 4);

                            } elseif (in_array(3, $get_dai) || sizeof($get_dai) == 2) {
                                $sum_3 += $tien_danh * ($number - 2);
                            } elseif (in_array(4, $get_dai) || sizeof($get_dai) == 3) {
                                $sum_3 += $tien_danh * ($number - 3);
                            } elseif (in_array(5, $get_dai) || sizeof($get_dai) == 4) {
                                $sum_3 += $tien_danh * ($number - 4);
                            } else {
                                if ($numberCalculate != null) {
                                    $sum_3 += $tien_danh * $numberCalculate;
                                } else {
                                    $sum_3 += $tien_danh * ($number - 1);

                                }

                            }

                        } else if (strlen($so_danh_each) == 4) {
                            $hasFourDigits = true;
                            if ($dai == 2) {
                                $sum_4 += $tien_danh * ($number - 7);
                            } elseif (in_array(3, $get_dai) || sizeof($get_dai) == 2) {
                                $sum_4 += $tien_danh * ($number - 4);
                            } elseif (in_array(4, $get_dai) || sizeof($get_dai) == 3) {
                                $sum_4 += $tien_danh * ($number - 6);
                            } elseif (in_array(5, $get_dai) || sizeof($get_dai) == 4) {
                                $sum_4 += $tien_danh * ($number - 8);
                            } else {

                                $sum_4 += $tien_danh * ($number - 2);

                            }

                        }
                    }
                    if (!$is_refresh) {

                        if ($hasTwoDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::HAI_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }
                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::BA_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }
                        if ($hasFourDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::BON_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }
                    }
                    $sum_2_total += $sum_2;
                    $sum_3_total += $sum_3;
                    $sum_4_total += $sum_4;

                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;

                    $sum_2_total_percent += $sum_2 * $ration_user->hai_so_percent;
                    $sum_3_total_percent += $sum_3 * $ration_user->ba_so_percent;
                    $sum_4_total_percent += $sum_4 * $ration_user->bon_so_percent;

                    //Tính theo từng đài
                    $hai_so_area = ($sum_2 * $ration_user->hai_so_percent) / $count_area;
                    $ba_so_area = ($sum_3 * $ration_user->ba_so_percent) / $count_area;
                    $bon_so_area = ($sum_4 * $ration_user->bon_so_percent) / $count_area;
                    $bao_lo_tung_dai = $hai_so_area + $ba_so_area + $bon_so_area;
//                    dd($result_tung_dai);
                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $area['so_danh'][$so][] = ['count' => 1, 'danh' => $tien_danh];

                        }
//                        $area['bao_lo'] += $bao_lo_tung_dai;
                        $area['bao_lo_2_so'] += $hai_so_area;
                        $area['bao_lo_3_so'] += $ba_so_area;
                        $area['bao_lo_4_so'] += $bon_so_area;
                        $area['total_danh'] += $bao_lo_tung_dai;
                    }
                    // dd($result_tung_dai);
                    break;
                case TypeFight::XIU_CHU:
                    $sum_xc_3 = 0;
                    $sum_dau_duoi = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2 * 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3 * 2;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4 * 2;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 2 * $count_dai;
                    }
//                    dd($get_dai);
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number = 4;
                            }
                        }
                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(4, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $str_so_err = implode(" ", $so_danh);
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ không nhận 2 số.");

                        } else if (strlen($so_danh_each) == 3) {
                            $sum_xc_3 += $tien_danh * $number;


                        }
                    }
                    if (!$is_refresh) {

                        dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                            FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));
                    }
                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    $sum_3_total_percent += $sum_xc_3 * $ration_user->xiu_chu_percent;

                    $sum_2_total += $sum_dau_duoi;
                    $sum_2_total_percent += $sum_dau_duoi * $ration_user->dau_duoi_percent;
                    $DD += $sum_dau_duoi;
//dd($tien_danh,$number);
                    //Tính theo từng đài
                    $ba_so_area = ($sum_xc_3 * $ration_user->xiu_chu_percent) / $count_area;

                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.d';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }
                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area;
                    }
                    break;
                case TypeFight::XIU_CHU_DAU:
                    $sum_xc_dau_3 = 0;
                    $sum_dau_2 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);

                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 3;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(5, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $str_so_err = implode(" ", $so_danh);
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ không nhận 2 số.");

                        }
                        if (strlen($so_danh_each) == 3) {

                            $sum_xc_dau_3 += $tien_danh * $number;

                        }
                    }
                    if (!$is_refresh) {

                        dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                            FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));
                    }
                    $sum_3_total += $sum_xc_dau_3;
                    $XC += $sum_xc_dau_3;
                    $sum_3_total_percent += $sum_xc_dau_3 * $ration_user->xiu_chu_percent;

                    $sum_2_total += $sum_dau_2;
                    $sum_2_total_percent += $sum_dau_2 * $ration_user->dau_duoi_percent;
                    $DD += $sum_dau_2;
                    //Tính theo từng đài

                    $ba_so_area = ($sum_xc_dau_3 * $ration_user->xiu_chu_percent) / $count_area;

                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.a';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }
                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area;
                    }
                    break;
                case TypeFight::XIU_CHU_DUOI:
                    $sum_xc_duoi = 0;
                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 1;
                            }
                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(6, $type);
                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    $hasThreeDigits = false;

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $so_danh);
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ không nhận 2 số.");
                        } elseif (strlen($so_danh_each) == 3) {
                            $hasThreeDigits = true;
                            $sum_duoi_3 += $tien_danh * $number;
                        } elseif (strlen($so_danh_each) == 4) {

                            $sum_duoi_4 += $tien_danh * $number;
                        }
                    }

                    if (!$is_refresh) {

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                                FiredTypeEnum::BA_CON_XC, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }
                    }

                    $sum_3_total += $sum_duoi_3;
                    $sum_4_total += $sum_duoi_4;

                    $XC += $sum_3_total + $sum_duoi_4;
                    $sum_3_total_percent += $sum_duoi_3 * $ration_user->xiu_chu_percent;

                    $sum_4_total_percent += $sum_duoi_4 * $ration_user->xiu_chu_percent;

                    $sum_2_total += $sum_duoi_2;
                    $sum_2_total_percent += $sum_duoi_2 * $ration_user->dau_duoi_percent;
                    $DD += $sum_duoi_2;
                    //Tính theo từng đài
                    $ba_so_area = ($sum_duoi_3 * $ration_user->xiu_chu_percent) / $count_area;
                    $bon_so_area = ($sum_duoi_4 * $ration_user->bon_so_percent) / $count_area;
                    $xiu_chu_area = $ba_so_area + $bon_so_area;

                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = substr($so, -3) . '.u';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }
                        // $area['bon_so'] += $bon_so_area;
                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $xiu_chu_area;
                        $area['total_danh'] += $xiu_chu_area;
                    }
                    break;
                case TypeFight::DA:

                    if ((array_key_exists("7", $type_detail) && $type_detail[7] == "dv" && count($so_danh) < 3)) {
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá vòng chỉ nhận đánh 3 số trở lên");
                    }

                    if (
                        (array_key_exists("7", $type_detail) && $type_detail[7] == "da" || $type_detail[7] == "dv")
                        && count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {
                            $sum_dx = 0;
                            $number = 0;
                            $count_so_danh = count($so_danh);
                            $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                            foreach ($get_dai as $each) {
                                if ($each == 0 || $each == 2) {
                                    $counter_0 = isset(array_count_values($get_dai)[0]) ? array_count_values($get_dai)[0] : 0;
                                    $counter_2 = isset(array_count_values($get_dai)[2]) ? array_count_values($get_dai)[2] : 0;
                                    if ($counter_0 == 1 || $counter_2 == 1) {
                                        $number = 36;
                                    } elseif ($counter_0 == 2 || $counter_2 == 2) {
                                        $number = 72;
                                    } else if ($counter_0 == 3 || $counter_2 == 3) {
                                        $number = 72 * 3;
                                    } else if ($counter_0 == 4 || $counter_2 == 4) {
                                        $number = 108 * 4;
                                    }
                                } elseif ($each == 3) {
                                    $number = 72;
                                } elseif ($each == 4) {
                                    $number = 72 * 3;
                                } elseif ($each == 5) {
                                    $number = 108 * 4;
                                }

                            }

                            $number = $number * $ct_danh;

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(7, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }
                            foreach ($so_danh as $so_danh_each) {

                                if (strlen($so_danh_each) == 3) {

                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá không nhận đánh 3 số");
                                }
                                if (strlen($so_danh_each) == 4) {

                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá không nhận đánh 4 số");


                                }
                            }
                            if (!$is_refresh) {

                                dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                    $date_check, FiredTypeEnum::DA_XIEN, $so_danh, $tien_danh, $customer_id, $type_detail));
                            }
                            $sum_dx += $number * $tien_danh;
                            $sum_2_total += $number * $tien_danh;


                            $sum_2_total_percent += $sum_dx * $ration_user->da_xien_percent;

                            $DAXIEN += $sum_dx;

                            //Tính theo từng đài
                            if ($count_area == 2) {
                                $ration_get = 1;
                                $ration_back = 1 / 2;
                            } elseif ($count_area == 3) {
                                $ration_get = 2 / 3;
                                $ration_back = 1 / 3;
                            } elseif ($count_area == 4) {
                                $ration_get = 1 / 2;
                                $ration_back = 1 / 4;
                            }
                            $hai_so_area = $number * $tien_danh * $ration_user->da_xien_percent;
                            $so_danh_area = collect($so_danh)->sort()->values()->all();
                            $count_so_danh = count($so_danh_area);
                            $keys = [];
                            for ($i = 0; $i < $count_so_danh; $i++) {
                                for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                    $key = $so_danh_area[$i] . '.' . $so_danh_area[$j];
                                    array_push($keys, $key);
                                }
                            }
                            foreach ($result_tung_dai as $ten_dai => &$area) {
                                $dai1 = $this->convertArea($ten_dai, $dai, $date_check);
                                foreach ($list_area as $area_name) {
                                    $dai2 = $this->convertArea($area_name, $dai, $date_check);
                                    if ($dai1 < $dai2) {
                                        foreach ($keys as $key) {
                                            $new_key = $key . '.' . $dai1 . '.' . $dai2;
                                            $area['so_danh'][$new_key][] = ['count' => 1, 'danh' => $tien_danh];
                                        }
                                    }
                                }
                                $area['tra_lai'] += $hai_so_area * $ration_back;
                                $area['da_xien'] += $hai_so_area * $ration_get;
                                $area['total_danh'] += $hai_so_area * $ration_get;

                            }

                        } else {

                            $str_so_err = implode(" ", $so_danh);
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                        }

                    } else if (count($so_danh) >= 2) {

                        $sum_dt = 0;
                        $number = 0;
                        $count_so_danh = count($so_danh);
                        $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number = 54;

                            } elseif ($each == 0 || $each == 2) {
                                $number += 36;
                            } elseif ($each == 3) {
                                $number = 72;
                            } elseif ($each == 4) {
                                $number = 72 + 36;
                            } elseif ($get_dai[0] == 5) {
                                $number = 72 + 36 + 36;
                            }

                        }

                        $number = $number * $ct_danh;
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(7, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }

                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 3) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {
                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 4 số");
                            }
                        }

                        $sum_dt += $number * $tien_danh;
                        $sum_2_total += $number * $tien_danh;

                        $sum_2_total_percent += $sum_dt * $ration_user->da_thang_percent;

                        $DATHANG += $sum_dt;

                        if (!$is_refresh) {

                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::DA, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }

                        //Tính theo từng đài
                        $hai_so_area = ($number * $tien_danh * $ration_user->da_thang_percent) / $count_area;
                        $so_danh_area = collect($so_danh)->sort()->values()->all();
                        $count_so_danh = count($so_danh_area);
                        $keys = [];
                        for ($i = 0; $i < $count_so_danh; $i++) {
                            for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                $key = $so_danh_area[$i] . '.' . $so_danh_area[$j];
                                array_push($keys, $key);
                            }
                        }


                        foreach ($result_tung_dai as &$area) {
                            foreach ($keys as $key) {
                                $area['so_danh'][$key][] = ['count' => 1, 'danh' => $tien_danh];
                            }
                            // $area['hai_so'] += $hai_so_area;
                            $area['da_thang'] += $hai_so_area;
                            $area['total_danh'] += $hai_so_area;
                        }

                    } else {
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 số trở lên");
                    }
                    break;
                case TypeFight::DA_XIEN:
                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        $sum_dx = 0;
                        $number = 0;
                        $count_so_danh = count($so_danh);
                        $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                        foreach ($get_dai as $each) {

                            if ($each == 0 || $each == 2) {
                                $counter_0 = isset(array_count_values($get_dai)[0]) ? array_count_values($get_dai)[0] : 0;
                                $counter_2 = isset(array_count_values($get_dai)[2]) ? array_count_values($get_dai)[2] : 0;
                                if ($counter_0 == 1 || $counter_2 == 1) {
                                    $number = 36;
                                } elseif ($counter_0 == 2 || $counter_2 == 2) {
                                    $number = 72;
                                } else if ($counter_0 == 3 || $counter_2 == 3) {
                                    $number = 72 * 3;
                                } else if ($counter_0 == 4 || $counter_2 == 4) {
                                    $number = 108 * 4;
                                }

                            } elseif ($each == 3) {
                                $number = 72;
                            } elseif ($each == 4) {
                                $number = 72 * 3;
                            } elseif ($each == 5) {
                                $number = 108 * 4;
                            }

                        }
                        $number = $number * $ct_danh;

                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(8, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }

                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 3) {
                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 4 số");
                            }

                        }

                        $sum_dx += $tien_danh * $number;
                        $sum_2_total += $tien_danh * $number;


                        $sum_2_total_percent += $sum_dx * $ration_user->da_xien_percent;

                        $DAXIEN += $sum_dx;
                        if (!$is_refresh) {

                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::DA_XIEN, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }

                        //Tính theo từng đài
                        $hai_so_area = $number * $tien_danh * $ration_user->da_xien_percent;
                        $so_danh_area = collect($so_danh)->sort()->values()->all();
                        $count_so_danh = count($so_danh_area);
                        $keys = [];

                        if ($count_area == 2) {
                            $ration_get = 1;
                            $ration_back = 1 / 2;
                        } elseif ($count_area == 3) {
                            $ration_get = 2 / 3;
                            $ration_back = 1 / 3;
                        } elseif ($count_area == 4) {
                            $ration_get = 1 / 2;
                            $ration_back = 1 / 4;
                        }
                        for ($i = 0; $i < $count_so_danh; $i++) {
                            for ($j = $i + 1; $j < $count_so_danh; $j++) {
                                $key = $so_danh_area[$i] . '.' . $so_danh_area[$j];
                                array_push($keys, $key);
                            }
                        }
                        foreach ($result_tung_dai as $ten_dai => &$area) {
                            $dai1 = $this->convertArea($ten_dai, $dai, $date_check);
                            foreach ($list_area as $area_name) {
                                $dai2 = $this->convertArea($area_name, $dai, $date_check);
                                if ($dai1 < $dai2) {
                                    foreach ($keys as $key) {
                                        $new_key = $key . '.' . $dai1 . '.' . $dai2;
                                        $area['so_danh'][$new_key][] = ['count' => 1, 'danh' => $tien_danh];
                                    }
                                }
                            }
                            $area['tra_lai'] += $hai_so_area * $ration_back;
                            $area['da_xien'] += $hai_so_area * $ration_get;
                            $area['total_danh'] += $hai_so_area * $ration_get;
                        }

                    } else {
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                    }

                    break;
                case
                TypeFight::DANH_BAY_LO:
                    $sum_bay_lo_2 = 0;
                    $sum_bay_lo_3 = 0;
                    $number = 0;
                    $sum_tam_lo_2 = 0;
                    $sum_tam_lo_3 = 0;
                    foreach ($get_dai as $each) {

                        if ($each == 0 || $each == 2 || $each == 1) {
                            $number += 7;

                        } elseif ($each == 3) {
                            $number = 14;
                        } elseif ($each == 4) {
                            $number = 21;
                        } elseif ($each == 5) {
                            $number = 28;
                        }

                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(9, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    $hasTwoDigits = false;
                    $hasThreeDigits = false;
                    foreach ($so_danh as $so_danh_each) {
                        if ($dai == 2) {
                            if (strlen($so_danh_each) == 2) {
                                $str_so_err = implode(" ", $so_danh);
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Bảy lô chỉ nhận đánh 3 số trở lên");
                            } else if (strlen($so_danh_each) == 3) {
                                $hasThreeDigits = true;
                                $sum_bay_lo_3 += $tien_danh * $number;

                            }
                        } else {
                            if (strlen($so_danh_each) == 2) {
                                $hasTwoDigits = true;
                                $sum_bay_lo_2 += $tien_danh * $number;

                            } else if (strlen($so_danh_each) == 3) {
                                $hasThreeDigits = true;

                                $sum_bay_lo_3 += $tien_danh * $number;

                            }
                        }

                    }
                    if (!$is_refresh) {

                        if ($hasTwoDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::HAI_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                                FiredTypeEnum::BA_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }
                    }
                    $sum_2_total += $sum_bay_lo_2;
                    $sum_3_total += $sum_bay_lo_3;

                    $hai_so += $sum_bay_lo_2;
                    $ba_so += $sum_bay_lo_3;


                    $sum_2_total_percent += $sum_bay_lo_2 * $ration_user->hai_so_percent;
                    $sum_3_total_percent += $sum_bay_lo_3 * $ration_user->ba_so_percent;


                    $sum_2_total += $sum_tam_lo_2;
                    $sum_3_total += $sum_tam_lo_3;

                    $hai_so += $sum_tam_lo_2;
                    $ba_so += $sum_tam_lo_3;

                    $sum_2_total_percent += $sum_tam_lo_2 * $ration_user->hai_so_percent;
                    $sum_3_total_percent += $sum_tam_lo_3 * $ration_user->ba_so_percent;

                    //Tính theo từng đài
                    $hai_so_area = ($sum_bay_lo_2 * $ration_user->hai_so_percent) / $count_area;
                    $ba_so_area = ($sum_bay_lo_3 * $ration_user->ba_so_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($so_danh as $so) {
                            $key_so_danh = $so . '.7l';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        $area['hai_so'] += $hai_so_area;
                        $area['ba_so'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area + $hai_so_area;
                    }

                    break;
                case TypeFight::BAY_LO_DAO:

                    $sum_bay_lo_dao_3 = 0;
                    $number = 0;

                    foreach ($get_dai as $each) {

                        if ($each == 0 || $each == 2 || $each == 1) {
                            $number += 7;

                        } elseif ($each == 3) {
                            $number = 14;
                        } elseif ($each == 4) {
                            $number = 21;
                        } elseif ($each == 5) {
                            $number = 28;
                        }

                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(10, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }


                    foreach ($so_danh as $so_danh_each) {
                        if ($dai == 2) {

                            if (strlen($so_danh_each) == 2) {

                                $str_so_err = implode(" ", $so_danh);
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Bảy lô đảo chỉ nhận đánh 3 số trở lên");

                            } else if (strlen($so_danh_each) == 3) {
                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_bay_lo_dao_3 += $tien_danh * $number;
                                    } else {
                                        $sum_bay_lo_dao_3 += $tien_danh * $number * 3;

                                    }
                                } else {

                                    $sum_bay_lo_dao_3 += $tien_danh * $number * 6;
                                }
                            }
                        } else {
                            if (strlen($so_danh_each) == 2) {

                                $str_so_err = implode(" ", $so_danh);
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Bảy lô đảo chỉ nhận đánh 3 số trở lên");

                            } else if (strlen($so_danh_each) == 3) {


                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_bay_lo_dao_3 += $tien_danh * $number;

                                    } else {
                                        $sum_bay_lo_dao_3 += $tien_danh * $number * 3;

                                    }
                                } else {

                                    $sum_bay_lo_dao_3 += $tien_danh * $number * 6;
                                }
                            }
                        }

                    }
                    if (!$is_refresh) {

                        dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                            FiredTypeEnum::BA_CON_BLD, $so_danh, $tien_danh, $customer_id, $type_detail));
                    }

                    $sum_3_total += $sum_bay_lo_dao_3;

                    $ba_so += $sum_bay_lo_dao_3;

                    $sum_3_total_percent += $sum_bay_lo_dao_3 * $ration_user->ba_so_percent;

                    // Tính theo từng đài
                    $array_so_danh = [];
                    foreach ($so_danh as $so) {
                        $array_so_danh = array_merge($array_so_danh, $this->permute($so));
                    }
                    $ba_so_area = ($sum_bay_lo_dao_3 * $ration_user->ba_so_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($array_so_danh as $so) {
                            $key_so_danh = $so . '.7l';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        $area['ba_so'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area;
                    }
                    break;
                case TypeFight::TAM_LO:
                    if ($dai == 2) {
                        $sum_tam_lo_2 = 0;

                        $number = 0;
                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number += 8;
                            }
                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(11, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {
                            if (strlen($so_danh_each) == 2) {

                                $sum_tam_lo_2 += $tien_danh * $number;

                            } else if (strlen($so_danh_each) == 3) {
                                $str_so_err = implode(" ", $so_danh);
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Tám lô không được đánh 3 số");
                            }
                        }
                        if (!$is_refresh) {

                            dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                                FiredTypeEnum::HAI_CON_BL, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }


                        $sum_2_total += $sum_tam_lo_2;

                        $hai_so += $sum_tam_lo_2;

                        $sum_2_total_percent += $sum_tam_lo_2 * $ration_user->hai_so_percent;

                        // Tính theo từng đài
                        $hai_so_area = ($sum_tam_lo_2 * $ration_user->hai_so_percent) / $count_area;
                        foreach ($result_tung_dai as &$area) {
                            foreach ($so_danh as $so) {
                                $key_so_danh = $so . '.8l';
                                $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                            }

                            $area['hai_so'] += $hai_so_area;
                            $area['total_danh'] += $hai_so_area;
                        }

                    } else {

                        throw new \Exception(" Tám lô chỉ nhận đánh ở đài miền Bắc");
                    }

                    break;
                case TypeFight::TAM_LO_DAO:
                    if ($dai == 2) {
                        $sum_tam_lo_dao_3 = 0;
                        $number = 0;
                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number += 8;
                            }
                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(12, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }


                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 2) {

                                $str_so_err = implode(" ", $so_danh);
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Tám lô đảo chỉ nhận đánh 3 số");
                            } elseif (strlen($so_danh_each) == 3) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_tam_lo_dao_3 += $tien_danh * $number;

                                    } else {
                                        $sum_tam_lo_dao_3 += $tien_danh * $number * 3;

                                    }

                                } else {

                                    $sum_tam_lo_dao_3 += $tien_danh * $number * 6;
                                }
                            }
                        }

                        if (!$is_refresh) {

                            dispatch(new HandleFiredTicketJob($array_dai, $dai,
                                $date_check, FiredTypeEnum::BA_CON_BLD, $so_danh, $tien_danh, $customer_id, $type_detail));
                        }

                        $sum_3_total += $sum_tam_lo_dao_3;

                        $ba_so += $sum_tam_lo_dao_3;

                        $sum_3_total_percent += $sum_tam_lo_dao_3 * $ration_user->hai_so_percent;

                        // Tính theo từng đài
                        $array_so_danh = [];
                        foreach ($so_danh as $so) {
                            $array_so_danh = array_merge($array_so_danh, $this->permute($so_danh_each));
                        }
                        $ba_so_area = ($sum_tam_lo_dao_3 * $ration_user->hai_so_percent) / $count_area;
                        foreach ($result_tung_dai as &$area) {
                            foreach ($array_so_danh as $so) {
                                $key_so_danh = $so . '.8l';
                                $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                            }

                            $area['ba_so'] += $ba_so_area;
                            $area['total_danh'] += $ba_so_area;
                        }

                    } else {

                        throw new \Exception("Tám lô chỉ nhận đánh ở đài miền Bắc");
                    }
                    break;
                case TypeFight::XIU_CHU_DAO_DAU:
                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);

                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 3;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(13, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }


                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));

                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");
                        }
                        if (strlen($so_danh_each) == 3) {


                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number;
                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3;

                                }
                            } else {
                                $sum_xc_3 += $tien_danh * $number * 6;
                            };

                        }
                    }
                    if (!$is_refresh) {

                        dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                            FiredTypeEnum::BA_CON_XCD, $so_danh, $tien_danh, $customer_id, $type_detail));
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    $sum_3_total_percent += $sum_xc_3 * $ration_user->xiu_chu_percent;

                    // Tính theo từng đài
                    $array_so_danh = [];
                    foreach ($so_danh as $so) {
                        $array_so_danh = array_merge($array_so_danh, $this->permute($so_danh_each));
                    }
                    $ba_so_area = ($sum_xc_3 * $ration_user->xiu_chu_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($array_so_danh as $so) {
                            $key_so_danh = $so . '.a';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        // $area['ba_so'] += $ba_so_area;
                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area;
                    }
                    break;
                case TypeFight::XIU_CHU_DAO_DUOI:
                    $sum_xc_3 = 0;
                    $sum_xc_4 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);


                        $number = 1 * $count_dai;
                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(14, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 1;

                            }

                        }
                    }

                    $hasThreeDigits = false;

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");
                        }
                        if (strlen($so_danh_each) == 3) {
                            $hasThreeDigits = true;
//                            $this->checkDuplicationNumber($so_danh_each, implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3)));
                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number;

                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3;

                                }
                            } else {
                                $sum_xc_3 += $tien_danh * $number * 6;
                            };

                        } else if (strlen($so_danh_each) == 4) {
//                            $this->checkDuplicationNumber($so_danh_each, implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4)));
                            $number_dao = $this->countPermutations($so_danh_each);

                            $sum_xc_4 += $tien_danh * $number_dao * $number;

                        }
                    }
                    if (!$is_refresh) {

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                                FiredTypeEnum::BA_CON_XCD, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }
                    }

                    $sum_3_total += $sum_xc_3;
                    $sum_4_total += $sum_xc_4;
                    $XC += $sum_xc_3;
                    $XC += $sum_xc_4;

                    $sum_3_total_percent += $sum_xc_3 * $ration_user->xiu_chu_percent;
                    $sum_4_total_percent += $sum_xc_4 * $ration_user->xiu_chu_percent;

                    // Tính theo từng đài
                    $ba_so_area = ($sum_xc_3 * $ration_user->xiu_chu_percent) / $count_area;
                    $bon_so_area = ($sum_xc_4 * $ration_user->xiu_chu_percent) / $count_area;
                    $xiu_chu_area = $ba_so_area + $bon_so_area;
                    $array_so_danh = [];
                    foreach ($so_danh as $so) {
                        $array_so_danh = array_merge($array_so_danh, $this->permute(substr($so, -3)));
                    }
                    foreach ($result_tung_dai as &$area) {
                        foreach ($array_so_danh as $so) {
                            if (strlen($so) == 3) {
                                $key_so_danh = $so . '.u';
                            } else {
                                $key_so_danh = substr($so, -3) . 'u';
                            }
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        // $area['ba_so'] += $ba_so_area;
                        // $area['bon_so'] += $bon_so_area;
                        $area['xiu_chu'] += $xiu_chu_area;
                        $area['total_danh'] += $xiu_chu_area;
                    }
                    break;
                case TypeFight::XIU_CHU_DAO:

                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 2;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(15, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));

                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");

                        } elseif (strlen($so_danh_each) == 3) {


                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number * 2;

                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3 * 2;

                                }
                            } else {

                                $sum_xc_3 += $tien_danh * $number * 6 * 2;
                            }

                        }
                    }

                    if (!$is_refresh) {

                        dispatch(new HandleFiredTicketJob($array_dai, $dai,
                            $date_check, FiredTypeEnum::BA_CON_XCD, $so_danh, $tien_danh, $customer_id, $type_detail));
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    $sum_3_total_percent += $sum_xc_3 * $ration_user->xiu_chu_percent;

                    // Tính theo từng đài
                    $array_so_danh = [];
                    foreach ($so_danh as $so) {
                        $array_so_danh = array_merge($array_so_danh, $this->permute($so_danh_each));
                    }
                    $ba_so_area = ($sum_xc_3 * $ration_user->xiu_chu_percent) / $count_area;
                    foreach ($result_tung_dai as &$area) {
                        foreach ($array_so_danh as $so) {
                            $key_so_danh = $so . '.d';
                            $area['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $tien_danh];
                        }

                        $area['xiu_chu'] += $ba_so_area;
                        $area['total_danh'] += $ba_so_area;
                    }
                    break;
                case TypeFight::BAO_LO_DAO:
                    $sum_3 = 0;
                    $sum_4 = 0;
                    $sum_2 = 0;
                    $number = 0;

                    foreach ($get_dai as $each) {

                        if ($each == 1) {
                            $number = 27;

                        } elseif ($each == 0 || $each == 2) {
                            $number += 18;

                        } elseif ($each == 3) {
                            $number = 36;
                        } elseif ($each == 4) {
                            $number = 54;
                        } elseif ($each == 5) {
                            $number = 72;
                        }

                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(16, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    $hasThreeDigits = false;
                    $hasFourDigits = false;

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Bao đảo không nhận đánh 2 số");
                        } else if (strlen($so_danh_each) == 3) {
                            $hasThreeDigits = true;

                            if ($dai == 2) {
                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 4);
                                    } else {
                                        $sum_3 += $tien_danh * ($number - 4) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 4) * 6;
                                }

                            } elseif (in_array(3, $get_dai) || $number == 36) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 2);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 2) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 2) * 6;
                                }
                            } elseif (in_array(4, $get_dai) || $number == 54) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 3);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 3) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 3) * 6;
                                }
                            } elseif (in_array(5, $get_dai) || $number == 72) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 4);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 4) * 3;

                                    }

                                } else {
                                    $sum_3 += $tien_danh * ($number - 4) * 6;
                                }
                            } else {
                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 1);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 1) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 1) * 6;

                                }

                            }

                        } else if (strlen($so_danh_each) == 4) {
                            $hasFourDigits = true;


                            $number_dao = $this->countPermutations($so_danh_each);
                            if ($dai == 2) {
                                $sum_4 += $tien_danh * ($number - 7) * $number_dao;
                            } elseif (in_array(3, $get_dai) || sizeof($get_dai) == 2) {
                                $sum_4 += $tien_danh * ($number - 4) * $number_dao;

                            } elseif (in_array(4, $get_dai) || sizeof($get_dai) == 3) {
                                $sum_4 += $tien_danh * ($number - 6) * $number_dao;
                            } elseif (in_array(5, $get_dai) || sizeof($get_dai) == 4) {
                                $sum_4 += $tien_danh * ($number - 8) * $number_dao;
                            } else {
                                $sum_4 += $tien_danh * ($number - 2) * $number_dao;
                            }
                        }
                    }
                    if (!$is_refresh) {

                        if ($hasThreeDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai,
                                $dai, $date_check, FiredTypeEnum::BA_CON_BLD, $so_danh, $tien_danh, $customer_id, $type_detail));

                        }

                        if ($hasFourDigits) {
                            dispatch(new HandleFiredTicketJob($array_dai, $dai, $date_check,
                                FiredTypeEnum::BON_CON_BLD, $so_danh, $tien_danh, $customer_id,
                                $type_detail));
                        }
                    }

                    $sum_2_total += $sum_2;
                    $sum_3_total += $sum_3;
                    $sum_4_total += $sum_4;
                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;

                    $sum_2_total_percent += $sum_2 * $ration_user->hai_so_percent;
                    $sum_3_total_percent += $sum_3 * $ration_user->ba_so_percent;
                    $sum_4_total_percent += $sum_4 * $ration_user->bon_so_percent;

                    //Tính theo từng đài
                    $ba_so_area = ($sum_3 * $ration_user->ba_so_percent) / $count_area;
                    $bon_so_area = ($sum_4 * $ration_user->bon_so_percent) / $count_area;
                    $bao_lo_tung_dai = $ba_so_area + $bon_so_area;
                    $array_so_danh = [];
                    foreach ($so_danh as $so) {
                        $array_so_danh = array_merge($array_so_danh, $this->permute($so_danh_each));
                    }
                    foreach ($result_tung_dai as &$area) {
                        foreach ($array_so_danh as $so) {
                            $area['so_danh'][$so][] = ['count' => 1, 'danh' => $tien_danh];
                        }
//                        $area['bao_lo'] += $bao_lo_tung_dai;
                        $area['bao_lo_3_so'] += $ba_so_area;
                        $area['bao_lo_4_so'] += $bon_so_area;
                        $area['total_danh'] += $bao_lo_tung_dai;
                    }
                    break;
            }
        }
        $array_result['tong_2_so'] = $sum_2_total;

        $array_result['tong_3_so'] = $sum_3_total;
        $array_result['tong_4_so'] = $sum_4_total;
        $array_result['hai_so'] = $hai_so;
        $array_result['ba_so'] = $ba_so;
        $array_result['bon_so'] = $bon_so;
        $array_result['DD'] = $DD;
        $array_result['XC'] = $XC;
        $array_result['DATHANG'] = $DATHANG;
        $array_result['DAXIEN'] = $DAXIEN;
        $array_result['tong_2_so_percent'] = $sum_2_total_percent;
        $array_result['tong_3_so_percent'] = $sum_3_total_percent;
        $array_result['tong_4_so_percent'] = $sum_4_total_percent;
        $array_result['tung_dai'] = $result_tung_dai;
        return array_filter($array_result, function ($value) {
            return $value != 0;
        });
    }

    function swap1(&$a, &$b)
    {
        $temp = $a;
        $a = $b;
        $b = $temp;
    }

    private function getFullnameDai($dai, $region, $date)
    {
        if ($region == 2) {
            return "Hà Nội";
        } elseif ($region == 1) {
            if (in_array($dai, ['tp'])) {
                return "TP. HCM";
            } elseif (in_array($dai, ['vl'])) {
                return "Vĩnh Long";
            } elseif (in_array($dai, ['bd'])) {
                return "Bình Dương";
            } elseif (in_array($dai, ['tv'])) {
                return "Trà Vinh";
            } elseif (in_array($dai, ['tn'])) {
                return "Tây Ninh";
            } elseif (in_array($dai, ['ag'])) {
                return "An Giang";
            } elseif (in_array($dai, ['la'])) {
                return "Long An";
            } elseif (in_array($dai, ['bp'])) {
                return "Bình Phước";
            } elseif (in_array($dai, ['hg'])) {
                return "Hậu Giang";
            } elseif (in_array($dai, ['tg'])) {
                return "Tiền Giang";
            } elseif (in_array($dai, ['kg'])) {
                return "Kiên Giang";
            } elseif (in_array($dai, ['dl'])) {
                return "Đà Lạt";
            } elseif (in_array($dai, ['dn'])) {
                return "Đồng Nai";
            } elseif (in_array($dai, ['ct'])) {
                return "Cần Thơ";
            } elseif (in_array($dai, ['st'])) {
                return "Sóc Trăng";
            } elseif (in_array($dai, ['dt'])) {
                return "Đồng Tháp";
            } elseif (in_array($dai, ['vt'])) {
                return "Vũng Tàu";
            } elseif (in_array($dai, ['bl'])) {
                return "Bạc Liêu";
            } elseif (in_array($dai, ['cm'])) {
                return "Cà Mau";
            } elseif (in_array($dai, ['bt'])) {
                $weekday = date('w', strtotime($date));
                if ($weekday == 2) return "Bến Tre";
                elseif ($weekday == 4) return "Bình Thuận";
            }
        } else {
            if (in_array($dai, ['bd'])) {
                return "Bình Định";
            } elseif (in_array($dai, ['dl'])) {
                return "Đắk Lắk";
            } elseif (in_array($dai, ['dn'])) {
                return "Đà Nẵng";
            } elseif (in_array($dai, ['dk'])) {
                return "Đắk Nông";
            } elseif (in_array($dai, ['tt', 'th'])) {
                return "Huế";
            } elseif (in_array($dai, ['gl'])) {
                return "Gia Lai";
            } elseif (in_array($dai, ['kh'])) {
                return "Khánh Hòa";
            } elseif (in_array($dai, ['kt'])) {
                return "Kon Tum";
            } elseif (in_array($dai, ['nt'])) {
                return "Ninh Thuận";
            } elseif (in_array($dai, ['py'])) {
                return "Phú Yên";
            } elseif (in_array($dai, ['qb'])) {
                return "Quảng Bình";
            } elseif (in_array($dai, ['qt'])) {
                return "Quảng Trị";
            } elseif (in_array($dai, ['qn'])) {
                $weekday = date('w', strtotime($date));
                if ($weekday == 6) return "Quảng Ngãi";
                elseif ($weekday == 2) return "Quảng Nam";
            }
        }
        return null; // Trường hợp không khớp với bất kỳ điều kiện nào
    }

    public function calculatorDa($combinations, $pairDaArr, $type, $so_danh, $so_tien_danh, $array_dai, $dai, $customer_id, $date_check, $type_detail)
    {
        foreach ($so_danh as $so) {
            if (strlen($so) == 1) {
                throw new \Exception("Sai cú pháp đánh");
            }
            if (strlen($so) >= 5) {
                throw new \Exception("Sai cú pháp đánh");
            }
        }
        $ration_user = Ration::query()->where('customer_id', '=', $customer_id)
            ->where('type', '=', TypeUser::User)
            ->where('region_id', $dai)
            ->get()
            ->first();

        $get_dai = $this->getALLSide($array_dai, $dai);

        if ($dai == 3) {
            if (count($get_dai) == 3 || in_array(4, $get_dai)) {
                $this->checkMiddleFightSide($date_check);

            }
        }
        if ($dai == 1) {
            if (count($get_dai) == 4 || in_array(5, $get_dai)) {
                $this->checkSouthFightSide($date_check);

            }
        }

        $array_result = [];
        $sum_2_total = 0;
        $sum_3_total = 0;
        $sum_4_total = 0;
        $hai_so = 0;
        $ba_so = 0;
        $bon_so = 0;
        $DD = 0;
        $XC = 0;
        $DATHANG = 0;
        $DAXIEN = 0;
        $sum_2_total_percent = 0;
        $sum_3_total_percent = 0;
        $sum_4_total_percent = 0;

        $list_area = $this->convertMultipleArea($array_dai, $date_check, $dai);
        $result_tung_dai = array_fill_keys($list_area, [
            'hai_so' => 0,
            'ba_so' => 0,
            'bon_so' => 0,
            'dau_duoi' => 0,
            'bao_lo_2_so' => 0,
            'bao_lo_3_so' => 0,
            'bao_lo_4_so' => 0,
            'xiu_chu' => 0,
            'da_thang' => 0,
            'da_xien' => 0,
            'total_danh' => 0,
            'tra_lai' => 0,
            'so_danh' => [],
        ]);

        foreach ($type as $type_each) {
            $val_sw = $type_each;

            switch ($val_sw) {
                case TypeFight::DA:
                    if ((array_key_exists("7", $type_detail) && $type_detail[7] == "dv" && count($so_danh) < 3)) {
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá vòng chỉ nhận đánh 3 số trở lên");
                    }


                    if (
                        (array_key_exists("7", $type_detail) && $type_detail[7] == "da" || $type_detail[7] == "dv")
                        && count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                            $sum_dx = 0;

                            $number = 72;
                            $ct_danh = 0;
                            $pair_number = $this->getAllPairDa($so_danh);

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(7, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }

                            foreach ($combinations as $combination) {

                                foreach ($pair_number as $each) {

                                    $key1 = $combination[0] . ',' . $combination[1];
                                    $key2 = $each[0] . "," . $each[1];

                                    $key3 = $combination[1] . ',' . $combination[0];
                                    $key4 = $each[1] . "," . $each[0];
                                    $dai0 = $this->convertArea($combination[0], $dai, $date_check);
                                    $dai1 = $this->convertArea($combination[1], $dai, $date_check);
                                    $so0 = $each[0];
                                    $so1 = $each[1];
                                    $money_danh_area = 0;
                                    if (isset($pairDaArr[$key3][$key2])) {
                                        $ct_danh += $pairDaArr[$key3][$key2];
                                        $money_danh_area = $pairDaArr[$key3][$key2];
                                    } else if (isset($pairDaArr[$key1][$key4])) {

                                        $ct_danh += $pairDaArr[$key1][$key4];
                                        $money_danh_area = $pairDaArr[$key1][$key4];
                                    } else if (isset($pairDaArr[$key1][$key2])) {
                                        $ct_danh += $pairDaArr[$key1][$key2];
                                        $money_danh_area = $pairDaArr[$key1][$key2];
                                    } elseif (isset($pairDaArr[$key3][$key4])) {
                                        $ct_danh += $pairDaArr[$key3][$key4];
                                        $money_danh_area = $pairDaArr[$key3][$key4];
                                    } else {
                                        $ct_danh += $tien_danh;
                                        $money_danh_area = $tien_danh;
                                    }
                                    if ($dai0 > $dai1)
                                        $this->swap1($dai0, $dai1);
                                    if ($so0 > $so1)
                                        $this->swap1($so0, $so1);
                                    $key_so_danh = $so0 . '.' . $so1 . '.' . $dai0 . '.' . $dai1;
                                    $result_tung_dai[$combination[0]]['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $money_danh_area];
                                    $result_tung_dai[$combination[0]]['tra_lai'] += $number * $money_danh_area / 2 * $ration_user->da_xien_percent;
                                    $result_tung_dai[$combination[0]]['da_xien'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                    $result_tung_dai[$combination[0]]['total_danh'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                    $result_tung_dai[$combination[1]]['tra_lai'] += $number * $money_danh_area / 2 * $ration_user->da_xien_percent;
                                    $result_tung_dai[$combination[1]]['da_xien'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                    $result_tung_dai[$combination[1]]['total_danh'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                }
                            }
                            $number = $number * $ct_danh;


                            foreach ($so_danh as $so_danh_each) {

                                if (strlen($so_danh_each) == 3) {
                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 3 số");

                                }
                                if (strlen($so_danh_each) == 4) {

                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 4 số");
                                }

                            }

                            $sum_dx += $number;
                            $sum_2_total += $number;


                            $sum_2_total_percent += $sum_dx * $ration_user->da_xien_percent;

                            $DAXIEN += $sum_dx;

                        } else {

                            $str_so_err = implode(" ", $so_danh);
                            throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                        }

                    } else if (count($so_danh) >= 2) {

                        $sum_dt = 0;
                        $number = 0;
                        $ct_danh = 0;
                        $pair_number = $this->getAllPairDa($so_danh);
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(7, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }


                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number = 54;

                            } elseif ($each == 0 || $each == 2) {
                                $number += 36;
                            } elseif ($each == 3) {
                                $number = 72;
                            } elseif ($each == 4) {
                                $number = 72 + 36;
                            } elseif ($get_dai[0] == 5) {
                                $number = 72 + 36 + 36;
                            }

                        }
                        foreach ($pair_number as $each) {
                            $so0 = $each[0];
                            $so1 = $each[1];
                            $money_danh_area = 0;
                            if (isset($pairDaArr[$each[0] . "," . $each[1]])) {
                                $ct_danh += $pairDaArr[$each[0] . "," . $each[1]];
                                $money_danh_area = $pairDaArr[$each[0] . "," . $each[1]];
                            } elseif (isset($pairDaArr[$each[1] . "," . $each[0]])) {
                                $ct_danh += $pairDaArr[$each[1] . "," . $each[0]];
                                $money_danh_area = $pairDaArr[$each[1] . "," . $each[0]];
                            } else {
                                $ct_danh += $tien_danh;
                                $money_danh_area = $tien_danh;
                            }
                            if ($so0 > $so1)
                                $this->swap1($so0, $so1);
                            $key_area = $so0 . '.' . $so1;
                            foreach ($result_tung_dai as &$area) {
                                $area['da_thang'] += $money_danh_area * $ration_user->da_thang_percent;
                                $area['total_danh'] += $money_danh_area * $ration_user->da_thang_percent;
                                $area['so_danh'][$key_area][] = ['count' => 1, 'danh' => $money_danh_area];

                            }

                        }

                        $number = $number * $ct_danh;


                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 3) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {
                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));
                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 4 số");
                            }
                        }

                        $sum_dt += $number;
                        $sum_2_total += $number;

                        $sum_2_total_percent += $sum_dt * $ration_user->da_thang_percent;

                        $DATHANG += $sum_dt;

                    } else {
                        Log::info("", ["so_danh" => $so_danh]);
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 số trở lên");
                    }
                    break;
                case TypeFight::DA_XIEN:
                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        $sum_dx = 0;

                        $number = 72;
                        $ct_danh = 0;
                        $pair_number = $this->getAllPairDa($so_danh);


                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(8, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }

                        foreach ($combinations as $combination) {

                            foreach ($pair_number as $each) {

                                $key1 = $combination[0] . ',' . $combination[1];
                                $key2 = $each[0] . "," . $each[1];

                                $key3 = $combination[1] . ',' . $combination[0];
                                $key4 = $each[1] . "," . $each[0];
                                $dai0 = $this->convertArea($combination[0], $dai, $date_check);
                                $dai1 = $this->convertArea($combination[1], $dai, $date_check);
                                $so0 = $each[0];
                                $so1 = $each[1];
                                $money_danh_area = 0;
                                if (isset($pairDaArr[$key3][$key2])) {
                                    $ct_danh += $pairDaArr[$key3][$key2];
                                    $money_danh_area = $pairDaArr[$key3][$key2];
                                } else if (isset($pairDaArr[$key1][$key4])) {

                                    $ct_danh += $pairDaArr[$key1][$key4];
                                    $money_danh_area = $pairDaArr[$key1][$key4];
                                } else if (isset($pairDaArr[$key1][$key2])) {
                                    $ct_danh += $pairDaArr[$key1][$key2];
                                    $money_danh_area = $pairDaArr[$key1][$key2];
                                } elseif (isset($pairDaArr[$key3][$key4])) {
                                    $ct_danh += $pairDaArr[$key3][$key4];
                                    $money_danh_area = $pairDaArr[$key3][$key4];
                                } else {
                                    $ct_danh += $tien_danh;
                                    $money_danh_area = $tien_danh;
                                }
                                if ($dai0 > $dai1)
                                    $this->swap1($dai0, $dai1);
                                if ($so0 > $so1)
                                    $this->swap1($so0, $so1);
                                $key_so_danh = $so0 . '.' . $so1 . '.' . $dai0 . '.' . $dai1;
                                $result_tung_dai[$combination[0]]['so_danh'][$key_so_danh][] = ['count' => 1, 'danh' => $money_danh_area];
                                $result_tung_dai[$combination[0]]['tra_lai'] += $number * $money_danh_area / 2 * $ration_user->da_xien_percent;
                                $result_tung_dai[$combination[0]]['da_xien'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                $result_tung_dai[$combination[0]]['total_danh'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                $result_tung_dai[$combination[1]]['tra_lai'] += $number * $money_danh_area / 2 * $ration_user->da_xien_percent;
                                $result_tung_dai[$combination[1]]['da_xien'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                                $result_tung_dai[$combination[1]]['total_danh'] += $number * $money_danh_area * $ration_user->da_xien_percent;
                            }
                        }

                        $number = $number * $ct_danh;


                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 3) {
                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 4 số");
                            }

                        }

                        $sum_dx += $number;
                        $sum_2_total += $number;


                        $sum_2_total_percent += $sum_dx * $ration_user->da_xien_percent;

                        $DAXIEN += $sum_dx;

                    } else {
                        $str_so_err = implode(" ", $so_danh);
                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                    }

                    break;

            }
        }
        $array_result['tong_2_so'] = $sum_2_total;

        $array_result['tong_3_so'] = $sum_3_total;
        $array_result['tong_4_so'] = $sum_4_total;
        $array_result['hai_so'] = $hai_so;
        $array_result['ba_so'] = $ba_so;
        $array_result['bon_so'] = $bon_so;
        $array_result['DD'] = $DD;
        $array_result['XC'] = $XC;
        $array_result['DATHANG'] = $DATHANG;
        $array_result['DAXIEN'] = $DAXIEN;
        $array_result['tong_2_so_percent'] = $sum_2_total_percent;
        $array_result['tong_3_so_percent'] = $sum_3_total_percent;
        $array_result['tong_4_so_percent'] = $sum_4_total_percent;
        $array_result['tung_dai'] = $result_tung_dai;
        return array_filter($array_result, function ($value) {
            return $value != 0;
        });
    }

    function hasDuplicates($number)
    {
        $digits = str_split($number);
        return count($digits) !== count(array_unique($digits));
    }

    function countPermutations($number)
    {
        $digits = str_split($number);
        $counts = array_count_values($digits);
        $factorial = 1;
        foreach ($counts as $count) {
            $factorial *= $this->factorial($count);
        }
        $totalPermutations = $this->factorial(count($digits)) / $factorial;
        return $totalPermutations;
    }

    function calculateTotalPairsFromRandomArray($array)
    {
        $totalPairs = 0;
        $arraySize = count($array);

//        for ($i = 0; $i < $arraySize; $i++) {
//
//            for ($j = $i + 1; $j < $arraySize; $j++) {
//                $pair = $array[$i] . $array[$j];
//                $totalPairs++;
//            }
//        }

        return $arraySize - 1;
    }

    function factorial($n)
    {
        if ($n <= 1) {
            return 1;
        }
        return $n * $this->factorial($n - 1);
    }

    public function getMoneyByDigitDaXien($time, $dai, $digit, $ticket_id, $customer_id, $tpsArr, $limits, $typeCheck)
    {
        $tickets = Ticket::query()
            ->where('date_check', $time)
            ->where('id', '!=', $ticket_id)
            ->where('customer_id', $customer_id)
            ->where('region_id', $dai)
            ->get();
        $money = 0;
        $ration_customer = Ration::query()
            ->with('customer')->where('customer_id', '=', $customer_id)
            ->where('type', '=', TypeUser::Customer)
            ->where('region_id', $dai)->get()->first();

        $limitService = new LimitService();
        $stringService = new StringService();

        if ($tickets) {
            foreach ($tickets as $ticket) {

                $message = $this->getCleanMessage($ticket->message);
                $message = $stringService->mergeDaiIntoALine($message, $time, $dai);
                $array_line_not_filter_null = explode("\r\n", $message);
                $array_line = array_filter($array_line_not_filter_null, function ($value) {
                    return !is_null($value) && trim($value) !== '' && stripos($value, "so:")
                        === false && stripos($value, "Số trúng:") === false && stripos($value, "(") === false && stripos($value, ")") === false;

                });
                $pos = array_search("", $array_line);

                if ($pos != null) {
                    $array_line = array_slice($array_line, 0, $pos);
                }

                $temp_dai = '';

                foreach ($array_line as $each) {


                    $each = $this->parseUTF8(strtolower($each));

                    $process = $stringService->processSyntaxInline($each, $time, $dai);
                    $message = str_replace("\n", "\r\n", $process['input']);

                    $arr_line = array_filter(explode("\r\n", $message), function ($value) {
                        return !is_null($value) && trim($value) !== '';
                    });
                    foreach ($arr_line as $string_standard) {
                        $string_standard = strtolower($string_standard);
                        if (preg_match("/(\d+)%/", $string_standard, $matches)) {
                            if ((int)$matches[1] <= 0) {
                                throw new \Exception("Phần trăm không hợp lệ");
                            } else {

                                $string_standard = (preg_replace("/(\d+)%/", "", $string_standard));
                            }

                        }
                        if (empty(trim($string_standard))) {
                            continue;
                        }
                        if ($dai == 2) {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {
                                    continue;
                                }
                            }
                        } else {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {

                                    throw new \Exception("Cú pháp đánh không hợp lệ");
                                }
                            }
                        }
                        $array_cu_phap = null;
                        if ($this->checkIsLimit($ration_customer)) {

                            $array_cu_phap = $limitService->parseMessageLimit(trim($string_standard), $dai, $temp_dai, $time);
                        } else {
                            $array_cu_phap = $this->parseMessage($string_standard, $dai, $temp_dai, $time);
                        }
                        $temp_dai = implode(" ", $array_cu_phap["tp"]);

                        if (!isset($array_cu_phap["result"])) {
                            throw new \Exception("Cú pháp đánh không hợp lệ");
                        }

                        foreach ($array_cu_phap["result"] as $result) {
                            $t = 0;
                            $arrayDai = $this->convertMultipleArea($array_cu_phap["tp"], $time, $dai);
                            $get_dai = $this->getALLSide($arrayDai, $dai);
                            foreach ($this->getCombination($arrayDai) as $tp) {


                                if ($tp[0] == $tpsArr[0] && $tp[1] == $tpsArr[1]) {
                                    $arrayDigitPairDX = false;
                                    $pair_number = $this->getAllPairDa($result["so_danh"]);
                                    $this->addReversedPairs($pair_number);
                                    if ($result["loai_danh"][$t] == TypeFight::DA) {
                                        $arrayDigitPairDX =
                                            (isset($result["loai_danh_detail"][7]) && $result["loai_danh_detail"][7] == "da" || $result["loai_danh_detail"][7] == "dv") &&
                                            $this->isHaveValueInDa($limits
                                                ->where('type', TypeLimitEnum::CHAN_DA_XIEN)
                                                ->pluck('digit')
                                                ->toArray(),
                                                $pair_number)
                                            && count($result["so_danh"]) >= 2 &&
                                            (count($get_dai) >= 2
                                                || in_array(3, $get_dai)
                                                || in_array(4, $get_dai)
                                                || in_array(5, $get_dai));
                                    } elseif ($result["loai_danh"][$t] == TypeFight::DA_XIEN) {
                                        $arrayDigitPairDX = true;
                                    }

                                    if ($arrayDigitPairDX) {
                                        $moneyTemp = 0;

                                        foreach ($pair_number as $pair) {
                                            $limit = $limits->first(function ($value) use ($pair, $dai) {
                                                return $value->digit == $pair[0] . "," . $pair[1]
                                                    && ($value->area_id == AreaEnum::getValueByRegion($dai) || $value->area_id == AreaEnum::ALL)
                                                    && $value->type == 18;
                                            });

                                            if ($limit) {
                                                if ($result["so_tien_danh"][$t] >= $limit->amount_max) {
                                                    $moneyTemp = $limit->amount_max;
                                                } else {
                                                    $moneyTemp += $result["so_tien_danh"][$t];
                                                }

                                            }
                                        }
                                        $money = $money + $moneyTemp;


                                    }
                                }
                            }


                        }

                    }
                }
            }
        }

        return $money;
    }

    /**
     * @throws \Exception
     */

    public function getMoneyByDigitDa($time, $dai, $digit, $ticket_id, $customer_id, $tp_detail, $limits, $typeCheck)
    {
        $tickets = Ticket::query()
            ->where('date_check', $time)
            ->where('id', '!=', $ticket_id)
            ->where('customer_id', $customer_id)
            ->where('region_id', $dai)
            ->get();
        $money = 0;
        $ration_customer = Ration::query()->with('customer')->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::Customer)->where('region_id', $dai)->get()->first();
        $limitService = new LimitService();
        $stringService = new StringService();
        if ($tickets) {
            foreach ($tickets as $ticket) {
                $message = $this->getCleanMessage($ticket->message);
                $message = $stringService->mergeDaiIntoALine($message, $time, $dai);
                $array_line_not_filter_null = explode("\r\n", $message);

                $array_line = array_filter($array_line_not_filter_null, function ($value) {
                    return !is_null($value) && trim($value) !== '' && stripos($value, "so:")
                        === false && stripos($value, "Số trúng:") === false && stripos($value, "(") === false && stripos($value, ")") === false;

                });
                $pos = array_search("", $array_line);

                if ($pos != null) {
                    $array_line = array_slice($array_line, 0, $pos);
                }

                $temp_dai = '';

                foreach ($array_line as $each) {


                    $each = $this->parseUTF8(strtolower($each));

                    $process = $stringService->processSyntaxInline($each, $time, $dai);
                    $message = str_replace("\n", "\r\n", $process['input']);

                    $arr_line = array_filter(explode("\r\n", $message), function ($value) {
                        return !is_null($value) && trim($value) !== '';
                    });
                    foreach ($arr_line as $string_standard) {
                        $string_standard = strtolower($string_standard);
                        if (preg_match("/(\d+)%/", $string_standard, $matches)) {
                            if ((int)$matches[1] <= 0) {
                                throw new \Exception("Phần trăm không hợp lệ");
                            } else {

                                $string_standard = (preg_replace("/(\d+)%/", "", $string_standard));
                            }

                        }
                        if (empty(trim($string_standard))) {
                            continue;
                        }
                        if ($dai == 2) {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {
                                    continue;
                                }
                            }
                        } else {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {

                                    throw new \Exception("Cú pháp đánh không hợp lệ");
                                }
                            }
                        }
                        $array_cu_phap = null;
                        if ($this->checkIsLimit($ration_customer)) {

                            $array_cu_phap = $limitService->parseMessageLimit(trim($string_standard), $dai, $temp_dai, $time);
                        } else {
                            $array_cu_phap = $this->parseMessage($string_standard, $dai, $temp_dai, $time);
                        }
                        $temp_dai = implode(" ", $array_cu_phap["tp"]);

                        if (!isset($array_cu_phap["result"])) {
                            throw new \Exception("Cú pháp đánh không hợp lệ");
                        }

                        foreach ($array_cu_phap["result"] as $result) {
                            $t = 0;
                            for ($m = 0; $m < count($result["loai_danh"]); $m++) {

                                $arrayDai = $this->convertMultipleArea($array_cu_phap["tp"], $time, $dai);
                                $get_dai = $this->getALLSide($arrayDai, $dai);

                                foreach ($arrayDai as $tp) {
                                    if ($tp == $tp_detail) {
                                        $arrayDigitPairDX = count($result["so_danh"]) >= 2 &&
                                            (count($get_dai) >= 2
                                                || in_array(3, $get_dai)
                                                || in_array(4, $get_dai)
                                                || in_array(5, $get_dai));

                                        if ($typeCheck == TypeFight::DA && $result["loai_danh"][$m] == TypeFight::DA) {

                                            $money += $this->retrieveMoneyFromMessageSyntax($result["so_danh"],
                                                $result, $digit, $limits,
                                                $dai,
                                                $this->convertArea($tp, $dai, $time),
                                                TypeFight::DA, $result["so_tien_danh"][$t]);

                                        }

                                    }

                                }
                                $t++;
                            }
                        }

                    }
                }
            }

        }
        return $money;
    }

    public function getMoneyByDigit($time, $dai, $digit, $ticket_id, $customer_id, $tp_detail, $limits, $typeCheck)
    {
        $tickets = Ticket::query()
            ->where('date_check', $time)
            ->where('id', '!=', $ticket_id)
            ->where('customer_id', $customer_id)
            ->where('region_id', $dai)
            ->get();
        $stringService = new StringService();
        $money = 0;
        $ration_customer = Ration::query()->with('customer')->where('customer_id', '=', $customer_id)->where('type', '=', TypeUser::Customer)->where('region_id', $dai)->get()->first();
        $limitService = new LimitService();
        if ($tickets) {
            foreach ($tickets as $ticket) {
                $message = $this->getCleanMessage($ticket->message);
                $message = $stringService->mergeDaiIntoALine($message, $time, $dai);
                $array_line_not_filter_null = explode("\r\n", $message);

                $array_line = array_filter($array_line_not_filter_null, function ($value) {
                    return !is_null($value) && trim($value) !== '' && stripos($value, "so:")
                        === false && stripos($value, "Số trúng:") === false && stripos($value, "(") === false && stripos($value, ")") === false;

                });
                $pos = array_search("", $array_line);

                if ($pos != null) {
                    $array_line = array_slice($array_line, 0, $pos);
                }

                $temp_dai = '';

                foreach ($array_line as $each) {
                    $each = $this->parseUTF8(strtolower($each));
                    $process = $stringService->processSyntaxInline($each, $time, $dai);
                    $message = str_replace("\n", "\r\n", $process['input']);
                    $arr_line = array_filter(explode("\r\n", $message), function ($value) {
                        return !is_null($value) && trim($value) !== '';
                    });
                    foreach ($arr_line as $string_standard) {
                        $string_standard = strtolower($string_standard);

                        if (preg_match("/(\d+)%/", $string_standard, $matches)) {
                            if ((int)$matches[1] <= 0) {
                                throw new \Exception("Phần trăm không hợp lệ");
                            } else {

                                $string_standard = (preg_replace("/(\d+)%/", "", $string_standard));
                            }

                        }
                        if (empty(trim($string_standard))) {
                            continue;
                        }
                        if ($dai == 2) {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {
                                    continue;
                                }
                            }
                        } else {
                            if (preg_match('/^([a-zA-Z]+)/', $string_standard, $matches)) {

                                $firstWord = $matches[1];
                                $space = substr($string_standard, strlen($firstWord));
                                if (trim($space) == "") {

                                    throw new \Exception("Cú pháp đánh không hợp lệ");
                                }
                            }
                        }
                        $array_cu_phap = null;
                        if ($this->checkIsLimit($ration_customer)) {
                            $array_cu_phap = $limitService->parseMessageLimit($string_standard, $dai, $temp_dai, $time);

                        } else {
                            $array_cu_phap = $this->parseMessage($string_standard, $dai, $temp_dai, $time);
                        }

                        $temp_dai = implode(" ", $array_cu_phap["tp"]);

                        if (!isset($array_cu_phap["result"])) {
                            throw new \Exception("Cú pháp đánh không hợp lệ");
                        }
                        foreach ($array_cu_phap["result"] as $result) {
                            $t = 0;
                            for ($m = 0; $m < count($result["loai_danh"]); $m++) {

                                $arrayDai = $this->convertMultipleArea($array_cu_phap["tp"], $time, $dai);
                                foreach ($arrayDai as $tp) {
                                    if ($tp == $tp_detail) {

                                        $get_dai = $this->getALLSide($arrayDai, $dai);

                                        if ($typeCheck == TypeFight::DA && $result["loai_danh"][$m] == TypeFight::DA) {
                                            $isDx = count($result["so_danh"]) >= 2 &&
                                                (count($get_dai) >= 2
                                                    || in_array(3, $get_dai)
                                                    || in_array(4, $get_dai)
                                                    || in_array(5, $get_dai));
                                            if (!$isDx) {
                                                $money += $this->retrieveMoneyFromMessageSyntax($result["so_danh"],
                                                    $result, $digit, $limits,
                                                    $dai,
                                                    $this->convertArea($tp, $dai, $time),
                                                    TypeFight::DA, $result["so_tien_danh"][$t]);
                                            }

                                        } else {
                                            foreach ($result["so_danh"] as $num) {

                                                if ($typeCheck == TypeFight::BAO_LO && ($result["loai_danh"][$m] == TypeFight::BAO_LO_DAO || $result["loai_danh"][$m] == TypeFight::BAY_LO_DAO || $result["loai_danh"][$m] == TypeFight::TAM_LO_DAO)) {
                                                    foreach ($this->permute($digit) as $digitPermute) {
                                                        $money += $this->retrieveMoneyFromMessageSyntax($num,
                                                            $result, $digitPermute, $limits, $dai, $this->convertArea($tp, $dai, $time), TypeFight::BAO_LO,
                                                            $result["so_tien_danh"][$m]);
                                                    }
                                                } else if ($typeCheck == TypeFight::XIU_CHU_DAU && $result["loai_danh"][$m] == TypeFight::XIU_CHU_DAO_DAU) {
                                                    foreach ($this->permute($digit) as $digitPermute) {
                                                        $money += $this->retrieveMoneyFromMessageSyntax($num, $result, $digitPermute, $limits,
                                                            $dai, $this->convertArea($tp, $dai, $time), TypeFight::XIU_CHU_DAU, $result["so_tien_danh"][$m]);
                                                    }
                                                } else if ($typeCheck == TypeFight::XIU_CHU_DUOI && $result["loai_danh"][$m] == TypeFight::XIU_CHU_DAO_DUOI) {
                                                    foreach ($this->permute($digit) as $digitPermute) {

                                                        $money += $this->retrieveMoneyFromMessageSyntax($num,
                                                            $result, $digitPermute, $limits, $dai, $this->convertArea($tp, $dai, $time),
                                                            TypeFight::XIU_CHU_DUOI, $result["so_tien_danh"][$m]);

                                                    }

                                                } else if ($result["loai_danh"][$m] == TypeFight::DAU_DUOI) {
                                                    if ($typeCheck == TypeFight::DAU) {
                                                        $money += $this->retrieveMoneyFromMessageSyntax($num, $result, $digit, $limits,
                                                            $dai, $this->convertArea($tp, $dai, $time), TypeFight::DAU, $result["so_tien_danh"][$m]);

                                                    } else {
                                                        $money += $this->retrieveMoneyFromMessageSyntax($num, $result, $digit, $limits,
                                                            $dai, $this->convertArea($tp, $dai, $time), TypeFight::DUOI, $result["so_tien_danh"][$m]);

                                                    }
                                                } else {
                                                    if ($typeCheck == $result["loai_danh"][$m]) {
                                                        $money += $this->retrieveMoneyFromMessageSyntax($num, $result, $digit, $limits,
                                                            $dai, $this->convertArea($tp, $dai, $time), $result["loai_danh"][$m], $result["so_tien_danh"][$m]);

                                                    }
                                                }
                                            }
                                        }
                                    }

                                }
                                $t++;
                            }
                        }

                    }
                }
            }

        }
        return $money;
    }


    private
    function retrieveMoneyFromMessageSyntax($num, $result, $digit, $limits, $region, $tp, $type, $moneyFight)
    {
        $money = 0;

        if ($type == TypeFight::DA) {
            $pair_number = $this->getAllPairDa($num);
            $this->addReversedPairs($pair_number);
            foreach ($pair_number as $pair) {
                $limit = $limits->first(function ($value) use ($digit, $pair, $region, $type) {
                    return $value->digit == $pair[0] . "," . $pair[1]
                        && ($value->area_id == AreaEnum::getValueByRegion($region) || $value->area_id == AreaEnum::ALL)
                        && $value->type == 17;
                });

                if ($limit) {

                    if ($moneyFight >= $limit->amount_max) {
                        $money = $limit->amount_max;
                    } else {
                        $money += $moneyFight;
                    }

                }
            }

//            $money += $this->calculateTotalPairsFromRandomArray($result["so_danh"]) * $moneyFight;

        } else {

            if ($digit == $num) {
                if ($type == TypeFight::BAO_LO || $type == TypeFight::XIU_CHU) {

                    $limit = $limits->first(function ($value) use ($digit, $tp, $type) {
                        return $value->digit == $digit && $value->area_id == $tp && $value->type == $this->convertTypeFightLimit($type);
                    });
                    if ($limit) {
                        if ($moneyFight >= $limit->amount_max) {
                            $money = $limit->amount_max;
                        } else {
                            $money += $moneyFight;
                        }

                    }
//                    else {
//                        $money = $moneyFight;
//                    }

                } else {
                    $money += $moneyFight;
                }

            }
        }

        return $money;

    }


    public
    function checkMiddleFightSide($date_check)
    {
        $date = new \DateTime($date_check);
        $weekday = $date->format('N');

        //t5 // t7 // cn

        if ($weekday != 4 && $weekday != 6 && $weekday != 7) {
            throw new \Exception("Ngày đánh hiện tại không có 3 đài, xin vui lòng chọn đúng ngày!");


        }

    }

    public
    function checkSouthFightSide($date_check)
    {
        $date = new \DateTime($date_check);
        $weekday = $date->format('N');
        // t7

        if ($weekday != 6) {
            throw new \Exception("Ngày đánh hiện tại không có 4 đài, xin vui lòng chọn đúng ngày!");

        }


    }

    public
    function convertMultipleArea($arrayDai, $date_check, $dai)
    {

        $arrayDc = ['dc', 'daichanh', 'dai chanh'];
        $arrayDp = ['dp', 'daiphu', 'dai phu'];


        $date = new \DateTime($date_check);
        $weekday = $date->format('N'); // 1 (Monday) to 7 (Sunday)
        if ($dai == 1) {
            foreach ($arrayDai as &$value) {
                if (in_array($value, $arrayDc)) {
                    switch ($weekday) {
                        case 6:
                        case 1:
                            $value = 'tp';
                            break;
                        case 2:
                            $value = 'bt';
                            break;
                        case 3:
                            $value = 'dn';
                            break;
                        case 4:
                            $value = 'tn';
                            break;
                        case 5:
                            $value = 'vl';
                            break;
                        case 7:
                            $value = 'tg';
                            break;
                    }
                }
                if (in_array($value, $arrayDp)) {
                    switch ($weekday) {
                        case 1:
                            $value = 'dt';
                            break;
                        case 2:
                            $value = 'vt';
                            break;
                        case 3:
                            $value = 'ct';
                            break;
                        case 4:
                            $value = 'ag';
                            break;
                        case 5:
                            $value = 'bd';
                            break;

                        case 6:
                            $value = 'la';
                            break;
                        case 7:
                            $value = 'kg';
                    }
                }
            }

            if (isset($arrayDai[0]) && $arrayDai[0] == '2d') {
                switch ($weekday) {
                    case 1:
                        return ['tp', 'dt'];
                    case 2:
                        return ['bt', 'vt'];
                    case 3:
                        return ['dn', 'ct'];
                    case 4:
                        return ['tn', 'ag'];

                    case 5:
                        return ['vl', 'bd'];

                    case 6:
                        return ['tp', 'la'];

                    case 7:
                        return ['tg', 'kg'];

                }
            } else
                if (isset($arrayDai[0]) && $arrayDai[0] == '3d') {
                    switch ($weekday) {
                        case 1:
                            return ['tp', 'dt', 'cm'];

                        case 2:
                            return ['bt', 'vt', 'bl'];

                        case 3:
                            return ['dn', 'ct', 'st'];

                        case 4:
                            return ['tn', 'ag', 'bt'];

                        case 5:
                            return ['vl', 'bd', 'tv'];

                        case 6:
                            return ['tp', 'la', 'bp'];

                        case 7:
                            return ['tg', 'kg', 'dl'];

                    }
                } else
                    if (isset($arrayDai[0]) && $arrayDai[0] == '4d') {
                        switch ($weekday) {
                            case 3:
                            case 4:
                            case 2:
                            case 5:
                            case 7:
                            case 1:
                                return [];
                            case 6:
                                return ['tp', 'la', 'bp', 'hg'];
                        }
                    }
        } elseif ($dai == 3) {
            foreach ($arrayDai as &$value) {
                if (in_array($value, $arrayDc)) {
                    switch ($weekday) {
                        case 1:
                            $value = 'py';
                            break;
                        case 2:
                            $value = 'dl';
                            break;
                        case 6:
                        case 3:
                            $value = 'dn';
                            break;
                        case 4:
                            $value = 'bd';
                            break;
                        case 5:
                            $value = 'gl';
                            break;
                        case 7:
                            $value = 'kt';
                            break;
                    }
                }
                if (in_array($value, $arrayDp)) {
                    switch ($weekday) {
                        case 1:
                            $value = 'th';
                            break;
                        case 6:
                        case 2:
                            $value = 'qn';
                            break;
                        case 7:
                        case 3:
                            $value = 'kh';
                            break;
                        case 4:
                            $value = 'qt';
                            break;
                        case 5:
                            $value = 'nt';
                            break;
                    }
                }
            }
            if (isset($arrayDai[0]) && $arrayDai[0] == '2d') {
                switch ($weekday) {
                    case 1:
                        return ['py', 'th'];
                    case 2:
                        return ['dl', 'qn'];
                    case 3:
                        return ['dn', 'kh'];
                    case 4:
                        return ['bd', 'qt'];
                    case 5:
                        return ['gl', 'nt'];

                    case 6:
                        return ['dn', 'qn'];

                    case 7:
                        return ['kt', 'kh'];

                }
            } else
                if (isset($arrayDai[0]) && $arrayDai[0] == '3d') {
                    switch ($weekday) {
                        case 3:
                        case 2:
                        case 5:
                        case 1:
                            return [];

                        case 4:
                            return ['bd', 'qt', 'qb'];

                        case 6:
                            return ['dn', 'qn', 'dk'];


                        case 7:
                            return ['kt', 'kh', 'th'];


                    }
                }
        }
        return $arrayDai;
    }


    public
    function getSideToCheck($value, $dai, $date_check)
    {

        if ($dai == 1) {
            $arrayVl = ['vl', 'vlong', 'vinhlong', 'vinh long',];
            $arrayBd = ['bd', 'bduong', 'binhduong', 'binh duong', 'sb'];
            $arrayTv = ['tv', 'tvinh', 'travinh', 'tra vinh'];
            $arrayTn = ['tn', 'tninh', 'tayninh', 'tay ninh'];
            $arrayAg = ['ag', 'angiang', 'an giang'];
            $arrayBthuan = ['bthuan', 'binhthuan', 'binh thuan'];
            $arrayHcm = ['tp', 'hcm'];
            $arrayLa = ['la', 'lan', 'longan', 'long an'];
            $arrayBp = ['bp', 'bphuoc', 'binhphuoc', 'binh phuoc'];
            $arrayHg = ['hg', 'hgiang', 'haugiang'];
            $arrayTg = ['tg', 'tgiang', 'tien giang', 'tiengiang'];
            $arrayKg = ['kg', 'kgiang', 'kiengiang', 'kien giang'];
            $arrayDl = ['dl', 'dlat', 'dalat', 'da lat'];
            $arrayDn = ['dn', 'dnai', 'dongnai', 'dn', 'dong nai'];
            $arrayCt = ['ct', 'ctho', 'cantho', 'can tho'];
            $arraySt = ['st', 'strang', 'soctrang', 'soc trang'];
            $arrayDt = ['dt', 'dthap', 'dongthap', 'dong thap'];
            $arrayVt = ['vt', 'vtau', 'vungtau', 'vung tau', 'vung tau'];
            $arrayBl = ['bl', 'blieu', 'baclieu', 'bac lieu'];
            $arrayBtre = ['btre', 'bentre', 'ben tre'];
            $arrayCm = ['cm', 'camau', 'ca mau'];
            $date = new \DateTime($date_check);
            $weekday = $date->format('N'); // 1 (Monday) to 7 (Sunday)

            if ($weekday == 4) {
                array_push($arrayBthuan, 'bt');
            } elseif ($weekday == 2) {
                array_push($arrayBtre, 'bt');
            }

            // binh thuan voi ben tre trung nhau
            $str = '';
            if (in_array($value, $arrayVl)) {
                $str = "Vĩnh Long";
            } elseif (in_array($value, $arrayBd)) {
                $str = "Bình Dương";
            } elseif (in_array($value, $arrayTv)) {
                $str = "Trà Vinh";
            } elseif (in_array($value, $arrayTn)) {
                $str = "Tây Ninh";
            } elseif (in_array($value, $arrayAg)) {
                $str = "An Giang";
            } elseif (in_array($value, $arrayBthuan)) {
                $str = "Bình Thuận";
            } elseif (in_array($value, $arrayHcm)) {
                $str = "TP. HCM";
            } elseif (in_array($value, $arrayLa)) {
                $str = "Long An";
            } elseif (in_array($value, $arrayBp)) {
                $str = "Bình Phước";
            } elseif (in_array($value, $arrayHg)) {
                $str = "Hậu Giang";
            } elseif (in_array($value, $arrayTg)) {
                $str = "Tiền Giang";
            } elseif (in_array($value, $arrayKg)) {
                $str = "Kiên Giang";
            } elseif (in_array($value, $arrayDl)) {
                $str = "Đà Lạt";
            } elseif (in_array($value, $arrayDn)) {
                $str = "Đồng Nai";
            } elseif (in_array($value, $arrayCt)) {
                $str = "Cần Thơ";
            } elseif (in_array($value, $arraySt)) {
                $str = "Sóc Trăng";
            } elseif (in_array($value, $arrayDt)) {
                $str = "Đồng Tháp";
            } elseif (in_array($value, $arrayVt)) {
                $str = "Vũng Tàu";
            } elseif (in_array($value, $arrayBl)) {
                $str = "Bạc Liêu";
            } elseif (in_array($value, $arrayBtre)) {
                $str = "Bến Tre";
            } elseif (in_array($value, $arrayCm)) {
                $str = "Cà Mau";
            }
            return $str;
        } else if ($dai == 3) {
            $arrayBd = ['bdinh', 'binhdinh', 'binh dinh', 'bd'];
            $arrayDl = ['dlak', 'daklak', 'dak lak', 'dlac', 'daclac', 'dac lac', 'dl'];
            $arrayDnang = ['dnang', 'danang', 'da nang'];
            $arrayDnong = ['dk', 'dknong', 'daknong', 'dak nong'];
            $arrayGl = ['glai', 'gialai', 'gia lai', 'gl'];
            $arrayKh = ['khoa', 'khanhhoa', 'khanh hoa', 'kh'];
            $arrayKt = ['ktum', 'kontum', 'kon tum', 'kt'];
            $arrayNt = ['nthuan', 'ninhthuan', 'ninh thuan', 'nt'];
            $arrayPy = ['pyen', 'phyen', 'phuyen', 'phu yen', 'py'];
            $arrayQb = ['qbinh', 'quangbinh', 'quang binh', 'qb'];
            $arrayQngai = ['qg', 'qngai', 'quangngai', 'quang ngai'];
            $arrayQnam = ['qnam', 'quangnam', 'quang nam'];
            $arrayQt = ['qtri', 'quangtri', 'quang tri', 'qt'];
            $arrayTth = ['thuathienhue', 'tt', 'thua thien hue', 'th', 'hue'];
            $date = new \DateTime($date_check);
            $weekday = $date->format('N');
            //t5

            if ($weekday == 2) {

                array_push($arrayQnam, 'qn');
                // t7
            } elseif ($weekday == 3) {
                array_push($arrayDnang, 'dn');

            } elseif ($weekday == 6) {
//

                array_push($arrayDnang, 'dn');

//                array_push($arrayDnong, 'dn');
                array_push($arrayQngai, 'qn');
            }
            $str = '';
            if (in_array($value, $arrayBd)) {
                $str = "Bình Định";
            } elseif (in_array($value, $arrayDl)) {
                $str = "Đắk Lắk";
            } elseif (in_array($value, $arrayTth)) {
                $str = "Huế";
            } elseif (in_array($value, $arrayDnang)) {
                $str = "Đà Nẵng";
            } elseif (in_array($value, $arrayDnong)) {
                $str = "Đắk Nông";
            } elseif (in_array($value, $arrayGl)) {
                $str = "Gia Lai";
            } elseif (in_array($value, $arrayKh)) {
                $str = "Khánh Hòa";
            } elseif (in_array($value, $arrayNt)) {
                $str = "Ninh Thuận";
            } elseif (in_array($value, $arrayPy)) {
                $str = "Phú Yên";
            } elseif (in_array($value, $arrayQb)) {
                $str = "Quảng Bình";
            } elseif (in_array($value, $arrayQngai)) {
                $str = "Quảng Ngãi";
            } elseif (in_array($value, $arrayQnam)) {
                $str = "Quảng Nam";
            } elseif (in_array($value, $arrayQt)) {
                $str = "Quảng Trị";
            } elseif (in_array($value, $arrayKt)) {
                $str = "Kon Tum";
            }


            return $str;
        }

    }

    function getCleanMessage($resMess)
    {


        if (strpos($resMess, "2 so") !== false) {
            return substr($resMess, 0, strpos($resMess, "2 so:"));
        } elseif (strpos($resMess, "3 so") !== false) {
            return substr($resMess, 0, strpos($resMess, "3 so:"));
        } else if (strpos($resMess, "4 so") !== false) {
            return substr($resMess, 0, strpos($resMess, "4 so:"));
        }

        return $resMess;
    }


    function getStringOnRowsBySide($str, $region)
    {

//        $array_get_by_side_in_line = [];
//        $array_get_index_side = $this->findPositions($this->parseUTF8(strtolower($str)), $this->allSideLottery($region));
//
//        if (sizeof($array_get_index_side) >= 2) {
//            $array_get_by_side_in_line [] = substr($str, $array_get_index_side[0], $array_get_index_side[1]);
//            for ($i = 2; $i < count($array_get_index_side); $i++) {
//                if ($i > 0) {
//                    $array_get_by_side_in_line [] = substr($str, $array_get_index_side[$i - 1], $array_get_index_side[$i] - $array_get_index_side[$i - 1]);
//
//                }
//            }
//            $array_get_by_side_in_line[] = substr($str, $array_get_index_side[sizeof($array_get_index_side) - 1]);
//
//            return $array_get_by_side_in_line;
//        } else {
//
//            return [$str];
//        }

        return [$str];
    }


    public
    function parseUTF8($str): array|string
    {

        $str = preg_replace("/^\.$/", " ", $str);
        $str = preg_replace("/\s+/", " ", $str);

        $str = str_replace("..", " ", $str);

        $charmap = array(
            "ế" => "e",
            "đ" => "d",
            "j" => "i",
            "J" => "i",
            "₫" => "d",
            "Đ" => "d",
            "à" => "a",
            "á" => "a",
            "ả" => "a",
            "ã" => "a",
            "ạ" => "a",
            "â" => "a",
            "ầ" => "a",
            "ấ" => "a",
            "ẩ" => "a",
            "ả" => "a",
            "ẫ" => "a",
            "ậ" => "a",
            "ă" => "a",
            "ằ" => "a",
            "ắ" => "a",
            "ẳ" => "a",
            "ẵ" => "a",
            "ặ" => "a",
            "ê" => "e",
            "ề" => "e",
            "ế" => "e",
            "ể" => "e",
            "ễ" => "e",
            "ệ" => "e",
            "ô" => "o",
            "ồ" => "o",
            "ố" => "o",
            "ổ" => "o",
            "ỗ" => "o",
            "ộ" => "o",
            "ơ" => "o",
            "ờ" => "o",
            "ớ" => "o",
            "ở" => "o",
            "ỡ" => "o",
            "ợ" => "o",
            "ù" => "u",
            "ú" => "u",
            "ủ" => "u",
            "ũ" => "u",
            "ụ" => "u",
            "ư" => "u",
            "ừ" => "u",
            "ứ" => "u",
            "ử" => "u",
            "ữ" => "u",
            "ự" => "u",
            "ì" => "i",
            "ì" => "i",
            "í" => "i",
            "ỉ" => "i",
            "ĩ" => "i",
            "ị" => "i",
            "ỳ" => "y",
            "ý" => "y",
            "ỷ" => "y",
            "ỹ" => "y",
            "ỵ" => "y",
            "ó" => "o",
            "-" => " ",
            ":" => ".",
            "+" => " ",
            " " => " ",
            "–" => " ",
            "—" => " ",
            ";" => " ",
            "/" => " ",
            "…" => " ",
            "mtr" => "",
            "mn" => "",
            "mt" => "",
            "é" => 'e',
            "À" => 'a',
            "3 dai" => '3d',
            "2 dai" => '2d',
            "4 dai" => '4d',
            "Ð" => "d"
        );
        $str = str_replace(array_keys($charmap), array_values($charmap), $str);

        // 3d65 ki tu
        return preg_replace("/\s*([^a-z0-9%])+\s*([^a-z0-9%])+\s*/", " ", $str);

    }

    public
    function calculator_tracking($type, $so_danh, $so_tien_danh, $array_dai, $dai, $customer_id): array
    {

        $get_dai = $this->getALLSide($array_dai, $dai);

        $array_result = [];
        $sum_2_total = 0;
        $sum_3_total = 0;
        $sum_4_total = 0;
        $hai_so = 0;
        $ba_so = 0;
        $bon_so = 0;
        $DD = 0;
        $XC = 0;
        $DATHANG = 0;
        $DAXIEN = 0;


        $array_tracking_money = array();


        $sum_2_total_percent = 0;
        $sum_3_total_percent = 0;
        $sum_4_total_percent = 0;
        // can xu ly so danh

        foreach ($type as $type_each) {
            $val_sw = $type_each;

            switch ($val_sw) {

                case TypeFight::DAU:
                    $sum_xc_dau_3 = 0;
                    $sum_dau_2 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);


                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        $number = 4;
                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(0, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $sum_dau_2 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 3) {


                            if ($get_dai[0] == 3) {
                                $number = 2;
                            } elseif ($get_dai[0] == 4) {
                                $number = 3;
                            } elseif ($get_dai[0] == 5) {
                                $number = 4;
                            } else {
                                $count_dai = count($get_dai);

                                $number = 1 * $count_dai;
                            }
                            if ($dai == 2) {
                                foreach ($get_dai as $each) {

                                    if ($each == 1) {
                                        $number = 3;

                                    }

                                }
                            }
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(5, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }


                            $sum_xc_dau_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;


                        }

                    }
                    $sum_2_total += $sum_dau_2;

                    $DD += $sum_dau_2;


                    $sum_3_total += $sum_xc_dau_3;
                    $XC = $sum_xc_dau_3;
                    break;
                case TypeFight::DUOI:


                    $sum_duoi_2 = 0;
                    $sum_duoi_3 = 0;
                    $sum_duoi_4 = 0;

                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);


                        $number = 1 * $count_dai;
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(1, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $sum_duoi_2 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 3) {

                            $sum_duoi_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 4) {
                            $sum_duoi_4 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                        }
                    }

                    $sum_2_total += $sum_duoi_2;
                    $sum_3_total += $sum_duoi_3;
                    $sum_4_total += $sum_duoi_4;


                    $DD += $sum_duoi_2 + $sum_duoi_3 + $sum_duoi_4;
                    break;
                case TypeFight::DAU_DUOI:

                    $sum_dau_duoi = 0;
                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2 * 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3 * 2;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4 * 2;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 2 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 5;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(2, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $sum_dau_duoi += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } elseif (strlen($so_danh_each) == 3) {

                            if ($get_dai[0] == 3) {
                                $number = 2 * 2;
                            } elseif ($get_dai[0] == 4) {
                                $number = 3 * 2;
                            } elseif ($get_dai[0] == 5) {
                                $number = 4 * 2;
                            } else {
                                $count_dai = count($get_dai);

                                $number = 2 * $count_dai;
                            }
                            if ($dai == 2) {
                                foreach ($get_dai as $each) {

                                    if ($each == 1) {
                                        $number = 4;

                                    }

                                }
                            }

                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(4, $type);

                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];

                            }


                            $sum_xc_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;


                        }
                    }

                    $sum_2_total += $sum_dau_duoi;

                    $DD += $sum_dau_duoi;

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;
                    break;
                case TypeFight::BAO_LO:


                    $sum_3 = 0;

                    $sum_4 = 0;
                    $sum_2 = 0;
                    $number = 0;
                    foreach ($get_dai as $each) {
                        if ($each == 1) {
                            $number = 27;
                        } elseif ($each == 0 || $each == 2) {
                            $number += 18;
                        } elseif ($each == 3) {
                            $number = 36;
                        } elseif ($each == 4) {
                            $number = 54;
                        } elseif ($each == 5) {
                            $number = 72;
                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(3, $type);
                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            $sum_2 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                        } else if (strlen($so_danh_each) == 3) {
                            if ($dai == 2) {
                                $sum_3 += $tien_danh * ($number - 4);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 4);
                            } elseif (in_array(3, $get_dai)) {
                                $sum_3 += $tien_danh * ($number - 2);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 2);

                            } elseif (in_array(4, $get_dai)) {
                                $sum_3 += $tien_danh * ($number - 3);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 3);

                            } elseif (in_array(5, $get_dai)) {
                                $sum_3 += $tien_danh * ($number - 4);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 4);

                            } else {
                                $sum_3 += $tien_danh * ($number - 1);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 1);

                            }

                        } else if (strlen($so_danh_each) == 4) {

                            if ($dai == 2) {
                                $sum_4 += $tien_danh * ($number - 7);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 7);

                            } elseif (in_array(3, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 4);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 4);

                            } elseif (in_array(4, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 6);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 6);

                            } elseif (in_array(5, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 8);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 8);

                            } else {
                                $sum_4 += $tien_danh * ($number - 2);
                                $array_tracking_money[$so_danh_each] = $tien_danh * ($number - 2);

                            }


                        }
                    }

                    $sum_2_total += $sum_2;
                    $sum_3_total += $sum_3;
                    $sum_4_total += $sum_4;

                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;
                    break;
                case TypeFight::XIU_CHU:
                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2 * 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3 * 2;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4 * 2;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 2 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {
                            if ($each == 1) {
                                $number = 4;
                            }
                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(4, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));
//                            throw new \Exception("Số đánh: "" . $str_so_err . "',Xỉu Chủ không nhận đánh 2 số");
                        }
                        if (strlen($so_danh_each) == 3) {
                            $sum_xc_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                        }
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;


                    break;
                case TypeFight::XIU_CHU_DAU:


                    $sum_xc_dau_3 = 0;

                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);

                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 3;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(5, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {


                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));


//                            throw new \Exception("Số đánh: '" . $str_so_err . "',Xỉu Chủ không nhận đánh 2 số");

                        }
                        if (strlen($so_danh_each) == 3) {

                            $sum_xc_dau_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        }
                    }

                    $sum_3_total += $sum_xc_dau_3;
                    $XC = $sum_xc_dau_3;


                    break;
                case TypeFight::XIU_CHU_DUOI:
                    $sum_xc_duoi_3 = 0;
                    $sum_xc_duoi_4 = 0;

                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);


                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 1;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(6, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {
                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));
//                            throw new \Exception("Số đánh: '" . $str_so_err . "',Xỉu Chủ không nhận đánh 2 số");
                        } elseif (strlen($so_danh_each) == 3) {
                            $sum_xc_duoi_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                        } elseif (strlen($so_danh_each) == 4) {
                            $sum_xc_duoi_4 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                        }
                    }
                    $sum_3_total += $sum_xc_duoi_3;
                    $sum_4_total += $sum_xc_duoi_4;

                    $XC += $sum_xc_duoi_3 + $sum_xc_duoi_4;

                    break;
                case TypeFight::DA:

                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                            $sum_da_xien_2 = 0;

                            $number = 0;
                            $count_so_danh = count($so_danh);
                            $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                            foreach ($get_dai as $each) {

                                if ($each == 0 || $each == 2) {
                                    $number += 36;

                                } elseif ($each == 3) {
                                    $number = 72;
                                } elseif ($each == 4) {
                                    $number = 72 * 3;
                                } elseif ($each == 5) {
                                    $number = 108 * 4;
                                }

                            }
                            $number = $number * $ct_danh;
                            if (count($so_tien_danh) >= 2) {
                                $key = array_search(8, $type);
                                $tien_danh = $so_tien_danh[$key];
                            } else {
                                $tien_danh = $so_tien_danh[0];
                            }
                            foreach ($so_danh as $so_danh_each) {

                                if (strlen($so_danh_each) == 2) {
                                    $sum_da_xien_2 += $tien_danh * $number;
                                    $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                                }
                                if (strlen($so_danh_each) == 3) {

                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

//                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá không nhận đánh 3 số");
                                }
                                if (strlen($so_danh_each) == 4) {

                                    $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));
//                                    throw new \Exception("Số đánh: '" . $str_so_err . "', Đá không nhận đánh 4 số");
                                }
                            }
                            $sum_2_total += $sum_da_xien_2;

                        } else {
                            $str_so_err = implode(" ", $so_danh);
//                            throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                        }
                    } else if (count($so_danh) >= 2) {
                        $sum_da_2 = 0;
                        $number = 0;
                        $count_so_danh = count($so_danh);
                        $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                        foreach ($get_dai as $each) {
                            if ($each == 1) {

                                $number = 54;
                            } elseif ($each == 0 || $each == 2) {
                                $number += 36;

                            } elseif ($each == 3) {
                                $number = 72;
                            } elseif ($each == 4) {
                                $number = 72 * 3;
                            } elseif ($get_dai[0] == 5) {
                                $number = 108 * 4;
                            }

                        }
                        $number = $number * $ct_danh;
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(7, $type);
                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {
                            if (strlen($so_danh_each) == 2) {

                                $sum_da_2 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;
                            }
                            if (strlen($so_danh_each) == 3) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

//                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

//                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá thẳng không nhận đánh 4 số");

                            }
                        }
                        $sum_2_total += $sum_da_2;
                    } else {
                        $str_so_err = implode(" ", $so_danh);
//                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá chỉ nhận đánh 2 số trở lên");
                    }
                    break;
                case TypeFight::DA_XIEN:

                    if (count($so_danh) >= 2 && (count($get_dai) >= 2 || in_array(3, $get_dai) || in_array(4, $get_dai) || in_array(5, $get_dai))) {

                        $sum_da_xien_2 = 0;

                        $number = 0;
                        $count_so_danh = count($so_danh);
                        $ct_danh = ($count_so_danh * ($count_so_danh - 1)) / 2;
                        foreach ($get_dai as $each) {

                            if ($each == 0 || $each == 2) {
                                $number += 36;

                            } elseif ($each == 3) {
                                $number = 72;
                            } elseif ($each == 4) {
                                $number = 72 * 3;
                            } elseif ($each == 5) {
                                $number = 108 * 4;
                            }

                        }
                        $number = $number * $ct_danh;

                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(8, $type);

                            $tien_danh = $so_tien_danh[$key];
                        } else {
                            $tien_danh = $so_tien_danh[0];

                        }

                        foreach ($so_danh as $so_danh_each) {
                            if (strlen($so_danh_each) == 2) {

                                $sum_da_xien_2 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                            }
                            if (strlen($so_danh_each) == 3) {
                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 3));

//                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 3 số");

                            }
                            if (strlen($so_danh_each) == 4) {

                                $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 4));

//                                throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên không nhận đánh 4 số");
                            }

                        }
                        $sum_2_total += $sum_da_xien_2;


                        $DAXIEN = $sum_da_xien_2;
                    } else {


                        $str_so_err = implode(" ", $so_danh);
//                        throw new \Exception("Số đánh: '" . $str_so_err . "', Đá xiên chỉ nhận đánh 2 số hoặc 2 đài trở lên");
                    }
                    break;
                case
                TypeFight::DANH_BAY_LO:
                    $sum_bay_lo_2 = 0;
                    $sum_bay_lo_3 = 0;

                    $number = 0;
                    foreach ($get_dai as $each) {

                        if ($each == 0 || $each == 2) {
                            $number += 7;

                        } elseif ($each == 3) {
                            $number = 14;
                        } elseif ($each == 4) {
                            $number = 21;
                        } elseif ($each == 5) {
                            $number = 28;
                        }

                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(9, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $sum_bay_lo_2 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } else if (strlen($so_danh_each) == 3) {
                            $sum_bay_lo_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        }
                    }
                    $sum_2_total += $sum_bay_lo_2;
                    $sum_3_total += $sum_bay_lo_3;

                    $hai_so += $sum_bay_lo_2;
                    $ba_so += $sum_bay_lo_3;


                    break;
                case TypeFight::BAY_LO_DAO:

                    $sum_bay_lo_dao_2 = 0;
                    $sum_bay_lo_dao_3 = 0;


                    $number = 0;
                    foreach ($get_dai as $each) {

                        if ($each == 0 || $each == 2) {
                            $number += 7;

                        } elseif ($each == 3) {
                            $number = 14;
                        } elseif ($each == 4) {
                            $number = 21;
                        } elseif ($each == 5) {
                            $number = 28;
                        }

                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(10, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $sum_bay_lo_dao_2 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } else if (strlen($so_danh_each) == 3) {
                            $sum_bay_lo_dao_3 += $tien_danh * $number;
                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        } else if (strlen($so_danh_each) == 4) {

                            $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                        }
                    }
                    $sum_2_total += $sum_bay_lo_dao_2;
                    $sum_3_total += $sum_bay_lo_dao_3;

                    $hai_so += $sum_bay_lo_dao_2;
                    $ba_so += $sum_bay_lo_dao_3;


                    break;
                case TypeFight::TAM_LO:
                    if ($dai == 2) {
                        $sum_tam_lo_2 = 0;
                        $sum_tam_lo_3 = 0;
                        $sum_tam_lo_4 = 0;
                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(11, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 2) {

                                $sum_tam_lo_2 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                            } else if (strlen($so_danh_each) == 3) {
                                $sum_tam_lo_3 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                            } else if (strlen($so_danh_each) == 4) {
                                $sum_tam_lo_4 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                            }
                        }
                        $sum_2_total += $sum_tam_lo_2;
                        $sum_3_total += $sum_tam_lo_3;
                        $sum_4_total += $sum_tam_lo_4;
                        $hai_so += $sum_tam_lo_2;
                        $ba_so += $sum_tam_lo_3;
                        $bon_so += $sum_tam_lo_4;


                    } else {

//                        throw new \Exception(" Tám lô chỉ nhận đánh ở đài miền Bắc");
                    }

                    break;
                case TypeFight::TAM_LO_DAO:
                    if ($dai == 2) {
                        $sum_tam_lo_dao_2 = 0;


                        $number = 0;
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number += 7;

                            }

                        }
                        if (count($so_tien_danh) >= 2) {
                            $key = array_search(12, $type);
                            $tien_danh = $so_tien_danh[$key];

                        } else {
                            $tien_danh = $so_tien_danh[0];
                        }
                        foreach ($so_danh as $so_danh_each) {

                            if (strlen($so_danh_each) == 2) {

                                $sum_tam_lo_dao_2 += $tien_danh * $number;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number;

                            }
                        }
                        $sum_2_total += $sum_tam_lo_dao_2;

                        $hai_so += $sum_tam_lo_dao_2;


                    } else {


//                        throw new \Exception("Tám lô chỉ nhận đánh ở đài miền Bắc");

                    }
                    break;
                case TypeFight::XIU_CHU_DAO_DAU:
                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);

                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 3;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(13, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));

//                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");
                        }
                        if (strlen($so_danh_each) == 3) {


                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number;
                                    $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number;
                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3;
                                    $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number * 3;
                                }

                            } else {
                                $sum_xc_3 += $tien_danh * $number * 6;
                                $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number * 6;

                            };

                        }
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    break;
                case TypeFight::XIU_CHU_DAO_DUOI:
                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 1 * $count_dai;
                    }

                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(14, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 1;
                            }
                        }
                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {

                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));

//                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");

                        }
                        if (strlen($so_danh_each) == 3) {


                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number;
                                    $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number;
                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3;
                                    $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number * 3;
                                }


                            } else {
                                $sum_xc_3 += $tien_danh * $number * 6;
                                $array_tracking_money[$so_danh_each] = $sum_xc_3 + $tien_danh * $number * 6;

                            };

                        }
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    break;
                case TypeFight::XIU_CHU_DAO:

                    $sum_xc_3 = 0;
                    if ($get_dai[0] == 3) {
                        $number = 2;
                    } elseif ($get_dai[0] == 4) {
                        $number = 3;
                    } elseif ($get_dai[0] == 5) {
                        $number = 4;
                    } else {
                        $count_dai = count($get_dai);
                        $number = 1 * $count_dai;
                    }
                    if ($dai == 2) {
                        foreach ($get_dai as $each) {

                            if ($each == 1) {
                                $number = 2;

                            }

                        }
                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(15, $type);

                        $tien_danh = $so_tien_danh[$key];
                    } else {
                        $tien_danh = $so_tien_danh[0];

                    }
                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            $str_so_err = implode(" ", $this->getValueWithLength(array_reverse($so_danh), 2));

//                            throw new \Exception("Số đánh: '" . $str_so_err . "', Xỉu chủ đảo đầu không nhận đánh 2 số");

                        } elseif (strlen($so_danh_each) == 3) {


                            if ($this->hasDuplicates($so_danh_each)) {
                                if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                    $sum_xc_3 += $tien_danh * $number * 2;
                                    $array_tracking_money[$so_danh_each] = $tien_danh * $number * 2;
                                } else {
                                    $sum_xc_3 += $tien_danh * $number * 3 * 2;
                                    $array_tracking_money[$so_danh_each] = $tien_danh * $number * 3 * 2;
                                }


                            } else {

                                $sum_xc_3 += $tien_danh * $number * 6 * 2;
                                $array_tracking_money[$so_danh_each] = $tien_danh * $number * 6 * 2;

                            }

                        }
                    }

                    $sum_3_total += $sum_xc_3;
                    $XC += $sum_xc_3;

                    break;
                case TypeFight::BAO_LO_DAO:
                    $sum_3 = 0;
                    $sum_4 = 0;
                    $sum_2 = 0;
                    $number = 0;

                    foreach ($get_dai as $each) {

                        if ($each == 1) {
                            $number = 27;

                        } elseif ($each == 0 || $each == 2) {
                            $number += 18;

                        } elseif ($each == 3) {
                            $number = 36;
                        } elseif ($each == 4) {
                            $number = 54;
                        } elseif ($each == 5) {
                            $number = 72;
                        }

                    }
                    if (count($so_tien_danh) >= 2) {
                        $key = array_search(16, $type);
                        $tien_danh = $so_tien_danh[$key];

                    } else {
                        $tien_danh = $so_tien_danh[0];
                    }

                    foreach ($so_danh as $so_danh_each) {

                        if (strlen($so_danh_each) == 2) {
                            if ($this->hasDuplicates($so_danh_each)) {
                                $sum_2 += $tien_danh * $number;
                            } else {
                                $sum_2 += $tien_danh * $number * 2;
                            }
                        } else if (strlen($so_danh_each) == 3) {

                            if ($dai == 2) {
                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 4);
                                    } else {
                                        $sum_3 += $tien_danh * ($number - 4) * 3;
                                    }

                                } else {
                                    $sum_3 += $tien_danh * ($number - 4) * 6;
                                }

                            } elseif (in_array(3, $get_dai)) {


                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 2);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 2) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 2) * 6;
                                }
                            } elseif (in_array(4, $get_dai)) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 3);
                                    } else {
                                        $sum_3 += $tien_danh * ($number - 3) * 3;
                                    }

                                } else {
                                    $sum_3 += $tien_danh * ($number - 3) * 6;
                                }
                            } elseif (in_array(5, $get_dai)) {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 4);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 4) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 4) * 6;
                                }
                            } else {

                                if ($this->hasDuplicates($so_danh_each)) {
                                    if ($this->kiemTraSoTrungNhau($so_danh_each)) {
                                        $sum_3 += $tien_danh * ($number - 1);

                                    } else {
                                        $sum_3 += $tien_danh * ($number - 1) * 3;

                                    }
                                } else {
                                    $sum_3 += $tien_danh * ($number - 1) * 6;
                                }

                            }

                        } else if (strlen($so_danh_each) == 4) {
                            $number_dao = 1;
                            if (strlen(implode("", array_unique(str_split($so_danh_each)))) == 4) {
                                $number_dao = 24;
                            } elseif (strlen(implode("", array_unique(str_split($so_danh_each)))) == 3) {
                                $number_dao = 12;
                            } elseif (strlen(implode("", array_unique(str_split($so_danh_each)))) == 2) {
                                $number_dao = 4;
                            } elseif (strlen(implode("", array_unique(str_split($so_danh_each)))) == 1) {
                                $number_dao = 1;
                            }

                            if ($dai == 2) {
                                $sum_4 += $tien_danh * ($number - 7) * $number_dao;
                            } elseif (in_array(3, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 4) * $number_dao;
                            } elseif (in_array(4, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 6) * $number_dao;
                            } elseif (in_array(5, $get_dai)) {
                                $sum_4 += $tien_danh * ($number - 8) * $number_dao;
                            } else {
                                $sum_4 += $tien_danh * ($number - 2) * $number_dao;
                            }
                        }
                    }

                    $sum_2_total += $sum_2;
                    $sum_3_total += $sum_3;
                    $sum_4_total += $sum_4;

                    $hai_so += $sum_2;
                    $ba_so += $sum_3;
                    $bon_so += $sum_4;


                    break;
            }
        }


        return array_filter($array_tracking_money, function ($value) {
            return $value != 0;
        });
    }

    function convert_array_to_dd($array)
    {
        $firstDIndex = array_search("d", $array);

        if ($firstDIndex !== false) {
            $array[$firstDIndex] = "dau";
        }


        $secondDIndex = array_search("d", $array, $firstDIndex + 1);

        if ($secondDIndex !== false) {
            $array[$secondDIndex] = "duoi";
        }
        return $array;
    }


    private
    function kiemTraSoTrungNhau($so)
    {
        $chuSo = str_split($so);

        $soLanXuatHien = array_count_values($chuSo);

        if (count($soLanXuatHien) == 1 && current($soLanXuatHien) == count($chuSo)) {
            return true;
        } else {
            return false;
        }
    }


}
