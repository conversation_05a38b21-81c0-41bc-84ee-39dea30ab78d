<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTicketsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->longText('message');
            $table->date('date_check');
            $table->float('tien_xac');
            $table->float('tien_xac_2nd');
            $table->float('tien_trung')->nullable();
            $table->float('tien_trung_lai_ve')->nullable();
            $table->float('tien_trung_2nd')->nullable();
            $table->float('tong_tien')->nullable();
            $table->float('tong_tien_2nd')->nullable();
            $table->foreignId('customer_id')->constrained('customers');
            $table->foreignId('region_id')->constrained('regions');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tickets');
    }
}
