<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddColumnTypeConversationBotsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('conversation_bots', function (Blueprint $table) {
            $table->string('type')->nullable();
            $table->string('provider_message_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('conversation_bots', function (Blueprint $table) {
            if (Schema::hasColumn('conversation_bots', 'type')) {
                $table->dropColumn('type');
            }            if (Schema::hasColumn('conversation_bots', 'provider_message_id')) {
                $table->dropColumn('provider_message_id');
            }
        });
    }
}
