<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('sessions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->unsignedBigInteger('machine_id')->nullable()->index();
            $table->unsignedBigInteger('customer_id')->nullable()->index();
            $table->unsignedBigInteger('shareholder_id')->nullable()->index();
            $table->unsignedBigInteger('accountant_id')->nullable()->index();
            $table->string('session_id')->unique();
            $table->timestamp('last_active')->default(now());
            $table->timestamps();

        });
    }

    public function down()
    {
        Schema::dropIfExists('sessions');
    }
};
