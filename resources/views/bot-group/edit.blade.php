@push('head')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .select2-selection--multiple  {

            overflow-y: auto; !important;
        }
    </style>
@endpush
@extends('layout.master')

@section('content')
    <div class="container mt-5">
        <h1 class="text-center mb-4">Chỉnh sửa Group</h1>

        <form action="{{ route('bot-group.update', $group->id) }}" method="POST" class="card p-4">
            @csrf
            @method('PUT')

            <div class="form-group mb-3">
                <label for="name">Tên <PERSON></label>
                <input type="text" name="name" id="name"  readonly class="form-control" value="{{ old('name', $group->name) }}" required>
                @error('name')
                <div class="text-danger">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="block_type">Chặn loại</label>
                <select style="width:100%; overflow-y: auto" class="form-control"
                        name="block_type[]" id="block_type" multiple >
                    @foreach ($enums as $enum)
                        <option value="{{ $enum->value }}"
                            {{  $group->block_type !=null && in_array($enum->value, old('block_type', $group->block_type)) ? 'selected' : '' }}>
                            {{ \App\Enums\FiredTypeEnum::getLabel($enum->value) }}
                        </option>
                    @endforeach
                </select>

                @error('block_type')
                <div class="text-danger">{{ $message }}</div>
                @enderror
            </div>

            <div class="form-group mb-3">
                <label for="is_block_all">Chặn tất cả</label>
                <select name="is_block_all" id="is_block_all" class="form-control" required>
                    <option value="1" {{ $group->is_block_all ? 'selected' : '' }}>Có</option>
                    <option value="0" {{ !$group->is_block_all ? 'selected' : '' }}>Không</option>
                </select>
                @error('block_all')
                <div class="text-danger">{{ $message }}</div>
                @enderror
            </div>

            <div class="d-flex justify-content-between">
                <button type="submit" class="btn btn-success">Cập nhật</button>
                <a href="{{ route('bot-group.index') }}" class="btn btn-secondary">Hủy</a>
            </div>
        </form>
    </div>
@endsection
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/dist/datepicker.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        $(document).ready(function () {
            $('#block_type').select2();


        });
    </script>
@endpush
