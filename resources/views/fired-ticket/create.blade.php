@push('head')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .select2-selection--multiple  {

            overflow-y: auto; !important;
        }
        .form-floating {
            margin-bottom: 1rem; /* Khoảng cách dưới của các ô input */
        }

        @media (min-width: 260px) {
            .form-floating {
                /* flex: 0 0 calc(50% - 1rem); Hai ô input mỗi hàng trên màn hình >= 576px */
                max-width: calc(50% - 1rem); /* Giới hạn chiều rộng tối đa của ô input */
            }
        }

        @media (min-width: 360px) {
            .form-floating {
                /* flex: 0 0 calc(33.33% - 1rem); <PERSON> ô input mỗi hàng trên màn hình >= 768px */
                max-width: calc(33.33% - 1rem); /* Giới hạn chiều rộng tối đa của ô input */
            }
        }

        @media (min-width: 480px) {
            .form-floating {
                /* flex: 0 0 calc(25% - 1rem); Bốn ô input mỗi hàng trên màn hình >= 992px */
                max-width: calc(25% - 1rem); /* Giới hạn chiều rộng tối đa của ô input */
            }
        }

        .collapse-content {
            margin-top: 5px; /* Khoảng cách giữa nút và nội dung */
            overflow: hidden; /* Đảm bảo nội dung không tràn ra ngoài */
        }
    </style>
@endpush

@extends('layout.master')
@section('content')
    <div class="container-fluid">
        <div class="col-md-12">
            <div class="portlet light portlet-fit portlet-datatable bordered md:mx-4">

                <div class="portlet-body">
                    <div class="tab-content">
                        <div id="menu1" class="tab-pane  active ">
                            <div class="row">
                                <form class="form-group" id="form_transaction" action="{{route('fired-ticket.store')}}"
                                      accept-charset="UTF-8"
                                      method="post"
                                >
                                    <h5 class="font-bold mb-3 font-title uppercase">Lọc số</h5>
                                    @csrf
                                    <div class="row flex-center p-2">
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Đài</label>
                                            <div class="">
                                                <select class="form-control"
                                                        name="area[]"
                                                        multiple="multiple" required
                                                        id="transaction_region">
                                                    <option value="39">Chọn tất cả đài</option>
                                                    <option value="38">Chọn tất cả Nam</option>
                                                    <option value="37">Chọn tất cả Trung</option>
                                                    <option value="36">Miền Bắc</option>
                                                    <optgroup label="Miền Nam">

                                                        @foreach($areas as $area)
                                                            @if($area->id <22)
                                                                <option
                                                                    value="{{$area->id}}">
                                                                    {{$area->name}}
                                                                </option>
                                                            @endif

                                                        @endforeach
                                                    </optgroup>
                                                    <optgroup label="Miền Trung">
                                                        @foreach($areas as $area)
                                                            @if($area->id >=22 && $area->id <36)
                                                                <option
                                                                    value="{{$area->id}}">
                                                                    {{$area->name}}
                                                                </option>
                                                            @endif

                                                        @endforeach
                                                    </optgroup>
                                                </select>
                                                <span class="error-region" style="color: red">
                                                        @error('area')
                                                <span style="color: red">{{ $message }}</span>
                                                @enderror
                                                </span>
                                            </div>
                                        </div>

                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Loại
                                            </label>
                                            <select style="width:100%;" class="form-control"
                                                    name="type[]"
                                                    multiple="multiple" required
                                                    id="type"
                                            >
                                                @foreach ($enums as $enum)
                                                    <option
                                                        value="{{ $enum->value }}">{{ \App\Enums\FiredTypeEnum::getLabel($enum->value) }}</option>
                                                @endforeach

                                            </select>
                                        </div>
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Thời gian gửi (giây)
                                            </label>
                                            <select style="width:100%;" class="form-control"
                                                    name="delay_time" id="delay_time"
                                            >
                                                <option value="">Ngay lập tức</option>
                                                <option value="5">5s</option>
                                                <option value="10">10s</option>
                                                <option value="15">15s</option>
                                                <option value="20">20s</option>
                                                <option value="25">25s</option>
                                                <option value="30">30s</option>
                                                <option value="40">40s</option>
                                                <option value="50">50s</option>
                                                <option value="60">60s</option>

                                            </select>
                                        </div>
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Số tiền
                                            </label>
                                            <div class="">
                                                <input style="width:100%;"
                                                       class="form-control "
                                                       type="number"
                                                       min="0"
                                                       placeholder="Nhập số tiền"
                                                       name="money"
                                                       step="any"
                                                />
                                                <span class="error-customer"
                                                      style="color: red">   @error('money')
                                                <span style="color: red">{{ $message }}</span>
                                                @enderror
                                                </span>
                                            </div>
                                        </div>
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Điều kiện số tin gửi đi khi lọc (min:2)
                                            </label>
                                            <div class="">
                                                <input style="width:100%;"
                                                       class="form-control "
                                                       type="number"
                                                       min="2"
                                                       name="number_fired"
                                                />
                                            </div>
                                        </div>
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Khách hàng
                                            </label>
                                            <div class="">

                                                <select style="width:100%;"
                                                        class="form-control form-khachhang select2_customer"
                                                        name="customer_ids[]"
                                                        multiple="multiple"
                                                        required
                                                        id="customer"
                                                >
                                                    <option value="all">Tất cả</option>
                                                    @foreach($customers as $customer)
                                                        <option
                                                            {{ request()->query('customer_id') == $customer->id ? "selected" : (old('customer') == $customer->id ? "selected" : "") }} value="{{$customer->id}}">{{$customer->username}}</option>

                                                    @endforeach
                                                </select>
                                                <span class="error-region" style="color: red">
                                                        @error('customer_ids')
                                                <span style="color: red">{{ $message }}</span>
                                                @enderror
                                                </span>
                                            </div>
                                        </div>
                                        <div class="form p-2 form-1 col-lg-4 col-md-6 col-sm-12">
                                            <label class="mr-2 text-main">Group nhóm (Tên bot)
                                            </label>
                                            <div class="">

                                                <select style="width:100%;"
                                                        class="form-control form-khachhang select2_customer"
                                                        name="groups[]"
                                                        multiple="multiple"
                                                        required
                                                        id="groups"
                                                >

                                                    @foreach($groups as $group)
                                                        <option value="{{$group->id}}">
                                                            {{$group->name . " (".$group->bot->name.")"}}
                                                        </option>
                                                    @endforeach
                                                </select>

                                            </div>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center mb-4 pb-4"
                                         style="border-bottom: 1px solid #dee2e6">
                                        <div class="  mx-auto pt-3">
                                            <button id="form-transaction-submit-btn"
                                                    class="btn btn-color-main text-white"
                                                    type="submit"
                                            >Tạo mới
                                            </button>
                                        </div>
                                    </div>
                                </form>
                                <div class="col-12 text-nowrap w-full">
                                    <div class="form-group w-full" id="form_transaction">
                                        <div
                                            class="d-flex align-items-center justify-content-between mb-3 flex-wrap w-full gap-2">
                                            <button type="button" class="btn btn-danger" style="margin-left: auto;"
                                                    onclick="deleteSelectedUsers()"> Xóa tất cả đã chọn
                                            </button>
                                        </div>
                                        <div class="form-group table-responsive" style="overflow: auto;">
                                            <table
                                                class="table table-bordered text-nowrap" id="table">
                                                <thead>
                                                <tr>
                                                    <th class="text-center align-middle">#</th>
                                                    <th class="align-middle">Thời gian</th>
                                                    <th class="align-middle">Tiền</th>
                                                    <th class="align-middle">Điều kiện gửi tin</th>
                                                    <th class="align-middle">Group</th>
                                                    <th class="align-middle">Loại</th>
                                                    <th class="align-middle">Miền</th>
                                                    <th class="align-middle">Khách hàng</th>
                                                    <th class="text-center align-middle">Chức năng</th>
                                                    <th class="text-center align-middle">
                                                        <div style="flex-direction: column-reverse;"
                                                             class="d-flex align-items-center gap-1 justify-content-center">
                                                            <input type="checkbox" name="check_all"
                                                                   onclick="toggleSelectAll()">
                                                            <span>Chọn tất cả</span>
                                                        </div>
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($firedTickets as $fired)
                                                    <tr>
                                                        <td class="text-center">#</td>
                                                        <td>{{$fired->send_time == null ? "Ngay lập tức" : $fired->send_time ."s"}}</td>
                                                        <td>{{$fired->money}}</td>
                                                        <td>{{$fired->number_fired}}</td>
                                                        <td>  {!! implode('<br/>',
                                        array_unique($fired->firedTicketDetails()->with('firedTicketGroup')->get()->pluck('firedTicketGroup.name')->toArray()))!!}
                                                        </td>

                                                        <td>
                                                            <button class="collapse-btn" onclick="toggleContent(this)">
                                                                Xem
                                                            </button>
                                                            <div class="collapse-content" style="display: none;">
                                                                {!! implode('<br/>', array_unique(array_map(fn($type) =>
                                                    \App\Enums\FiredTypeEnum::getLabel($type), $fired->firedTicketDetails()
                                                    ->pluck('type')->toArray())))!!}

                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button class="collapse-btn" onclick="toggleContent(this)">
                                                                Xem
                                                            </button>
                                                            <div class="collapse-content" style="display: none;">
                                                                {!! implode('<br/>', array_unique($fired->firedTicketDetails()->with('area')->get()->pluck('area.name')->toArray()))!!}

                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button class="collapse-btn" onclick="toggleContent(this)">
                                                                Xem
                                                            </button>
                                                            <div class="collapse-content" style="display: none;">
                                                                {!! implode('<br/>', array_unique($fired->firedTicketDetails()->with('customer')->get()->pluck('customer.username')->toArray()))!!}

                                                            </div>
                                                        </td>


                                                        <td class="table-td-center text-center">
                                                            <form
                                                                id="deleteForm{{ $fired->id }}"
                                                                action="{{route('fired-ticket.destroy',$fired)}}"
                                                                method="post">
                                                                @method('DELETE')
                                                                @csrf
                                                                <button style="color:white;"
                                                                        id="{{ $fired->id }}"
                                                                        type="submit"
                                                                        class="btn btn-xs btn-danger btn-delete custom-button"
                                                                        title="Xoá"><i
                                                                        class="fa fa-trash-alt"></i>
                                                                </button>
                                                            </form>

                                                        </td>
                                                        <td class="text-center"><input type="checkbox"
                                                                                       name="delete_ids[]"
                                                                                       value="{{$fired->id}}"></td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>        <!-- END FORM-->
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <!-- END DASHBOARD STATS 1-->
    </div>

    <div class="modal fade text-left r-0 l-0" id="ModalDelete" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0" role="document">
            <div class="modal-content mx-3">
                <div class="d-flex items-center justify-between p-3 border-bottom-main">
                    <h5 class="font-title font-bold mb-0">Bảng xác nhận</h5>
                    <button type="button"
                            class="text-gray-400 bg-transparent close-modal hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal"
                    >
                        <i class="fas fa-times" style="font-size: 18px;" aria-hidden="true"></i>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>

                <div class="mt-4 p-3 text-center" style="font-size:15px; color: #111">
                    <i class="fa fa-trash-alt text-danger mb-2" style="font-size: 50px;"></i>
                    <p class="font-semibold mt-2">Bạn có chắc muốn xóa khách hàng này ?</p>
                </div>
                <div class="d-flex justify-center pb-3 mb-1" style="gap:10px;">
                    <button id="delete-submit" class="btn text-white font-bold bg-danger" style="width: 100px;"
                            type="button">Xóa
                    </button>
                    <button id="form-transaction-submit-btn" class="btn btn-dark text-white font-bold"
                            style="width: 100px;" type="button">Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="{{ asset('js/dist/datepicker.js') }}"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function () {
            var formId
            $(".custom-button").click(function (e) {

                e.preventDefault();
                $("#ModalDelete").modal('show');
                formId = $(this).attr('id');
                console.log(formId);
            });
            $("#delete-submit").click(function () {

                $("#deleteForm" + formId).submit();
            });

            $('.close-modal, #form-transaction-submit-btn').on('click', function () {
                $('#ModalDelete').modal('hide');
            });

        });

        function toggleSelectAll() {
            var checkboxes = document.querySelectorAll('input[name="delete_ids[]"]');

            if (document.querySelector('input[name="check_all"]').checked) {
                checkboxes.forEach(function (checkbox) {
                    checkbox.checked = true;
                });
            } else {
                checkboxes.forEach(function (checkbox) {
                    checkbox.checked = false;
                });
            }

        }

        function deleteSelectedUsers() {
            var selectedUserIds = [];
            var checkboxes = document.querySelectorAll('input[name="delete_ids[]"]:checked');
            checkboxes.forEach(function (checkbox) {
                selectedUserIds.push(checkbox.value);
            });
            if (selectedUserIds.length === 0) {
                alert('Hãy chọn dòng để xóa để xóa');
                return;
            }
            $.ajax({
                url: "{{route('fired-ticket.deleteAll')}}",
                type: "POST",
                data: {
                    limit_ids: selectedUserIds,
                    _token: "{{ csrf_token() }}",
                },
                success: function (response) {
                    location.reload();
                }
            });

        }

        function toggleContent(button) {
            const content = button.nextElementSibling;
            if (content.style.display === "none") {
                content.style.display = "block";
                button.textContent = "Ẩn";
            } else {
                content.style.display = "none";
                button.textContent = "Xem";
            }
        }

        function handleRadioChange() {
            var viewAllLimit = document.getElementById('viewAllLimit');
            var viewDetailLimit = document.getElementById('viewDetailLimit');
            var form4 = document.querySelector('.form-4');
            var form5 = document.querySelector('.form-5');

            if (viewAllLimit.checked) {
                form4.style.display = 'block';
                form5.style.display = 'none';
            } else if (viewDetailLimit.checked) {
                form4.style.display = 'none';
                form5.style.display = 'block';
            }
        }

    </script>
    <script>
        $(document).ready(function () {
            $('#transaction_region').select2();
            $('#customer').select2();
            $('#type').select2();
            $('#groups').select2();


        });
    </script>
@endpush
