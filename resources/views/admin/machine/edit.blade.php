@extends('layout.master')
@section('content')
    @push("head")
    @endpush
    <div class="container-fluid">
        <!-- BEGIN PAGE HEADER-->
        <!-- BEGIN PAGE BAR -->

        <!-- <PERSON>ND PAGE BAR -->
        <!-- BEGIN PAGE TITLE-->
        <!-- END PAGE TITLE-->
        <!-- END PAGE HEADER-->
        <!-- BEGIN DASHBOARD STATS 1-->
            <div class="col-lg-12">
                {{ Breadcrumbs::render('shareholder.edit',"Sửa cổ đông") }}
                <div class="portlet light portlet-fit portlet-datatable bordered">
                    <div class="portlet-title text-center">
                        <div class="caption" style="margin-Bottom: 20px;">
                            <span class="font-bold mb-4 font-title uppercase">Sửa thông tin User</span>
                        </div>
                    </div>
                    <div class="portlet-body" >
                        <form id="form_member" action="{{route('admin.machine.update',$machine->id)}}" method="post">
                            @csrf
                            @method('PUT')
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">User name:
                                        </label>
                                        <div class="">
                                            <input class="form-control" placeholder="User Name" type="text"
                                                name="username" id="user_user_name" value="{{$machine->username}}"
                                                readonly>
                                            @error('username')
                                            <span style="color: red">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Email:
                                        </label>
                                        <div class="">
                                            <input class="form-control" placeholder="Email" type="text" name="email"
                                                id="email" value="{{$machine->email}}">

                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Trạng thái:
                                        </label>
                                        <div class="">


                                            <select
                                                class="custom-select"
                                                value="{{old('status')}}">
                                                <option value="0" selected="">Đóng</option>
                                                <option value="1">Mở</option>
                                            </select>
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Mật khẩu:
                                        </label>
                                        <div class="">
                                            <input class="form-control" placeholder="Mật khẩu" type="password"
                                                name="password" id="user_input_password">
                                            @error('password')
                                            <span style="color: red">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Pin:
                                        </label>
                                        <div class="">
                                            <input name="pin" type="password" class="form-control"
                                            >
                                            @error('pin')
                                            <span style="color: red">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Số ngày dùng thử:
                                        </label>
                                        <div class="">
                                            <input name="day_trial" type="number" class="form-control">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="form-group col-12 d-flex items-center p-0 mt-1">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Bot telegram</label>
                                        <div class="col-md-1 custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="turnOffBotSwitch"
                                                name="off_tele" {{ $machine->off_tele ? 'checked' : '' }}>
                                            <label class="custom-control-label text-main" for="turnOffBotSwitch"></label>
                                        </div>
                                    </div>
                                    <div class="form-group col-12 d-flex items-center p-0 mt-1">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main ">Chặn gửi tin nhắn telegram trước giờ xổ</label>
                                        <div class="col-md-1 custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="blockSendTelegramSwitch"


                                                name="is_send" {{ $machine->block_send_telegram ? 'checked' : '' }}>
                                            <label class="custom-control-label text-main" for="blockSendTelegramSwitch"></label>
                                        </div>
                                    </div>

                                    <div class="form-group col-12 d-flex items-center p-0 mt-1">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xóa tin nhắn telegram trước giờ xổ</label>
                                        <div class="col-md-1 custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="deleteTelegramSwitch"

                                                name="is_remove" {{ $machine->is_remove_telegram ? 'checked' : '' }}>
                                            <label class="custom-control-label text-main" for="deleteTelegramSwitch"></label>
                                        </div>
                                    </div>

                                    <div class="form-group col-12 d-flex items-center p-0 mt-1">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xem chi tiết tin nhắn telegram</label>
                                        <div class="col-md-1 custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="viewDetailsSwitch"
                                                name="is_detail" {{ $machine->is_detail_message ? 'checked' : '' }}>
                                            <label class="custom-control-label text-main" for="viewDetailsSwitch"></label>
                                        </div>
                                    </div>

                                    <div class="form-group col-12 d-flex items-center p-0 mt-1">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Lọc hạn mức</label>
                                        <div class="col-md-1 custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="limitSwitch"
                                                name="is_limit" {{ $machine->is_limit ? 'checked' : '' }}>
                                            <label class="custom-control-label text-main" for="limitSwitch"></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Nick name:
                                        </label>
                                        <div class="">
                                            <input name="nick_name" type="text" class="form-control"
                                                value="{{$machine->nick_name}}">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="row flex-dec  ">
                                        <label class="control-label text-main ">Lần hđ gần nhất:
                                        </label>
                                        <div class="">
                                            <input disabled style="cursor: not-allowed;" type="text" class="form-control"
                                                value="">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Thời gian thay đổi:
                                        </label>
                                        <div class="">
                                            <input disabled style="cursor: not-allowed;" type="text" class="form-control"
                                                value="">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Giá thuê:
                                        </label>
                                        <div class="">
                                            <input name="fee" type="number" class="form-control" value="{{$machine->fee}}">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="row flex-dec ">
                                        <label class="control-label text-main ">Người giới thiệu:
                                        </label>
                                        <div class="">

                                            <select name="sale" id="selecte_sales"
                                                    class="custom-select"
                                                    tabindex="-98">
                                                <option value="">Không</option>

                                            </select></div>
                                        <span class="help-block help-block-error"></span>
                                    </div>
                                    <div class="row flex-dec  ">
                                        <label class="control-label text-main ">Hoa hồng:
                                        </label>
                                        <div class="">
                                            <input name="hoa_hong" type="number" class="form-control" value="">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                    </div>
                                    <div class="d-flex justify-content-center w-full my-3" >
                                        <button id="form-member-submit-btn"
                                                class="btn btn-color-main text-white text-center mobile-w-full"
                                                type="submit"
                                        >Cập nhật
                                        </button>
                                    </div>

                                </div>

                            </div>


                        </form>        <!-- END FORM-->
                    </div>
                </div>
            </div>

        <div class="clearfix"></div>
        <!-- END DASHBOARD STATS 1-->
    </div>

@endsection

@push('js')
@endpush
