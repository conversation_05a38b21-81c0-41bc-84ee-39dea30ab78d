@extends('layout.master')

@section('content')
    @push("head")
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>

        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    @endpush

    <div class="container mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-primary">Kế toán phụ</h2>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#createAccountantModal">
                <i class="fas fa-plus"></i> Tạo kế toán phụ
            </button>
        </div>

        <table class="table table-bordered table-hover text-center">
            <thead class="table-dark">
            <tr>
                <th>username</th>
                <th>tên</th>
                <th>Hành động</th>
            </tr>
            </thead>
            <tbody>
            @foreach($accountants as $accountant)
                <tr>
                    <td>{{ $accountant->username }}</td>
                    <td>{{ $accountant->name }}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" data-bs-toggle="modal" data-bs-target="#editAccountantModal{{ $accountant->id }}">
                            <i class="fas fa-edit"></i> Sửa
                        </button>
                        <form action="{{ route('sub-accountants.destroy', $accountant) }}" method="POST" class="d-inline">
                            @csrf
                            @method('DELETE')
                            <button class="btn btn-danger btn-sm" onclick="return confirm('Bạn có chắc muốn xoá')">
                                <i class="fas fa-trash"></i> Xoá
                            </button>
                        </form>
                    </td>
                </tr>

                <!-- Edit Modal -->
                <div class="modal fade" id="editAccountantModal{{ $accountant->id }}" tabindex="-1" aria-labelledby="editAccountantModalLabel{{ $accountant->id }}" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-warning">
                                <h5 class="modal-title" id="editAccountantModalLabel{{ $accountant->id }}">Sửa kế toán viên phụ</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form action="{{ route('sub-accountants.update', $accountant->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Username</label>
                                        <input type="text" class="form-control-plaintext" name="username" value="{{ $accountant->username }}" readonly >
                                    </div>
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Tên</label>
                                        <input type="text" class="form-control" name="name" value="{{ $accountant->name }}" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Mật khẩu </label>
                                        <input type="password" class="form-control" name="password">
                                    </div>
                                    <button type="submit" class="btn btn-primary w-100">Lưu</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
            </tbody>
        </table>

        <!-- Create Modal -->
        <div class="modal fade" id="createAccountantModal" tabindex="-1" aria-labelledby="createAccountantModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title" id="createAccountantModalLabel">Tạo kế toán viên phụ</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="{{ route('sub-accountants.store') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="username" class="form-label">Username</label>
                                <input type="text" class="form-control-plaintext" name="username" value="{{$username}}" readonly >
                            </div>
                            <div class="mb-3">
                                <label for="name" class="form-label">Tên</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Mật khẩu</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Tạo kế toán viên phụ</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
