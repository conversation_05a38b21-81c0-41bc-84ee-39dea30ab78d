@extends('layout.master')
@section('content')
    @push("head")
        <link href="{{asset('js/dist/datepicker.css')}}" rel="stylesheet">

    @endpush


    <div class="container-fluid">
        <div class="col-md-12">

            <div class="portlet light portlet-fit portlet-datatable bordered md:mx-4">
                <div class="portlet-title">
                    <ul class="nav nav-tabs nav-justified tab-limit">

                        <li class="active"><a class="tab-home active" href="#menu2" data-toggle="tab" >Báo
                                cáo</a></li>


                    </ul>
                </div>
                <div class="portlet-body">
                    <div class="tab-content">
                        <div id="menu2" class="tab-pane  active">
                            <h3 class="font-bold mb-4 font-title uppercase">Báo cáo</h3>
                            <div class="table-responsive" style="overflow: auto">
                                <table class="table table-bordered text-nowrap">
                                    <tbody>
                                    <tr>
                                        <th>Tên</th>
                                        <th><PERSON><PERSON><PERSON> lập</th>
                                        <th colspan="2"></th>
                                        <th colspan="2" class="text-center">Chức năng</th>

                                    </tr>
                                    </tbody>
                                    <tbody>
                                    @foreach($statistics as $statistic)

                                        <tr style="cursor: pointer;"
                                            onclick="redirect('{{route('statistic.show',$statistic->id)}}')">

                                            <td>{{$statistic->name}}</td>
                                            <td>{{ $statistic->created_at }}</td>
                                            <td colspan="2"></td>
                                            <td class="table-td-center text-center">
{{--                                                <form action="{{route('reports.deleteStatistic',$statistic)}}"--}}
{{--                                                      method="post">--}}
{{--                                                    @method('DELETE')--}}
{{--                                                    @csrf--}}
{{--                                                    <button style="color:white;"--}}
{{--                                                            type="submit"--}}
{{--                                                            class="btn btn-danger btn-icon-only  custom-button"--}}
{{--                                                            title="Xoá"><i--}}
{{--                                                            class="fas fa-trash-alt"></i>--}}
{{--                                                    </button>--}}
{{--                                                </form>--}}
                                                <br>

                                            </td>
                                        </tr>

                                    @endforeach
                                    </tbody>

                                </table>
                            </div>
                        </div>


                    </div>
                </div>

            </div>
        </div>

        <div class="clearfix"></div>
    </div>
    <script>
        function redirect(url) {
            window.location.href = url; // Thay đổi URL hiện tại
        }
    </script>
@endsection

