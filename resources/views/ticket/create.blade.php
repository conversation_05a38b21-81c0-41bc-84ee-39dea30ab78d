@push('head')
    <link href="{{asset('js/dist/datepicker.css')}}" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="{{asset('css/highlight-within-textarea.min.css')}}" rel="stylesheet">
    <style>
        .draw-color {
            background-color: #ffec99;
        }
    </style>
@endpush

@extends('layout.master')
@section('content')

    <div class="container-fluid">
        <div class="col-lg-12">
            {{ Breadcrumbs::render('report.index', auth()->guard('machine')->user() ?? auth()->user() ) }}
            <div class="portlet light portlet-fit portlet-datatable bordered">
                <div class="portlet-title">
                    <div class="caption" style="width:200px">
                        <i class="icon-settings font-dark"></i>
                        <h3 class="font-bold mb-4 font-title uppercase">Thêm tin nhắn</h3>
                    </div>
                </div>
                <div class="portlet-body">
                    <form class="form-horizontal form-taomoi" id="form_transaction" action="{{route('ticket.store')}}"
                          accept-charset="UTF-8"
                          onsubmit="return validateForm()"
                          method="post"
                    >
                        @csrf
                        <div class="form-body">
                            <div class="">
                                <div class="">
                                    <div class="">
                                        <div class="d-flex justify-between form-wrap" style="gap:10px;">
                                            <div style="flex:1;" class=" ">
                                                <label class="control-label lb-region text-main">Vùng:
                                                </label>
                                                <div class="text-main">
                                                    <select style="width:100%;"
                                                            class="form-control form-khachhang p-0 text-main"
                                                            name="region"
                                                            id="transaction_region">
                                                        <option value="">Lựa chọn vùng</option>
                                                        <option

                                                            {{   request()->query('region_id') == 1 ? "selected" : (old('region') == 1? "selected" : "")  }} value="1">
                                                            Miền
                                                            Nam
                                                        </option>
                                                        <option
                                                            {{ request()->query('region_id') == 2 ? "selected" : (old('region') == 2? "selected" : "") }} value="2">
                                                            Miền
                                                            Bắc
                                                        </option>
                                                        <option
                                                            {{ request()->query('region_id') == 3 ? "selected": (old('region') == 3? "selected" : "") }} value="3">
                                                            Miền
                                                            Trung
                                                        </option>
                                                    </select>
                                                    <span class="error-region" style="color: red"></span>
                                                </div>
                                            </div>
                                            <div style="flex:1;" class=" ">
                                                <div class="form-group  ">
                                                    <label class="control-label text-main">Ngày Dò:
                                                    </label>
                                                    <div class=" input-group date date-picker margin-bottom-5"
                                                         data-date-format="dd/mm/yyyy">
                                                        <input class="form-control form-khachhang"
                                                               value=" {{ request()->query('date_check')  ? request()->query('date_check') : (old('date_check') ? old('date_check') : date("d/m/Y"))}}"
                                                               type="text"
                                                               name="date_check"
                                                               data-toggle="datepicker"
                                                               id="date_check"
                                                        >
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="">
                                        <div class="  ">
                                            <div class="form-group">


                                                <label class="control-label text-main lb-customer">Khách hàng:
                                                </label>
                                                <div class="">
                                                    <select style="width:100%;"
                                                            class="form-control form-khachhang select2_customer"
                                                            name="customer"
                                                            id="customer"
                                                    >
                                                        <option value=""></option>

                                                        @foreach($customers as $customer)
                                                            <option
                                                                {{ request()->query('customer_id') == $customer->id ? "selected" : (old('customer') == $customer->id ? "selected" : "") }} value="{{$customer->id}}">{{$customer->username}}</option>

                                                        @endforeach
                                                    </select>

                                                    <span class="error-customer"
                                                          style="color: red"> {{$error ?? ''}} </span>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class=" form-group ">
                                    <label class="control-label text-main lb-message"
                                           style="color: {{ session()->has('error') ? 'red':'' }}">Tin nhắn:
                                    </label>
                                    <div class=" mx-auto ">
                                        <div class="hwt-container">
                                            <div class="hwt-backdrop">
                                                <div class="hwt-highlights hwt-content"></div>
                                            </div>
                                            <textarea
                                                class="form-control hwt-input hwt-content w-100 text-area p-2 text-main"
                                                placeholder="Tin nhắn"
                                                name="message" id="transaction_message"
                                                contenteditable="true"
                                                cols="90" rows="6"
                                                style="overflow-wrap: break-word;  overflow-y: scroll;
                                                          resize: horizontal;border-color:  {{ session()->has('error') ? '#e73d4a':'' }}"
                                            >{{ session()->has('error') ?  old('message') : ''}}</textarea>
                                        </div>
                                        <div class="info-message-create"
                                             data-message="{{ session()->get('message_err') }}"></div>
                                        <div class="error-message" style="color: red">
                                                <span class="error-customer"
                                                      style="color: red"> @if(session()->has('error'))
                                                        {{ session()->get('error') }}
                                                    @endif </span>
                                            <span class="error-customer"
                                                  style="color: #2e59d9"> @if(session()->has('limit_messages'))
                                                    {!! session()->get('limit_messages') !!}
                                                @endif </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="  mx-auto pt-3">
                                <button id="form-transaction-submit-btn" class=" btn btn-color-main text-white "
                                        type="submit"
                                >Tạo mới
                                </button>
                            </div>
                        </div>
                    </form>        <!-- END FORM-->
                    <div id="transaction-messages-view">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade text-left r-0" id="ModalDetail" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-xl my-0" role="document">
            <div class="modal-content">
                <div class="d-flex items-center justify-between p-3 border-bottom-main">
                    <h5 class="font-title font-bold">Chỉnh sửa tin nhắn</h5>
                    <button data-toggle="modal" data-target="#ModalDetail" type="button"
                            class="bg-transparent rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center close-modal"
                            data-modal-hide="ModalDetail">
                        <i class="fas fa-times" aria-hidden="true"></i>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <div class="p-3">
                    <div class="d-flex" style="flex-direction: column">
                        <label class="label-message text-main">Nội dung</label>
                        <textarea class="form-control hwt-input hwt-content text-area px-2 py-1"
                                  contenteditable="true"
                                  cols="90" rows="6" id="message" placeholder="Tin nhắn" name="message"></textarea>
                        <div class="info-message"
                             data-message=""
                        ></div>
                        <div class="error-message" style="color: red">
                                                    <span class="error-customer"
                                                          style="color: red"
                                                    >
                                                    </span>
                        </div>
                    </div>
                    <div class="d-flex justify-center" style="gap:10px;">
                        <div data-toggle="modal"
                             {{--                             data-target="#ModalDetail" --}}
                             class="  pt-3">
                            <button style="padding: 4px 15px; width: 71px;" id="update-submit"
                                    class="btn btn-color-main text-white font-bold"
                                    type="submit"

                            >Sửa
                            </button>
                        </div>
                        <div data-toggle="modal" data-target="#ModalDetail" class="  pt-3">
                            <button style="padding: 4px 15px;" id="form-transaction-submit-btn"
                                    class="btn btn-dark text-white font-bold "
                                    type="submit"
                            >Đóng
                            </button>
                        </div>
                    </div>
                    <div class="pt-3">
                        <h6 class="font-title font-bold">Lịch sử chỉnh sửa</h6>
                        <div class="w-100" style="overflow-x: auto" id="table-content">
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade text-left r-0 l-0" id="ModalDelete" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0" role="document">
            <div class="modal-content mx-3">
                <div class="d-flex items-center justify-between p-3 border-bottom-main">
                    <h5 class="font-title font-bold mb-0">Bảng xác nhận</h5>
                    <button data-toggle="modal" data-target="#ModalDelete" type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal"
                            data-modal-hide="ModalDetail">
                        <i class="fas fa-times" aria-hidden="true"></i>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>
                <div class="mt-4 p-3 text-center" style="font-size:15px; color: #111">
                    <i class="fa fa-trash text-danger mb-2" style="font-size: 50px;"></i>
                    <p class="font-semibold mt-2">Bạn có chắc muốn xóa tin nhắn này không ?</p>
                </div>
                <div class="d-flex justify-center pb-3 mb-1" style="gap:10px;">
                    <div class="">
                        <button style="width: 100px;" id="delete-sumbit"
                                class="btn text-white font-bold bg-danger"
                                type="button"
                        >Xóa
                        </button>
                    </div>
                    <div data-toggle="modal" data-target="#ModalDelete" class="">
                        <button style="width: 100px;" id="form-transaction-submit-btn"
                                class="btn btn-dark text-white font-bold"
                                type="submit"
                        >Hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{asset('js/select2.min.js')}}"></script>
    <script src="{{asset('js/dist/datepicker.js')}}"></script>
    <script src="{{asset('js/highlight-within-textarea.min.js')}}"></script>
    <script src="{{asset('js/autosize.min.js')}}"></script>
    <script>
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        $('#ModalDetail').on('shown.bs.modal', function () {
            document.querySelectorAll('.auto-resize').forEach(textarea => {
                autoResize(textarea);
            });
        });

        document.querySelectorAll('.auto-resize').forEach(textarea => autoResize(textarea));
        $("#message").highlightWithinTextarea({
            highlight: [
                {
                    highlight: $(".info-message").attr('data-message'),
                    className: 'draw-color'
                }
            ]
        });

        $('#transaction_message').highlightWithinTextarea({
            highlight: [
                {
                    highlight: $(".info-message-create").data('message'),
                    className: 'draw-color'
                }
            ]
        });
        autosize($('textarea'));


        $(document).ready(function () {
            $('[data-toggle="datepicker"]').datepicker({
                language: 'vi-VN',
                format: 'dd/mm/yyyy'
            });

        });
        $(".select2_customer").select2({
            placeholder: "Lựa khách hàng",
            sorter: function (data) {
                return data.sort(function (a, b) {
                    return a.text.localeCompare(b.text);
                });
            }
        });
        let customer_id = $("#customer").val()
        let region_id = $("#transaction_region").val()
        let date_check = $("#date_check").val()


        getTicketsToday(customer_id, region_id, date_check)
        $("#transaction_region").change(function () {
            region_id = this.value
            $(".error-region").text("")
            $(".lb-region").css("color", "black")
            $("#transaction_region").css("border-color", "")
            getTicketsToday(customer_id, region_id, date_check)

        })
        $("#customer").change(function () {

            customer_id = this.value
            $(".error-customer").empty()
            $(".lb-customer").css("color", "black")

            $(".select2-selection").css("border-color", "")
            getTicketsToday(customer_id, region_id, date_check)
        })
        $("#transaction_message").bind('input propertychange', function () {
            if (this.value == null || $.trim(this.value) === '') {
                $(".error-message").text("Trường này bắt buộc phải điền")
                $(".lb-message").css("color", "red")
                $("#transaction_message").css("border-color", "#e73d4a")
            } else {
                $(".error-message").text("")
                $(".lb-message").css("color", "black")
                $(this).css("border-color", "")
            }

        })

        $("#date_check").change(function () {

            date_check = this.value

            getTicketsToday(customer_id, region_id, date_check)
        })

        function validateForm() {
            var checkRegion = $("#transaction_region").val();
            var checkCustomer = $("#customer").val();
            var checkMessage = $("#transaction_message").val();
            // var c = document.forms["Form"]["answer_c"].value;
            let flag = true
            if (checkRegion == null || checkRegion === "") {
                $(".error-region").text("Trường này bắt buộc phải điền")
                $(".lb-region").css("color", "red")
                $("#transaction_region").css("border-color", "#e73d4a")
                flag = false
            }
            if (checkCustomer == null || checkCustomer === "") {
                $(".error-customer").text("Trường này bắt buộc phải điền")
                $(".lb-customer").css("color", "red")

                $(".select2-selection").css("border-color", "#e73d4a")
                flag = false
            }
            if (checkMessage == null || checkMessage === "") {
                $(".error-message").text("Trường này bắt buộc phải điền")
                $(".lb-message").css("color", "red")
                $("#transaction_message").css("border-color", "#e73d4a")
                flag = false
            }
            if (flag === false) {
                return false
            }
        }

        let arrCus = @json($customers)


        $(document).on('click', '.btn-delete', function () {
            let id = $(this).data('id');
            let modelDelete = $('#ModalDelete');
            modelDelete.modal('show');
            $("#delete-sumbit").click(function () {
                $.ajax({
                    url: "{{route('api.ticket.delete')}}",
                    type: "POST",
                    data: {
                        id: id,
                        _token: "{{ csrf_token() }}",
                    },
                    success: function (response) {
                        $(`.btn-delete[data-id=${id}]`).parent().parent().parent().remove();
                        modelDelete.modal('hide');
                    }
                });
            });
        });
        $(document).on('click', '.btn-update', function () {
            let id = $(this).data('id');
            $("#table-content").empty()
            let modelUpdate = $('#ModalDetail');
            let oldMessage = ""
            modelUpdate.find("#message").bind('input propertychange', function () {
                if (this.value == null || $.trim(this.value) === '') {
                    $(".error-message").text("Trường này bắt buộc phải điền")
                    $(".label-message").css("color", "red")
                    modelUpdate.find("#message").css("border-color", "#e73d4a")
                } else {
                    $(".error-message").text("")
                    $(".label-message").css("color", "black")
                    $(this).css("border-color", "")
                }
            })
            $.ajax({
                url: "{{route('api.ticket.show')}}",

                data: {
                    id: id,
                },
                success: function (response) {
                    $(".error-message").text("")
                    $(".label-message").css("color", "black")
                    modelUpdate.find("#message").css("border-color", "")
                    let resMess = response.data.message
                    let showMessage = ""
                    if (resMess.includes("2 so")) {
                        showMessage = resMess.substring(0, resMess.indexOf("2 so:"));
                    } else if (resMess.includes("3 so")) {
                        showMessage = resMess.substring(0, resMess.indexOf("3 so:"));
                    } else {
                        showMessage = resMess.substring(0, resMess.indexOf("4 so:"));
                    }

                    oldMessage = showMessage
                    modelUpdate.find("#message").val(showMessage);
                    var lines = document.getElementById("message").value.split("\n");
                    document.getElementById("message").rows = lines.length;
                    modelUpdate.modal('show')
                    if (response.data.histories !== null && response.data.histories.length > 0) {
                        $("#table-content").append(`<table class="table table-w table-striped no-footer text-sm collapsed">
                                        <thead>
                                        <tr>
                                            <th style="width: 5%;">STT</th>
                                            <th class="text-nowrap" style="width: 10%;"> Thời gian</th>
                                            <th style="width: 85%;"> Tin đã sửa</th>
                                        </tr>
                                        </thead>
                                        <tbody id="body-render" >

                                          </tbody>
                                    </table>
                                    `)
                        let i = 0;

                        response.data.histories.forEach(function (e) {
                            i++;
                            let ms = !e.message_show ? e.message : e.message_show
                            let messageShow = ms.replace(/\r\n/g, "<br>")

                            $("#body-render").append(`
                                        <tr>
                                            <td>${i}</td>
                                            <td>${e.created_at}</td>
                                            <td>${messageShow}</td>
                                        </tr>
                                         `)
                        })


                    }

                }
            });

            $("#update-submit").off('click').click(function () {

                var checkMessage = modelUpdate.find("#message").val();
                let flag = true;
                if (checkMessage == null || checkMessage === "") {
                    $(".error-message").text("Trường này bắt buộc phải điền")
                    $(".label-message").css("color", "red")
                    modelUpdate.find("#message").css("border-color", "#e73d4a")
                    return false;
                }

                if ((oldMessage.replace(/\s/g, "") !== modelUpdate.find("#message").val().replace(/\s/g, "")) && flag === true) {
                    $.ajax({
                        url: "{{route('api.ticket.update')}}",
                        type: "POST",
                        data: {
                            id: id,
                            message: modelUpdate.find("#message").val(),

                            _token: "{{ csrf_token() }}",
                        },
                        success: function (response) {


                            if (response.status === 'error') {


                                $(".info-message").attr('data-message', response.messageErr)

                                $("#message").highlightWithinTextarea({
                                    highlight: [
                                        {
                                            highlight: $(".info-message").attr('data-message'),
                                            className: 'draw-color'
                                        }
                                    ]
                                });

                                $('.error-message').text(response.message)

                                $(".label-message").css("color", "red")
                                modelUpdate.find("#message").css("border-color", "#e73d4a")
                            } else {
                                modelUpdate.modal('hide')
                                let url = '{{  url('')}}' + "/ticket/create?region_id=1&customer_id=9&date_check=16/07/2023";
                                let parts = url.split("?");
                                let queryString = "";
                                if (parts.length > 1) {
                                    queryString = parts[0];
                                }
                                window.location.href = queryString + '?region_id=' + region_id + '&customer_id=' + customer_id + '&date_check=' + date_check.trim();
                            }

                        }
                    });
                } else {
                    $(".error-message").text("Vui lòng thay đổi tin nhắn")
                }
            })
        });

        function getTicketsToday(customer_id, region_id, date_check) {
            if (customer_id !== 0 && region_id !== 0) {
                $.ajax({
                    url: "{{route('api.get_ticket_today')}}",
                    type: "GET",
                    data: {
                        customer_id: customer_id,
                        region_id: region_id,
                        date_check: date_check,
                    },

                    success: function (response) {
                        //

                        let cus = null
                        if (Object.keys(arrCus).length == 1) {
                            cus = Object.values(arrCus).find(x => x.id == customer_id)
                        } else {
                            cus = arrCus.find(x => x.id == customer_id)
                        }


                        let urlRedirect = "#"
                        if (cus) {
                            urlRedirect = "{{url('/report/detail')}}/" + `${cus.id}?region_id=${region_id}&date=${date_check.replace(/\//g, "-").trim()}`

                        }

                        $("#transaction-messages-view").empty()

                        if (response != null && response.length !== 0) {
                            $("#transaction-messages-view").append(`<div class="form-body">
                        <div class="caption" style="margin-bottom: 20px;margin-top: 20px;">
                            <i class="icon-info font-green"></i>
                            <span class="caption-subject font-bold bold uppercase">Tin nhắn đã nhập trong ngày</span>
                        </div>
                        <div class="portlet-body table-responsive" style="overflow: auto;">
                            <table class="table table-bordered"
                                    style="width:100%;">
                                <thead>
                                <tr>
                                    <th style="width: 5%;"> # </th>
                                    <th style="width: 80%;"> Tin nhắn của <a href="${urlRedirect}" style="color: #2e59d9">${cus.username}</a>  </th>
                                    <th class="text-nowrap" style="text-align:center;width: 10%;"> Tùy chỉnh </th>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>
                        </div>
                    </div>`)
                            let i = response.length + 1;
                            response.forEach(function (e) {
                                i--;
                                let ms = !e.message_show ? e.message : e.message_show
                                let arrayString = ms.split("\r\n\r\n")
                                console.log(e.message_show)
                                console.log(e.message)
                                let getLastIndexArr = null
                                let getLastIndexArr2nd = null
                                let htmlString = ''
                                if (arrayString.find(x => x.includes("Số trúng"))) {

                                    htmlString = ''
                                    if (arrayString.length === 1) {

                                        htmlString = arrayString[0].replace(/\r\n/g, "<br>")
                                    } else {
                                        getLastIndexArr = arrayString[arrayString.length - 1];
                                        getLastIndexArr2nd = arrayString[arrayString.length - 2];
                                        arrayString = arrayString.filter(e => e !== getLastIndexArr && e !== getLastIndexArr2nd)
                                        getLastIndexArr2nd = getLastIndexArr2nd.replace(/\r\n/g, "<br>")
                                        getLastIndexArr = getLastIndexArr.replace(/\r\n/g, "<br>")
                                        arrayString.forEach(function (item) {
                                            item.replace(/\r\n/g, "<br>")
                                            htmlString += '<p style="white-space: pre-line">' + item + '</p>';

                                        })
                                    }
                                } else {
                                    htmlString = ''

                                    if (arrayString.length === 1) {

                                        htmlString = arrayString[0].replace(/\r\n/g, "<br>")


                                    } else {
                                        getLastIndexArr = arrayString[arrayString.length - 1];
                                        arrayString = arrayString.filter(e => e !== getLastIndexArr)
                                        getLastIndexArr = getLastIndexArr.replace("\r\n", "<br>")
                                        arrayString.forEach(function (item) {
                                            item.replace(/\r\n/g, "<br>")
                                            htmlString += '<p style="white-space: pre-line">' + item + '</p>';

                                        })
                                    }
                                }
                                $(".table > tbody ").append(`<tr>
                                    <td><p>${i}</p></td>
                                    <td>
                                        <div>${htmlString}</div>
                                        ${getLastIndexArr2nd ? `<p class="text-danger">${getLastIndexArr2nd}</p>` : ''}
                                        ${getLastIndexArr ? `<p class="text-primary">${getLastIndexArr}</p>` : ''}
                                             ${e.message_skip ? `<p class="text-info " style="white-space: pre-line">${e.message_skip.replace("\r\n", "</br>")}</p>` : ''}

                                    </td>
                                    <td class="table-td-center text-center">
                                        <p>
                                            <button style="color:white;" type="button" class="btn btn-icon-only btn-danger transaction-customer-delete btn-delete" data-id="${e.id}" title="Xoá" data-transaction-id="3528180">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        </p>
                                        <p>
                                            <button style="color:white;" type="button" class="btn btn-icon-only btn-edit transaction-customer-edit btn-update" title="Sửa" data-id="${e.id}" data-transaction-id="3528180">
                                                <i class="fa fa-edit"></i>
                                            </button>
                                        </p>
                                    </td>
                                </tr>
                                `)


                            })

                        }
                    }
                });
            }
        }
    </script>
@endpush
