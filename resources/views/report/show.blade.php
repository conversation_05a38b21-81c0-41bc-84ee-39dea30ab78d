@extends('layout.master')
@section('content')
    @push("head")

        <link href="{{asset('css/datatables.min.css')}}" rel="stylesheet">
    @endpush

    <div class="container-fluid">
        <div class="col-md-12">
            {{ Breadcrumbs::render('report.show', auth()->guard('machine')->user() ?? auth()->user(),$customer) }}
            <!-- Begin: life time stats -->
            <div class="portlet light portlet-fit portlet-datatable bordered">

                <div class="portlet-title">
                    <div class="caption">
                        <h3 class="font-bold mb-4 font-title uppercase">LỊCH SỬ: {{$name}}</h3>
                    </div>
                    <!--            <div class="btn-group btn-group-devided">-->
                    <!--              <a onclick="calcResults()" class="btn btn-circle blue btn-outline">-->
                    <!--                <i class="fa fa-server" aria-hidden="true"></i>-->
                    <!--                Tính tiền-->
                    <!--              </a>-->
                    <!--            </div>-->
                </div>
                <div class="portlet-body mobile">
                    <div class="actions mb-3" style="text-align: end">
                        <div class="btn-group btn-group-devided ">
                            <a style="font-weight: 500;" href="{{route('ticket.create')}}"
                               class="btn btn-color-main text-main text-white px-3 py-2">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                                Thêm tin nhắn
                            </a>
                        </div>
                        <div class="btn-group">
                            <a class="btn btn-outline-warning text-default" style="padding: 9px 16px;"
                               href="javascript:" data-toggle="dropdown"
                               aria-expanded="false">
                                <i class="fa fa-share"></i>
                                <span class="hidden-xs"> Công cụ </span>
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu pull-right dropdown-menu-right shadow"
                                aria-labelledby="userDropdown">
                                <li class="dropdown-item">
                                    <a href="#" id="report_export_excel"> Xuất ra Excel </a>
                                </li>
                                <li class="dropdown-item">
                                    <a href="#" id="report_export_csv"> Xuất ra CSV </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="table-responsive" style="overflow: auto">
                        <table
                            class="table table-bordered text-nowrap"
                            style="width:100%;" id="table">
                            <thead>
                            <tr class="table-fz">
                                <th style="width:5%;">#ID</th>
                                <th style="width:8%;">Ngày</th>
                                <th style="width:8%;">Tiền xác</th>
                                <th style="width:8%;">Tiền trúng</th>
                                <th style="width:8%;">Tổng tiền</th>
                                <th style="width:10%;">Tiền xác (Khách)</th>
                                <th style="width:11%;">Tiền trúng (Khách)</th>
                                <th style="width:11%;">Tổng tiền (Khách)</th>
                                <th style="width:5%;">Actions</th>
                            </tr>
                            </thead>
                        </table>

                    </div>
                </div>
            </div>
            <!-- End: life time stats -->
        </div>
    </div>
    <div class="modal fade text-left r-0 l-0" id="ModalDelete" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0" role="document">
            <div class="modal-content mx-3">
                <div class="d-flex items-center justify-between p-3 border-bottom-main">
                    <h5 class="font-title font-bold mb-0">Bảng xác nhận</h5>
                    <button ata-toggle="modal" data-toggle="modal" data-target="#ModalDelete" type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal">
                        <i class="fas fa-times" style="font-size: 18px;" aria-hidden="true"></i>
                        <span class="sr-only">Close modal</span>
                    </button>
                </div>

                <div class="mt-4 p-3 text-center" style="font-size:15px; color: #111">
                    <i class="fa fa-trash-alt text-danger mb-2" style="font-size: 50px;"></i>
                    <p class="font-semibold mt-2">Bạn có chắc muốn xóa tin nhắn này không ?</p>
                </div>
                <div class="d-flex justify-center pb-3 mb-1" style="gap:10px;">
                    <button id="delete-sumbit" class="btn text-white font-bold bg-danger" style="width: 100px;"
                            type="button">Xóa
                    </button>
                    <button id="form-transaction-submit-btn" class="btn btn-dark text-white font-bold"
                            style="width: 100px;" type="button" data-toggle="modal" data-target="#ModalDelete">Đóng
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="loading-overlay" style="
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.6);
    z-index: 9999;
    display: none;
    justify-content: center;
    align-items: center;
">
        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
        </div>
    </div>

@endsection

@push('js')

    <script src="{{asset('js/datatables.min.js')}}"></script>
    <script>
        $(function () {

            var table = $('#table').DataTable({
                processing: true,
                serverSide: true,
                pageLength: 10,
                paging: true,
                lengthChange: true,
                // responsive: true,
                pagingType: 'simple_numbers',
                order: [[1, 'desc']],
                language: {
                    paginate: {
                        previous: '<i class="fas fa-angle-left" style="font-size:20px"></i>',
                        next: '<i class="fas fa-angle-right" style="font-size:20px"></i>',
                    },
                    lengthMenu: "Hiển thị  _MENU_",
                    // info: "Hiển thị trang _PAGE_/_PAGES_",
                },

                dom:
                    "<'overflow-auto d-flex'<'table-flex'f>>" +
                    "<'d-flex items-center mb-3 overflow-auto'<'col-sm-12 p-0'tr>>" +
                    "<'d-flex items-center flex-row-reverse justify-content-between flex-wrap'<'table-flex mb-2'p><'table-flex'l>>",

                lengthMenu: [10, 25, 50, 100, 150],
                ajax: ' {!!  url('/api/report/index') .'/'.$customer_id !!}',
                drawCallback: function (settings) {
                    $('.btn-delete').click(function () {
                        let date_check = $(this).attr('id');
                        let modelDelete = $('#ModalDelete');
                        modelDelete.modal('show');
                        $("#delete-sumbit").click(function () {
                            $.ajax({
                                url: "{{route('api.report.detail.delete')}}",
                                type: "POST",
                                data: {
                                    date_check: date_check,
                                    customer: '{{$customer_id}}',

                                    _token: "{{ csrf_token() }}",
                                },
                                success: function (response) {

                                    modelDelete.modal('hide');
                                    table.ajax.reload();
                                    // location.reload();
                                }
                            });
                        });
                    });
                    let flag = true
                    $('.btn-reload').click(function () {
                        let date_check = $(this).attr('data-date');
                        if (flag) {
                            flag = false
                            $('#loading-overlay').fadeIn();
                            $('#loading-overlay').addClass("d-flex");

                            $.ajax({
                                url: "{{route('api.report.show.refresh')}}",
                                type: "POST",
                                data: {
                                    date_check: date_check,
                                    customer: '{{$customer_id}}',
                                    _token: "{{ csrf_token() }}",
                                },
                                timeout: 57000,
                                success: function (response) {
                                    if (response.status !== null && response.status === 'error') {
                                        // Nếu có lỗi, hiển thị alert với message từ server
                                        alert(response.message);
                                        $('#loading-overlay').fadeOut();
                                        $('#loading-overlay').removeClass("d-flex");

                                        flag = true;
                                    } else {
                                        location.reload(); // success: reload page, spinner tự mất
                                    }
                                },
                                error: function () {
                                    alert('Đã có lỗi xảy ra. Vui lòng liên lạc admin để xử lý (504 time out)');
                                    $('#loading-overlay').fadeOut();
                                    $('#loading-overlay').removeClass("d-flex");
                                    flag = true;
                                }
                            });
                        }
                    });
                },
                columns: [
                    {
                        data: 'action',
                        render: function (data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    {
                        'name': 'date_check',
                        'data': {
                            '_': 'date_check.display',
                            'sort': 'date_check.timestamp'
                        }
                    },
                    {data: 'tien_xac', name: 'tien_xac'},
                    {data: 'tien_trung', name: 'tien_trung'},
                    {
                        data: 'tong_tien', render: function (data, type, row, meta) {

                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },
                    {data: 'tien_xac_2nd', name: 'tien_xac_2nd'},
                    {data: 'tien_trung_2nd', name: 'tien_trung_2nd'},
                    {
                        data: 'tong_tien_2nd', render: function (data, type, row, meta) {

                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },
                    {data: 'action', name: 'action'}
                ]
            });

            $('#table tbody').on('click', 'tr td:last-child', function (e) {
                e.stopPropagation();
            });


            $('#table tbody').on('click', 'tr', function () {
                var data = table.row(this).data();

                window.location.href = "{{url('/report/detail')}}/" + {{$customer_id}} + "?date=" + data.date_check.display + "&region_id=1";
            });
        });


    </script>
@endpush
