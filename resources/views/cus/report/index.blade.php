@extends('layout.master')
@section('content')
    @push("head")

        <link href="{{asset('css/datatables.min.css')}}" rel="stylesheet">
    @endpush

    <style>
        .mg-left {
            margin-left: -100px;
        }
    </style>
    <div class="container-fluid">
        <div class="col-md-12">


            <!-- Begin: life time stats -->
            <div class="portlet light portlet-fit portlet-datatable bordered">

                <div class="portlet-title">
                    <div class="caption" style="width:200px">
                        <i class="icon-settings font-dark"></i>
                        <h3 class="font-bold mb-4 font-title uppercase">Báo cáo: Khách</h3>
                    </div>
                </div>
                <div class="portlet-body mobile">
                    <div class="actions mb-3" style="text-align: end">
                        <div class="btn-group btn-group-devided ">
                            <a style="font-weight: 500;" href="{{route('ticket.create')}}"
                               class="btn btn-color-main text-main text-white px-3 py-2">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                                Thêm tin nhắn
                            </a>
                        </div>
                        <div class="btn-group">
                            <a class="btn btn-outline-warning text-default" style="padding: 9px 16px;" href="javascript:" data-toggle="dropdown"
                               aria-expanded="false">
                                <i class="fa fa-share"></i>
                                <span class="hidden-xs"> Công cụ </span>
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu pull-right dropdown-menu-right shadow" aria-labelledby="userDropdown">
                                <li class="dropdown-item">
                                    <a href="#" id="report_customer_export_excel"> Xuất ra Excel </a>
                                </li>
                                <li class="dropdown-item">
                                    <a href="#" id="report_customer_export_csv"> Xuất ra CSV </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="table-responsive" style="overflow: auto">
                        <table
                            class="table table-bordered text-nowrap"
                            style="width:100%;" id="table">
                            <thead>
                            <tr class="table-fz">
                                <th style="width:5%;">#ID</th>
                                <th style="width:8%;">User Name</th>
                                <th style="width:8%;">Tiền xác</th>
                                <th style="width:8%;">Tiền trúng</th>
                                <th style="width:8%;">Tổng tiền</th>
                                <th style="width:10%;">Tiền xác (Khách)</th>
                                <th style="width:11%;">Tiền trúng (Khách)</th>
                                <th style="width:11%;">Tổng tiền (Khách)</th>
                                <th style="width:5%;">Ngày</th>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            <!-- End: life time stats -->
        </div>

    </div>

@endsection

@push('js')
    <script src="{{asset('js/datatables.min.js')}}"></script>

    <script>


        $(function () {
            var table = $('#table').DataTable({
                processing: true,
                serverSide: true,
                pageLength: 100,
                paging: true,
                lengthChange: true,
                // responsive: true,
                pagingType: 'simple_numbers',
                order: [[1, 'asc']],
                language: {
                    paginate: {
                        previous: '<i class="fas fa-angle-left" style="font-size:20px"></i>',
                        next: '<i class="fas fa-angle-right" style="font-size:20px"></i>',
                    },
                    lengthMenu: "Hiển thị  _MENU_",
                    // info: " _PAGE_/_PAGES_",
                },
                dom:
                    "<'overflow-auto d-flex'<'table-flex'f>>" +
                    "<'d-flex items-center mb-3 overflow-auto'<'col-sm-12 p-0'tr>>" +
                    "<'d-flex items-center flex-row-reverse justify-content-between flex-wrap'<'table-flex mb-2'p><'table-flex'l>>",
                lengthMenu: [10, 25, 50, 100, 150],
                ajax: '{!! route('cus.api.report.index') !!}',
                columnDefs: [
                    {searchable: false, targets: [0, 2, 3, 4, 5, 6, 7, 8]} // Ví dụ chỉ tìm kiếm cho cột thứ nhất
                ],
                drawCallback: function (settings) {
                    var api = this.api();
                    var rows = api.rows({
                        page: 'current'
                    }).nodes();
                    var count = 1;
                    $.each(rows, function (i, row) {
                        $('td:eq(0)', row).html(count);
                        count++;
                    });
                },
                columns: [
                    {data: 'id', name: 'id'},
                    {data: 'username', name: 'username'},
                    {data: 'tien_xac', name: 'tien_xac'},
                    {data: 'tien_trung', name: 'tien_trung'},
                    {
                        data: 'tong_tien', render: function (data, type, row, meta) {
                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },
                    {data: 'tien_xac_2nd', name: 'tien_xac_2nd'},
                    {data: 'tien_trung_2nd', name: 'tien_trung_2nd'},
                    {
                        data: 'tong_tien_2nd', render: function (data, type, row, meta) {

                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },
                    {data: 'created_at', name: 'created_at'},

                ]
            });

            $('#table tbody').on('click', 'tr td:first-child', function (e) {
                e.stopPropagation();
            });

            $('#table tbody').on('click', 'tr', function () {
                var data = table.row(this).data();

                window.location.href = "{{url('/cus/report')}}" + "/" + data.id;
            });
        });


    </script>
@endpush
