@extends('layout.master')
@section('content')
    @push('head')
        <link href="{{ asset('css/datatables.min.css') }}" rel="stylesheet">
        <link href="{{ asset('css/highlight-within-textarea.min.css') }}" rel="stylesheet">

        <style>
            .draw-color {
                background-color: #ffec99;
            }

            .custom-input {
                width: 4ch;
            }

            input[type=number]::-webkit-inner-spin-button,
            input[type=number]::-webkit-outer-spin-button {
                -webkit-appearance: none;
                margin: 0;
            }

            input[type=number] {
                -moz-appearance: textfield;
                appearance: none;
            }
        </style>
    @endpush
    @php
        $hai_so_result = $report->hai_so_trung
            ? $report->hai_so_ration - $report->hai_so_percent
            : -$report->hai_so_percent;
        $formatted_hai_so_result = number_format($hai_so_result, 1, '.', ',');
        $class_hai_so = $hai_so_result < 0 ? 'text-danger' : 'text-primary';

        $ba_so_result = $report->ba_so_trung ? $report->ba_so_ration - $report->ba_so_percent : -$report->ba_so_percent;
        $formatted_ba_so_result = number_format($ba_so_result, 1, '.', ',');
        $class_ba_so = $ba_so_result < 0 ? 'text-danger' : 'text-primary';

        $bon_so_result = $report->bon_so_trung
            ? $report->bon_so_ration - $report->bon_so_percent
            : -$report->bon_so_percent;
        $formatted_bon_so_result = number_format($bon_so_result, 1, '.', ',');
        $class_bon_so = $bon_so_result < 0 ? 'text-danger' : 'text-primary';

        $da_thang_result = $report->da_thang_trung
            ? $report->da_thang_ration - $report->da_thang_percent
            : -$report->da_thang_percent;
        $formatted_da_thang_result = number_format($da_thang_result, 1, '.', ',');
        $class_da_thang = $da_thang_result < 0 ? 'text-danger' : 'text-primary';

        $da_xien_result = $report->da_xien_trung
            ? $report->da_xien_ration - $report->da_xien_percent
            : -$report->da_xien_percent;
        $formatted_da_xien_result = number_format($da_xien_result, 1, '.', ',');
        $class_da_xien = $da_xien_result < 0 ? 'text-danger' : 'text-primary';

        $dau_duoi_result = $report->dau_duoi_trung
            ? $report->dau_duoi_ration - $report->dau_duoi_percent
            : -$report->dau_duoi_percent;
        $formatted_dau_duoi_result = number_format($dau_duoi_result, 1, '.', ',');
        $class_dau_duoi = $dau_duoi_result < 0 ? 'text-danger' : 'text-primary';

        $xiu_chu_result = $report->xiu_chu_trung
            ? $report->xiu_chu_ration - $report->xiu_chu_percent
            : -$report->xiu_chu_percent;
        $formatted_xiu_chu_result = number_format($xiu_chu_result, 1, '.', ',');
        $class_xiu_chu = $xiu_chu_result < 0 ? 'text-danger' : 'text-primary';
    @endphp
    <div class="container-fluid">
        <div class="col-md-12">
            <div class="portlet light portlet-fit portlet-datatable bordered">

                <div class="portlet-title">
                    <div class="caption">

                        <i class="icon-settings font-dark"></i>
                        <span class="caption-subject font-bold bold uppercase">THỐNG KÊ TỪ NGÀY
                            {{ date('d-m-Y', strtotime($date_start)) }} ĐẾN NGÀY {{ date('d-m-Y', strtotime($date_end)) }}
                            CỦA {{ $name }}</span>
                    </div>

                    <!--            <div class="btn-group btn-group-devided">-->
                    <!--              <a onclick="calcResults()" class="btn btn-circle blue btn-outline">-->
                    <!--                <i class="fa fa-server" aria-hidden="true"></i>-->
                    <!--                Tính tiền-->
                    <!--              </a>-->
                    <!--            </div>-->

                </div>
                <div class="portlet-body mobile">
                    <div class="actions mb-4" style="text-align: end">
                        <div class="btn-group btn-group-devided ">
                            <a style="font-weight: 500;" href="{{ route('ticket.create') }}"
                                class="btn btn-color-main text-main text-white px-3 py-2">
                                <i class="fa fa-plus" aria-hidden="true"></i>
                                Thêm tin nhắn
                            </a>
                        </div>
                        <div class="btn-group">
                            <a class="btn btn-outline-warning text-default" style="padding: 9px 16px;" href="javascript:"
                                data-toggle="dropdown" aria-expanded="false">
                                <i class="fa fa-share"></i>
                                <span class="hidden-xs"> Công cụ </span>
                                <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu pull-right dropdown-menu-right shadow" aria-labelledby="userDropdown">
                                <li class="dropdown-item">
                                    <a href="#" id="report_export_excel"> Xuất ra Excel </a>
                                </li>
                                <li class="dropdown-item">
                                    <a href="#" id="report_export_csv"> Xuất ra CSV </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    @if ($region_id != 0)
                        <div class="btn-detail">
                            <a href="{{ url('/statistic/detail') }}/{{ $customer_id }}?date_start={{ $date_start }}&date_end={{ $date_end }}&region_id=1"
                                class="{{ $region_id == 1 ? 'active' : '' }}">
                                Miền Nam ({{ $count_south }})</a>
                            <a href="{{ url('/statistic/detail') }}/{{ $customer_id }}?date_start={{ $date_start }}&date_end={{ $date_end }}&region_id=3"
                                class="{{ $region_id == 3 ? 'active' : '' }}">
                                Miền Trung ({{ $count_middle }})</a>
                            <a href="{{ url('/statistic/detail') }}/{{ $customer_id }}?date_start={{ $date_start }}&date_end={{ $date_end }}&region_id=2"
                                class="{{ $region_id == 2 ? 'active' : '' }}">
                                Miền Bắc ({{ $count_north }})</a>

                        </div>
                    @endif
                    <div class="" style="overflow: auto">
                        <table class="table table-bordered table1" style="width:100%;">
                            <thead>
                                <tr>
                                    <th style="width: 25%;"></th>
                                    <th style="width: 25%;"> Tổng tiền đánh</th>
                                    <th style="width: 25%;"> Tổng tiền trúng</th>
                                    <th style="width: 25%;"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($report)

                                    @if ($report->hai_so)
                                        <tr id="table1_haiso" ondblclick="handle('table2_haiso')">

                                            <td>BL 2SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false" type="number"
                                                        step="any" value="{{ $ration_user->hai_so_percent }}"
                                                        name="" id="hai_so_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('hai_so_percent','hai_so_danh','hai_so_result','tong_hai_so_danh','tong_hai_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="hai_so_danh">
                                                -{{ number_format($report->hai_so, 1, '.', ',') }}
                                                (-{{ number_format($report->hai_so_percent, 1, '.', ',') }}) </td>

                                            <td class="text-primary" id="hai_so_trung">
                                                {{ $report->hai_so_trung ? number_format($report->hai_so_trung, 1, '.', ',') . ' (' . number_format($report->hai_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}
                                            </td>
                                            <td class="{{ $class_hai_so }}" id="hai_so_result">
                                                {{ $formatted_hai_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->ba_so)
                                        <tr id="table1_baso" ondblclick="handle('table2_baso')">
                                            <td>BL 3SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false" type="number"
                                                        step="any" value="{{ $ration_user->ba_so_percent }}"
                                                        name="" id="ba_so_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('ba_so_percent','ba_so_danh','ba_so_result','tong_ba_so_danh','tong_ba_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td id="ba_so_danh" class="text-danger">
                                                -{{ number_format($report->ba_so, 1, '.', ',') }}
                                                (-{{ number_format($report->ba_so_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="ba_so_trung">
                                                {{ $report->ba_so_trung ? number_format($report->ba_so_trung, 1, '.', ',') . ' (' . number_format($report->ba_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="ba_so_result" class="{{ $class_ba_so }}">
                                                {{ $formatted_ba_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->bon_so)
                                        <tr id="table1_bonso" ondblclick="handle('table2_bonso')">
                                            <td>BL 4SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false" type="number"
                                                        step="any" value="{{ $ration_user->bon_so_percent }}"
                                                        name="" id="bon_so_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('bon_so_percent','bon_so_danh','bon_so_result','tong_bon_so_danh','tong_bon_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td id="bon_so_danh" class="text-danger">
                                                -{{ number_format($report->bon_so, 1, '.', ',') }}
                                                (-{{ number_format($report->bon_so_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="bon_so_trung">
                                                {{ $report->bon_so_trung ? number_format($report->bon_so_trung, 1, '.', ',') . ' (' . number_format($report->bon_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="bon_so_result" class="{{ $class_bon_so }}">
                                                {{ $formatted_bon_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->da_thang)
                                        <tr id="table1_dathang" ondblclick="handle('table2_dathang')">
                                            <td>DATHANG
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->da_thang_percent }}" name=""
                                                        id="da_thang_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('da_thang_percent','da_thang_danh','da_thang_result','tong_hai_so_danh','tong_hai_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="da_thang_danh">
                                                -{{ number_format($report->da_thang, 1, '.', ',') }}
                                                (-{{ number_format($report->da_thang_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="da_thang_trung">
                                                {{ $report->da_thang_trung ? number_format($report->da_thang_trung, 1, '.', ',') . ' (' . number_format($report->da_thang_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="da_thang_result" class="{{ $class_da_thang }}">
                                                {{ $formatted_da_thang_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->da_xien)
                                        <tr id="table1_daxien" ondblclick="handle('table2_daxien')">
                                            <td>DAXIEN
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->da_xien_percent }}" name=""
                                                        id="da_xien_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('da_xien_percent','da_xien_danh','da_xien_result','tong_hai_so_danh','tong_hai_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="da_xien_danh">
                                                -{{ number_format($report->da_xien, 1, '.', ',') }}
                                                (-{{ number_format($report->da_xien_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="da_xien_trung">
                                                {{ $report->da_xien_trung ? number_format($report->da_xien_trung, 1, '.', ',') . ' (' . number_format($report->da_xien_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="da_xien_result" class="{{ $class_da_xien }}">
                                                {{ $formatted_da_xien_result }}</td>
                                        </tr>
                                    @endif

                                    @if ($report->dau_duoi)
                                        <tr id="table1_dauduoi" ondblclick="handle('table2_dauduoi')">
                                            <td>DD
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->dau_duoi_percent }}" name=""
                                                        id="dau_duoi_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('dau_duoi_percent','dau_duoi_danh','dau_duoi_result','tong_hai_so_danh','tong_hai_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td id="dau_duoi_danh" class="text-danger">
                                                -{{ number_format($report->dau_duoi, 1, '.', ',') }}
                                                (-{{ number_format($report->dau_duoi_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="dau_duoi_trung">
                                                {{ $report->dau_duoi_trung ? number_format($report->dau_duoi_trung, 1, '.', ',') . ' (' . number_format($report->dau_duoi_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="dau_duoi_result" class="{{ $class_dau_duoi }}">
                                                {{ $formatted_dau_duoi_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->xiu_chu)
                                        <tr id="table1_xiuchu" ondblclick="handle('table2_xiuchu')">
                                            <td>XC
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->xiu_chu_percent }}" name=""
                                                        id="xiu_chu_percent" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('xiu_chu_percent','xiu_chu_danh','xiu_chu_result','tong_ba_so_danh','tong_ba_so_result','total_danh','total_result')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="xiu_chu_danh">
                                                -{{ number_format($report->xiu_chu, 1, '.', ',') }}
                                                (-{{ number_format($report->xiu_chu_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="xiu_chu_trung">
                                                {{ $report->xiu_chu_trung ? number_format($report->xiu_chu_trung, 1, '.', ',') . ' (' . number_format($report->xiu_chu_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="xiu_chu_result" class="{{ $class_xiu_chu }}">
                                                {{ $formatted_xiu_chu_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->tong_tien_2so)
                                        <tr>
                                            <td>Tổng 2 số</td>
                                            <td class="text-danger" id="tong_hai_so_danh">
                                                -{{ number_format($report->tong_tien_2so, 1, '.', ',') . ' (-' . number_format($report->tong_tien_2so_lai, 1, '.', ',') . ')' }}
                                            </td>
                                            <td class="text-primary" id="tong_hai_so_trung">
                                                {{ $report->tong_tien_2so_trung ? number_format($report->tong_tien_2so_trung, 1, '.', ',') . ' (' . number_format($report->tong_tien_2so_trung_lai, 1, '.', ',') . ')' : '0.0 (0.0)' }}
                                            </td>
                                            <td id="tong_hai_so_result"
                                                class="{{ $report->tong_tien_2so_trung_lai - $report->tong_tien_2so_lai >= 0 ? 'text-primary' : 'text-danger' }}">
                                                {{ number_format($report->tong_tien_2so_trung_lai - $report->tong_tien_2so_lai, 1, '.', ',') }}

                                            </td>
                                        </tr>
                                    @endif
                                    @if ($report->tong_tien_3so)
                                        <tr>
                                            <td>Tổng 3 số</td>
                                            <td class="text-danger" id="tong_ba_so_danh">
                                                -{{ number_format($report->tong_tien_3so, 1, '.', ',') . ' (-' . number_format($report->tong_tien_3so_lai, 1, '.', ',') . ')' }}
                                            </td>
                                            <td class="text-primary" id="tong_ba_so_trung">
                                                {{ $report->tong_tien_3so_trung ? number_format($report->tong_tien_3so_trung, 1, '.', ',') . ' (' . number_format($report->tong_tien_3so_trung_lai, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="tong_ba_so_result"
                                                class="{{ $report->tong_tien_3so_trung_lai - $report->tong_tien_3so_lai >= 0 ? 'text-primary' : 'text-danger' }}">
                                                {{ number_format($report->tong_tien_3so_trung_lai - $report->tong_tien_3so_lai, 1, '.', ',') }}

                                            </td>
                                        </tr>
                                    @endif
                                    @if ($report->tong_tien_4so)
                                        <tr>
                                            <td>Tổng 4 số</td>
                                            <td class="text-danger" id="tong_bon_so_danh">
                                                -{{ number_format($report->tong_tien_4so, 1, '.', ',') . ' (-' . number_format($report->tong_tien_4so_lai, 1, '.', ',') . ')' }}
                                            </td>
                                            <td class="text-primary" id="tong_bon_so_trung">
                                                {{ $report->tong_tien_4so_trung ? number_format($report->tong_tien_4so_trung, 1, '.', ',') . ' (' . number_format($report->tong_tien_4so_trung_lai, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="tong_bon_so_result"
                                                class="{{ $report->tong_tien_4so_trung_lai - $report->tong_tien_4so_lai >= 0 ? 'text-primary' : 'text-danger' }}">
                                                {{ number_format($report->tong_tien_4so_trung_lai - $report->tong_tien_4so_lai, 1, '.', ',') }}

                                            </td>
                                        </tr>
                                    @endif

                                    <tr id="table1_thuchi">

                                        <td><b>Thu chi</b></td>
                                        <td id="total_danh" class="text-red">
                                            {{ $report->total_danh > 0 ? '-' : '' }}{{ number_format($report->total_danh, 0, '.', ',') . ' (' . ($report->total_danh > 0 ? '-' : '') . number_format($report->total_danh_lai, 0, '.', ',') . ')' }}
                                        </td>
                                        <td id="total_trung" class="text-primary">
                                            {{ number_format($report->total_trung, 1, '.', ',') . ' (' . number_format($report->total_trung_lai, 1, '.', ',') . ')' }}
                                        </td>
                                        <td id="total_result"
                                            class="{{ $report->total >= 0 ? 'text-primary' : 'text-danger' }}">
                                            {{ number_format($report->total, 0, '.', ',') . ' (' . number_format($report->total_lai, 0, '.', ',') . ')' }}
                                        </td>

                                    </tr>
                                @endif
                            </tbody>

                        </table>
                    </div>
                    <hr />
                    <hr />
                    <div class="" style="overflow: auto">
                        <table class="table table-bordered table2 hidden" style="width:100%;" id="table2">
                            <thead>
                                <tr>
                                    <th style="width: 25%;"></th>
                                    <th style="width: 25%;"> Tổng tiền đánh</th>
                                    <th style="width: 25%;"> Tổng tiền trúng</th>
                                    <th style="width: 25%;"></th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($report)

                                    @if ($report->hai_so)
                                        <tr id="table2_haiso" class="hidden">

                                            <td>BL 2SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->hai_so_percent }}" name=""
                                                        id="hai_so_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('hai_so_percent2','hai_so_danh2','hai_so_result2','tong_hai_so_danh2','tong_hai_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td class="text-danger danh" id="hai_so_danh2">
                                                -{{ number_format($report->hai_so, 1, '.', ',') }}
                                                (-{{ number_format($report->hai_so_percent, 1, '.', ',') }}) </td>

                                            <td class="text-primary trung">
                                                {{ $report->hai_so_trung ? number_format($report->hai_so_trung, 1, '.', ',') . ' (' . number_format($report->hai_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}
                                            </td>
                                            <td class="{{ $class_hai_so }} result" id="hai_so_result2">
                                                {{ $formatted_hai_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->ba_so)
                                        <tr id="table2_baso" class="hidden">
                                            <td>BL 3SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->ba_so_percent }}" name=""
                                                        id="ba_so_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('ba_so_percent2','ba_so_danh2','ba_so_result2','tong_ba_so_danh2','tong_ba_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td id="ba_so_danh2" class="text-danger danh">
                                                -{{ number_format($report->ba_so, 1, '.', ',') }}
                                                (-{{ number_format($report->ba_so_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary">
                                                {{ $report->ba_so_trung ? number_format($report->ba_so_trung, 1, '.', ',') . ' (' . number_format($report->ba_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="ba_so_result2" class="{{ $class_ba_so }}">
                                                {{ $formatted_ba_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->bon_so)
                                        <tr id="table2_bonso" class="hidden">
                                            <td>BL 4SO
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->bon_so_percent }}" name=""
                                                        id="bon_so_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('bon_so_percent2','bon_so_danh2','bon_so_result2','tong_bon_so_danh2','tong_bon_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td id="bon_so_danh2" class="text-danger">
                                                -{{ number_format($report->bon_so, 1, '.', ',') }}
                                                (-{{ number_format($report->bon_so_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary">
                                                {{ $report->bon_so_trung ? number_format($report->bon_so_trung, 1, '.', ',') . ' (' . number_format($report->bon_so_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="bon_so_result2" class="{{ $class_bon_so }}">
                                                {{ $formatted_bon_so_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->da_thang)
                                        <tr id="table2_dathang" class="hidden">
                                            <td>DATHANG
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->da_thang_percent }}" name=""
                                                        id="da_thang_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('da_thang_percent2','da_thang_danh2','da_thang_result2','tong_hai_so_danh2','tong_hai_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="da_thang_danh2">
                                                -{{ number_format($report->da_thang, 1, '.', ',') }}
                                                (-{{ number_format($report->da_thang_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="da_thang_trung2">
                                                {{ $report->da_thang_trung ? number_format($report->da_thang_trung, 1, '.', ',') . ' (' . number_format($report->da_thang_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="da_thang_result2" class="{{ $class_da_thang }}">
                                                {{ $formatted_da_thang_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->da_xien)
                                        <tr id="table2_daxien" class="hidden">
                                            <td>DAXIEN
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->da_xien_percent }}" name=""
                                                        id="da_xien_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('da_xien_percent2','da_xien_danh2','da_xien_result2','tong_hai_so_danh2','tong_hai_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="da_xien_danh2">
                                                -{{ number_format($report->da_xien, 1, '.', ',') }}
                                                (-{{ number_format($report->da_xien_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="da_xien_trung2">
                                                {{ $report->da_xien_trung ? number_format($report->da_xien_trung, 1, '.', ',') . ' (' . number_format($report->da_xien_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="da_xien_result2" class="{{ $class_da_xien }}">
                                                {{ $formatted_da_xien_result }}</td>
                                        </tr>
                                    @endif

                                    @if ($report->dau_duoi)
                                        <tr id="table2_dauduoi"class="hidden">
                                            <td>DD
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->dau_duoi_percent }}" name=""
                                                        id="dau_duoi_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('dau_duoi_percent2','dau_duoi_danh2','dau_duoi_result2','tong_hai_so_danh2','tong_hai_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td id="dau_duoi_danh2" class="text-danger">
                                                -{{ number_format($report->dau_duoi, 1, '.', ',') }}
                                                (-{{ number_format($report->dau_duoi_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="dau_duoi_trung2">
                                                {{ $report->dau_duoi_trung ? number_format($report->dau_duoi_trung, 1, '.', ',') . ' (' . number_format($report->dau_duoi_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="dau_duoi_result2" class="{{ $class_dau_duoi }}">
                                                {{ $formatted_dau_duoi_result }}</td>
                                        </tr>
                                    @endif
                                    @if ($report->xiu_chu)
                                        <tr id="table2_xiuchu" class="hidden">
                                            <td>XC
                                                @if ($ration_user)
                                                    <input class="form-control custom-input" error="false"
                                                        type="number" step="any"
                                                        value="{{ $ration_user->xiu_chu_percent }}" name=""
                                                        id="xiu_chu_percent2" aria-required="true"
                                                        style="display: inline-block; width: 4ch; vertical-align: middle;"
                                                        oninput="handle_change('xiu_chu_percent2','xiu_chu_danh2','xiu_chu_result2','tong_ba_so_danh2','tong_ba_so_result2','total_danh2','total_result2')">
                                                @endif
                                            </td>
                                            <td class="text-danger" id="xiu_chu_danh2">
                                                -{{ number_format($report->xiu_chu, 1, '.', ',') }}
                                                (-{{ number_format($report->xiu_chu_percent, 1, '.', ',') }})</td>
                                            <td class="text-primary" id="xiu_chu_trung2">
                                                {{ $report->xiu_chu_trung ? number_format($report->xiu_chu_trung, 1, '.', ',') . ' (' . number_format($report->xiu_chu_ration, 1, '.', ',') . ')' : '0.0 (0.0)' }}

                                            </td>
                                            <td id="xiu_chu_result2" class="{{ $class_xiu_chu }}">
                                                {{ $formatted_xiu_chu_result }}</td>
                                        </tr>
                                    @endif

                                    <tr id="table2_thuchi" >

                                        <td><b>Thu chi</b></td>
                                        <td id="total_danh2" class="text-red">
                                            0.0 (0.0)
                                        </td>
                                        <td id="total_trung2" class="text-primary">
                                            0.0 (0.0)
                                        </td>
                                        <td id="total_result2"
                                            class="{{ $report->total >= 0 ? 'text-primary' : 'text-danger' }}">
                                            0.0 (0.0)
                                        </td>

                                    </tr>
                                @endif
                            </tbody>

                        </table>
                    </div>
                    <hr />
                    <hr />
                    <div class="btn-detail">

                        <a href="{{ url('/statistic/detail', ['customer_id' => $customer_id]) }}?date_start={{ $date_start }}&date_end={{ $date_end }}&region_id={{ request()->region_id }}"
                            class="{{ request()->type_view != 2 && request()->type_view != 3 ? 'active' : '' }}">
                            Xem mặc định
                        </a>
                        <a href="{{ request()->type_view
                            ? request()->fullUrlWithQuery(['type_view' => 2])
                            : request()->fullUrl() . '&' . http_build_query(['type_view' => 2]) }}"
                            class="{{ request()->type_view == 2 ? 'active' : '' }}">
                            Xem tin phân loại
                        </a>
                        <a href="{{ request()->type_view
                            ? request()->fullUrlWithQuery(['type_view' => 3])
                            : request()->fullUrl() . '&' . http_build_query(['type_view' => 3]) }}"
                            class="{{ request()->type_view == 3 ? 'active' : '' }}">
                            Xem tin trúng
                        </a>
                    </div>


                    <div class="table-responsive" style="overflow: auto">
                        <table class="table table-bordered table-baocao table-icon-center" style="width:100%;"
                            id="table">
                            <thead>
                                <tr class="table-fz text-nowrap">
                                    <th class="text-center" style="width:3%;">#ID</th>
                                    {{-- <th style="width:2%;">Nút</th> --}}
                                    <th class="text-nowrap" style="width:7%;">Thời gian</th>
                                    <th style="width:20%;">Tin nhắn</th>
                                    <th style="width:5%;">Tiền xác</th>
                                    <th style="width:8%;">Tiền trúng</th>
                                    <th style="width:10%;">Tổng tiền</th>
                                    <th style="width:10%;">Tiền xác (Khách)</th>
                                    <th style="width:10%;">Tiền trúng (Khách)</th>
                                    <th style="width:10%;">Tổng tiền (Khách)</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>

            </div>
        </div>
        <!-- End: life time stats -->
    </div>





@endsection
@push('js')
    <script src="{{ asset('js/datatables.min.js') }}"></script>
    <script src="{{ asset('js/highlight-within-textarea.min.js') }}"></script>
    <script src="{{ asset('js/autosize.min.js') }}"></script>
    <script>
        function autoResize(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }

        $('#ModalDetail').on('shown.bs.modal', function() {
            document.querySelectorAll('.auto-resize').forEach(textarea => {
                autoResize(textarea);
            });
        });

        document.querySelectorAll('.auto-resize').forEach(textarea => autoResize(textarea));
        $("#message").highlightWithinTextarea({
            highlight: [{
                highlight: $(".info-message").attr('data-message'),
                className: 'draw-color'
            }]
        });
        autosize($('textarea'));
        $('textarea').css("overflow-y", "scroll")
        $('#ModalDetail').on('shown.bs.modal', function() {
            document.querySelectorAll('.auto-resize').forEach(textarea => {
                autoResize(textarea);
            });
        });
        document.querySelectorAll('.auto-resize').forEach(textarea => autoResize(textarea));
        $("#message").highlightWithinTextarea({
            highlight: [{
                highlight: $(".info-message").attr('data-message'),
                className: 'draw-color'
            }]
        });
        autosize($('textarea'));
        $('textarea').css("overflow-y", "scroll")
        $(function() {
            var table = $('#table').DataTable({
                processing: true,
                serverSide: true,
                pageLength: 10,
                paging: true,
                lengthChange: true,
                // responsive: true,
                pagingType: 'simple_numbers',
                order: [
                    [1, 'asc']
                ],
                language: {
                    paginate: {
                        previous: '<i class="fas fa-angle-left" style="font-size:20px"></i>',
                        next: '<i class="fas fa-angle-right" style="font-size:20px"></i>',
                    },
                    lengthMenu: "Hiển thị  _MENU_",
                    // info: " _PAGE_/_PAGES_",
                },
                dom:
                    //"<'overflow-auto d-flex'<'table-flex'f>>" +
                    "<'d-flex items-center mb-3 overflow-auto'<'col-sm-12 p-0'tr>>" +
                    "<'d-flex items-center flex-row-reverse justify-content-between flex-wrap'<'table-flex mb-2'p><'table-flex'l>>",

                // lengthMenu: [10, 25, 50, 100, 150],

                ajax: {
                    url: " {!! url('/api/statistic/detail') . '/' . $customer_id !!}?date_start={{ $date_start }}&date_end={{ $date_end }}&region_id={{ $region_id }}&type_view={{ request()->type_view }}",

                },
                "drawCallback": function(settings) {

                },
                columns: [{
                        data: 'action',
                        render: function(data, type, row, meta) {
                            return meta.row + meta.settings._iDisplayStart + 1;
                        }
                    },
                    // {data: 'action', name: 'action'},
                    {
                        data: 'created_at',
                        name: 'created_at'
                    },

                    {
                        data: 'message_show',
                        "render": function(data, type, row, meta) {
                            if (data == null) {
                                return "";
                            }
                            let arrayString = data.split("\r\n\r\n")

                            // console.log(arrayString.find(x => x.includes("Số trúng")))
                            let getLastIndexArr = null
                            let getLastIndexArr2nd = null
                            let htmlString = ''
                            if (arrayString.find(x => x.includes("Số trúng"))) {

                                htmlString = ''


                                if (arrayString.length == 1) {

                                    htmlString = arrayString[0].replace(/\r\n/g, "<br>")
                                } else {
                                    getLastIndexArr = arrayString[arrayString.length - 1];
                                    getLastIndexArr2nd = arrayString[arrayString.length - 2];
                                    arrayString = arrayString.filter(e => e !== getLastIndexArr &&
                                        e !== getLastIndexArr2nd)
                                    getLastIndexArr2nd = getLastIndexArr2nd.replace(/\r\n/g, "<br>")

                                    getLastIndexArr = getLastIndexArr.replace(/\r\n/g, "<br>")
                                    arrayString.forEach(function(item) {
                                        item.replace(/\r\n/g, "<br>")
                                        htmlString += '<p style="white-space: pre-line">' +
                                            item + '</p>';


                                    })
                                }
                            } else {

                                htmlString = ''

                                if (arrayString.length == 1) {
                                    htmlString = arrayString[0].replace(/\r\n/g, "<br>")
                                } else {
                                    getLastIndexArr2nd = arrayString[arrayString.length - 1];
                                    arrayString = arrayString.filter(e => e !== getLastIndexArr2nd)
                                    getLastIndexArr2nd = getLastIndexArr2nd.replace("\r\n", "<br>")
                                    arrayString.forEach(function(item) {
                                        item.replace(/\r\n/g, "<br>")
                                        htmlString += '<p style="white-space: pre-line">' +
                                            item + '</p>';


                                    })
                                }

                            }
                            var txt = document.createElement("textarea");
                            txt.innerHTML = htmlString;
                            htmlString = txt.value
                            return ` <div>${htmlString}</div>
                                        <span class="text-danger ">
                                           ${getLastIndexArr2nd ? getLastIndexArr2nd : ''}<br>
                                        </span>
                                        <span class="text-primary">
                                           ${getLastIndexArr ? getLastIndexArr : ""}
                                        </span>
                                        <span class="text-success"><b>TC: </b></span><span class="${row.tong_tien>=0?'text-primary':'text-danger'}">${row.tong_tien}</span>`
                        },
                    },
                    {
                        data: 'tien_xac',
                        name: 'tien_xac'
                    },
                    {
                        data: 'tien_trung',
                        name: 'tien_trung'
                    },
                    {
                        data: 'tong_tien',
                        render: function(data, type, row, meta) {

                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },
                    {
                        data: 'tien_xac_2nd',
                        name: 'tien_xac_2nd'
                    },
                    {
                        data: 'tien_trung_2nd',
                        name: 'tien_trung_2nd'
                    },
                    {
                        data: 'tong_tien_2nd',
                        render: function(data, type, row, meta) {

                            if (data < 0) {
                                return '<span style="color: red">' + data + '</span>';
                            }
                            return data

                        }
                    },


                ]
            });



        })
        

        function extractNumbers(input) {
            // Biểu thức chính quy để khớp với hai số
            const regex = /(-?[\d,]+(?:\.\d+)?)\s\((-?[\d,]+(?:\.\d+)?)\)/;

            // Áp dụng biểu thức chính quy để khớp với chuỗi đầu vào
            const match = input.match(regex);
            if (match) {
                // Trích xuất và chuyển đổi các số từ chuỗi thành số thực
                const outerNumber = parseFloat(match[1].replace(/,/g, ''));
                const innerNumber = parseFloat(match[2].replace(/,/g, ''));
                return [outerNumber, innerNumber];
            } else {
                return null;
            }
        }

        function formatNumbers(outerNumber, innerNumber) {
            function formatNumber(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }

            const formattedOuterNumber = formatNumber(outerNumber);
            const formattedInnerNumber = formatNumber(innerNumber);

            return `${formattedOuterNumber} (${formattedInnerNumber})`;
        }
        function formatNumbers2(outerNumber) {
            function formatNumber(num) {
                return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
            }

            return formatNumber(outerNumber);
            
        }

        function hidden_row() {
            const tableBody = document.querySelector('#table2 tbody');
            const rows = tableBody.querySelectorAll('tr');

            rows.forEach(row => {
                if (!row.classList.contains('hidden')) {
                    row.classList.add('hidden');
                }
            });
        }

        function plus_1(selected, total) {
            let seleted_tds = selected.querySelectorAll('td');
            let total_tds = total.querySelectorAll('td');
            let [selected1, selected2] = extractNumbers(seleted_tds[1].textContent.replace(/\s+/g, ' ').trim());
            let [total1, total2] = extractNumbers(total_tds[1].textContent.replace(/\s+/g, ' ').trim());
            total_tds[1].textContent = formatNumbers(total1+selected1,total2+selected2)
            
            let [selected3, selected4] = extractNumbers(seleted_tds[2].textContent.replace(/\s+/g, ' ').trim());
            let [total3, total4] = extractNumbers(total_tds[2].textContent.replace(/\s+/g, ' ').trim());
            total_tds[2].textContent = formatNumbers(total3+selected3,total4+selected4)
            let [selected5, selected6] = extractNumbers(total_tds[1].textContent.replace(/\s+/g, ' ').trim());
            let [total5, total6] = extractNumbers(total_tds[2].textContent.replace(/\s+/g, ' ').trim());
            total_tds[3].className =(total6+selected6>=0)?'text-primary':'text-danger' ;

            total_tds[3].textContent = formatNumbers2(total6+selected6)
        }

        function minus_1(selected, total) {
            let seleted_tds = selected.querySelectorAll('td');
            let total_tds = total.querySelectorAll('td');
            let [selected1, selected2] = extractNumbers(seleted_tds[1].textContent.replace(/\s+/g, ' ').trim());
            let [total1, total2] = extractNumbers(total_tds[1].textContent.replace(/\s+/g, ' ').trim());
            total_tds[1].textContent = formatNumbers(total1-selected1,total2-selected2)
            
            let [selected3, selected4] = extractNumbers(seleted_tds[2].textContent.replace(/\s+/g, ' ').trim());
            let [total3, total4] = extractNumbers(total_tds[2].textContent.replace(/\s+/g, ' ').trim());
            total_tds[2].textContent = formatNumbers(total3-selected3,total4-selected4)
            let [selected5, selected6] = extractNumbers(total_tds[1].textContent.replace(/\s+/g, ' ').trim());
            let [total5, total6] = extractNumbers(total_tds[2].textContent.replace(/\s+/g, ' ').trim());
            total_tds[3].className =(total6+selected6>=0)?'text-primary':'text-danger' ;

            total_tds[3].textContent = formatNumbers2(total6+selected6)
        }
        let count =0;
        function handle(id) {
            const table2 = document.getElementById('table2');
            const selected = document.getElementById(id);
            
            const total = document.getElementById('table2_thuchi');
            
            if (selected.classList.contains('hidden')) {
                selected.classList.remove('hidden');
                plus_1(selected, total)
                count+=1;
                
            } else {
                selected.classList.add('hidden');
                minus_1(selected, total)
                count-=1;
            }
            if(count>0&&table2.classList.contains('hidden')){
                table2.classList.remove('hidden');
            }else if(count==0&&!table2.classList.contains('hidden')){
                table2.classList.add('hidden');
            }
        }

        function handle_change(id1, id2, id3, id4, id5, id6, id7) {
            var hai_so_percent = document.getElementById(id1);
            let new_hai_so_percent = hai_so_percent.value;

            var hai_so_danh = document.getElementById(id2);
            let hai_so_danh_content = hai_so_danh.textContent.replace(/\s+/g, ' ').trim();
            let hai_so_danh_percent_new, hai_so_danh_percent_old;
            const regex = /(-?[\d,]+(?:\.\d+)?)\s\((-?[\d,]+(?:\.\d+)?)\)/;
            let match = hai_so_danh_content.match(regex);
            if (match) {
                const outerNumberStr = match[1];
                const innerNumberStr = match[2];
                hai_so_danh_percent_old = parseFloat(innerNumberStr.replace(/,/g, ''));
                const outerNumber = parseFloat(outerNumberStr.replace(/,/g, ''));
                hai_so_danh_percent_new = outerNumber * new_hai_so_percent;
                const formattedInnerNumber = hai_so_danh_percent_new.toLocaleString('en-US', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                });
                const lastIndex = hai_so_danh_content.lastIndexOf(innerNumberStr);
                let newTextContent;
                if (lastIndex !== -1) {
                    const before = hai_so_danh_content.substring(0, lastIndex);
                    const after = hai_so_danh_content.substring(lastIndex + innerNumberStr.length);
                    newTextContent = before + formattedInnerNumber + after;
                }
                hai_so_danh.textContent = newTextContent;
            }
            var hai_so_result = document.getElementById(id3);
            let hai_so_result_content = hai_so_result.textContent.trim();
            let hai_so_result_new = parseFloat(hai_so_result_content.replace(/,/g, '')) - hai_so_danh_percent_old +
                hai_so_danh_percent_new;
            let formattedInnerNumber = hai_so_result_new.toLocaleString('en-US', {
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            });
            hai_so_result.textContent = formattedInnerNumber;
            hai_so_result.className = hai_so_result_new < 0 ? 'text-danger' : 'text-primary';

            try {
                var tong_hai_so_danh = document.getElementById(id4);
                let tong_hai_so_danh_content = tong_hai_so_danh.textContent.trim();
                let tong_hai_so_danh_percent_new, tong_hai_so_danh_percent_old;
                match = tong_hai_so_danh_content.match(regex);
                if (match) {
                    const outerNumberStr = match[1];
                    const innerNumberStr = match[2];
                    tong_hai_so_danh_percent_old = parseFloat(innerNumberStr.replace(/,/g, ''));
                    const outerNumber = parseFloat(outerNumberStr.replace(/,/g, ''));
                    tong_hai_so_danh_percent_new = tong_hai_so_danh_percent_old - hai_so_danh_percent_old +
                        hai_so_danh_percent_new;
                    const formattedInnerNumber = tong_hai_so_danh_percent_new.toLocaleString('en-US', {
                        minimumFractionDigits: 1,
                        maximumFractionDigits: 1
                    });
                    const lastIndex = tong_hai_so_danh_content.lastIndexOf(innerNumberStr);

                    let newTextContent;
                    if (lastIndex !== -1) {
                        const before = tong_hai_so_danh_content.substring(0, lastIndex);
                        const after = tong_hai_so_danh_content.substring(lastIndex + innerNumberStr.length);

                        newTextContent = before + formattedInnerNumber + after;
                    }
                    tong_hai_so_danh.textContent = newTextContent;

                }

                var tong_hai_so_result = document.getElementById(id5);
                let tong_hai_so_result_content = tong_hai_so_result.textContent.trim();
                let tong_hai_so_result_new = parseFloat(tong_hai_so_result_content.replace(/,/g, '')) -
                    hai_so_danh_percent_old + hai_so_danh_percent_new;
                formattedInnerNumber = tong_hai_so_result_new.toLocaleString('en-US', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                });
                tong_hai_so_result.textContent = formattedInnerNumber;
                tong_hai_so_result.className = tong_hai_so_result_new < 0 ? 'text-danger' : 'text-primary';
            } catch (error) {

            }


            var total_danh = document.getElementById(id6);
            let total_danh_content = total_danh.textContent.trim();
            let total_danh_percent_new, total_danh_percent_old;
            match = total_danh_content.match(regex);
            if (match) {
                const outerNumberStr = match[1];
                const innerNumberStr = match[2];
                total_danh_percent_old = parseFloat(innerNumberStr.replace(/,/g, ''));
                const outerNumber = parseFloat(outerNumberStr.replace(/,/g, ''));
                total_danh_percent_new = total_danh_percent_old - hai_so_danh_percent_old + hai_so_danh_percent_new;
                const formattedInnerNumber = total_danh_percent_new.toLocaleString('en-US', {
                    minimumFractionDigits: 1,
                    maximumFractionDigits: 1
                });
                const lastIndex = total_danh_content.lastIndexOf(innerNumberStr);
                let newTextContent;
                if (lastIndex !== -1) {
                    const before = total_danh_content.substring(0, lastIndex);
                    const after = total_danh_content.substring(lastIndex + innerNumberStr.length);
                    newTextContent = before + formattedInnerNumber + after;
                }
                total_danh.textContent = newTextContent;
            }
            var total_result = document.getElementById(id7);
            let total_result_content = total_result.textContent.trim();
            let total_result_new = parseFloat(total_result_content.replace(/,/g, '')) - hai_so_danh_percent_old +
                hai_so_danh_percent_new;
            formattedInnerNumber = total_result_new.toLocaleString('en-US', {
                minimumFractionDigits: 1,
                maximumFractionDigits: 1
            });
            total_result.textContent = formattedInnerNumber;
            total_result.className = total_result_new < 0 ? 'text-danger' : 'text-primary';
        }
    </script>
@endpush
