@extends('layout.master')
@section('content')
    @push("head")
        <link href="{{asset('js/dist/datepicker.css')}}" rel="stylesheet">

    @endpush


    <div class="container-fluid">
        <div class="col-md-12">
            <div class="portlet light portlet-fit portlet-datatable bordered md:mx-4">
                <div class="portlet-title">
                    <ul class="nav nav-tabs nav-justified tab-limit">
                        <li class="active"><a class="tab-home active" data-toggle="tab" href="#menu1"
                                              aria-expanded="true">Kế toán</a></li>
                        <li class=""><a class="tab-home" href="#menu2" data-toggle="tab" aria-expanded="false">Báo
                                cáo</a></li>


                            <li class=""><a class="tab-home" href="#menu3" data-toggle="tab" aria-expanded="false">Th<PERSON><PERSON>
                                    viên</a></li>

                    </ul>
                </div>
                <div class="portlet-body">
                    <div class="tab-content">
                        <div id="menu1" class="tab-pane  active ">

                            <div class="row">
                                <div class="container w-full margin-bottom-20" style="text-align: center">
                                    <div class="row items-center justify-center mb-2">
                                        <button class="m-2 btn-day" onclick="setYesterday(this)">
                                            <i class="fa fa-calendar-alt"></i> Hôm qua
                                        </button>
                                        <button class="m-2 btn-day active" onclick="setToday(this)">
                                            <i class="fa fa-calendar-alt"></i> Hôm nay
                                        </button>
                                        <button class="m-2 btn-day" onclick="setThisWeek(this)">
                                            <i class="fa fa-calendar-alt"></i> Tuần này
                                        </button>
                                        <button class="m-2 btn-day" onclick="setLastWeek(this)">
                                            <i class="fa fa-calendar-alt"></i> Tuần trước
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-center items-center">
                                <form class="col-md-6"
                                      action="{{route('statistic.store')}}"

                                      method="POST">
                                    @csrf
                                    <div class="text-main my-1">Từ ngày</div>
                                    <div id="datepicker_from" class="input-group date date-picker margin-bottom-10"
                                         data-date-language="vi" data-date-format="dd/mm/yyyy"
                                         data-date-today-highlight="true">
                                        <input class="form-control" readonly="readonly" type="text"
                                               value="{{date('d/m/Y')}}"
                                               data-toggle="datepicker"
                                               name="from_date" id="accounting_report_from_date">
                                        <span class="help-block help-block-error"></span>
                                    </div>
                                    <div class="text-main my-1">Đến ngày</div>
                                    <div id="datepicker_to" class="input-group date date-picker margin-bottom-10"
                                         data-date-language="vi" data-date-format="dd/mm/yyyy"
                                         data-date-today-highlight="true">
                                        <input class="form-control" readonly="readonly" type="text"
                                               data-toggle="datepicker"
                                               name="to_date" id="accounting_report_to_date"
                                               value="{{date('d/m/Y')}}">
                                        <span class="help-block help-block-error"></span>
                                    </div>

                                    <div class="col text-center mt-4 btn-ketoan">
                                        <button id="btn_scan" style="width: 150px"
                                                type="button" onclick="scan()"
                                                class="btn btn-color-main text-white btn-quet margin-bottom-10">
                                            <i class="fa fa-floppy-o"></i>Quét
                                        </button>

                                        <button style="width:150px" type="submit"
                                                class="btn btn-baocao margin-bottom-10">
                                            <i class="fa fa-floppy-o"></i>Tạo báo cáo
                                        </button>
                                    </div>

                                </form>
                            </div>
                            <div class="table-responsive mt-4">
                            </div>
                        </div>
                        <div id="menu2" class="tab-pane fade">
                            <h3 class="font-bold mb-4 font-title uppercase">Báo cáo</h3>
                            <div class="table-responsive" style="overflow: auto">
                                <table class="table table-bordered text-nowrap">
                                    <tbody>
                                    <tr>
                                        <th>Tên</th>
                                        <th>Ngày lập</th>
                                        <th colspan="2"></th>
                                        <th colspan="2" class="text-center">Chức năng</th>

                                    </tr>
                                    </tbody>
                                    <tbody>
                                    @foreach($statistics as $statistic)

                                        <tr style="cursor: pointer;"
                                            onclick="redirect('{{route('statistic.show',$statistic->id)}}')">

                                            <td>{{$statistic->name}}</td>
                                            <td>{{ $statistic->created_at }}</td>
                                            <td colspan="2"></td>
                                            <td class="table-td-center text-center">
                                                <form action="{{route('reports.deleteStatistic',$statistic)}}"
                                                      method="post">
                                                    @method('DELETE')
                                                    @csrf
                                                    <button style="color:white;"
                                                            type="submit"
                                                            class="btn btn-danger btn-icon-only  custom-button"
                                                            title="Xoá"><i
                                                            class="fas fa-trash-alt"></i>
                                                    </button>
                                                </form>
                                                <br>

                                            </td>
                                        </tr>

                                    @endforeach
                                    </tbody>

                                </table>
                            </div>
                        </div>
                        <div id="menu3" class="tab-pane fade">
                            <h3 class="font-bold mb-4 font-title uppercase">Thành viên</h3>
                            <div class="mb-3 d-flex justify-content-end">
                                <a href="{{route('shareholder.create')}}" class="btn btn-color-main text-white">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                    Thêm cổ đông
                                </a>
                            </div>
                            <div class="table-responsive" style="overflow: auto">
                                <table class="table table-bordered text-nowrap"
                                       style="width:100%" id="table-stock-select">
                                    <tbody>
                                    <tr>

                                        <th>Tên</th>

                                        <th>Trang</th>
                                        <th>Giao/Nhận</th>
                                        <th>Phần trăm</th>
                                        <th></th>
                                        <th class="text-center">Chức năng</th>
                                        <th></th>

                                    </tr>
                                    </tbody>
                                    <tbody>
                                    @foreach($shareholders as  $value)
                                        <tr>
                                            <td style="cursor: pointer" data-toggle="collapse"
                                                href="#collapseExample{{$value->id}}" aria-expanded="false"
                                                aria-controls="collapseExample"><i class="fa fa-caret-down"
                                                                                   aria-hidden="true"></i>{{$value->name }}
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td class="table-td-center text-center">
                                                <div class="w-full d-flex align-items-center justify-content-center">
                                                    <a href="{{route('shareholder.edit',$value->id)}}">
                                                        <button type="button"
                                                                class="btn btn-xs btn-warning warning-edit text-white mr-2"
                                                                title="Sửa"><i
                                                                class="fa fa-edit"></i></button>
                                                    </a>
                                                    <form action="{{route('shareholder.destroy',$value->id)}}"
                                                          method="post">
                                                        @method('DELETE')
                                                        @csrf
                                                        <button style="color:white;"
                                                                type="submit"
                                                                class="btn btn-danger btn-icon-only  custom-button"
                                                                title="Xoá"><i
                                                                class="fas fa-trash-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                            <td></td>
                                        @foreach($value->children as $item)
                                            <tr class="collapse" id="collapseExample{{$value->id}}">
                                                <td></td>
                                                <td>{{$item->username}}</td>
                                                <td @class([
                                               ' text-danger' => $item->is_send==1,
                                               'text-success' => $item->is_send==0,
                                                       ])
                                                >{{$item->is_send==0 ? "Giao": "Nhận"}}</td>
                                                <td>{{$item->percent}}</td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            @endforeach
                                            </tr>
                                        @endforeach
                                    </tbody>

                                </table>
                            </div>

                        </div>
                        @if(!auth()->guard('shareholder')->check())

                            <div id="menu4" class="tab-pane fade">
                                <h3 class="font-bold mb-4 font-title uppercase">Kế toán viên</h3>
                                <div class="mb-3 d-flex justify-content-end">
                                    <button
                                        type="button" class="btn btn-color-main text-white"
                                        data-toggle="modal" data-target="#ModelAccountantCreate">
                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                        Thêm kế toán viên
                                    </button>
                                </div>
                                <div class="table-responsive" style="overflow: auto">
                                    <table class="table dataTable no-footer text-sm collapsed"
                                           style="width:100%" id="table-stock-select">
                                        <tbody>
                                        <tr>
                                            <th>ID</th>
                                            <th>Tên</th>
                                            <th></th>
                                            <th></th>
                                            <th class="text-center">Chức năng</th>
                                        </tr>
                                        </tbody>
                                        <tbody>

                                        @foreach($accountants as  $item)
                                            <tr>

                                                <td>{{$item->id}}</td>
                                                <td>{{$item->name}}</td>
                                                <td>
                                                    <input type="hidden" value="{{$item->username}}">
                                                </td>
                                                <td></td>
                                                <td class="d-flex align-items-center justify-content-center">
                                                    <button type="button"
                                                            style="padding: 6px 10px;"
                                                            onclick="updateAccountant(this,{{$item->id}})"
                                                            data-toggle="modal" data-target="#ModelAccountantUpdate"
                                                            class="btn btn-warning btn-icon-only mt-1 m-2" title="Sửa">
                                                        <i
                                                            class="fa fa-edit "></i>
                                                    </button>
                                                    <button type="button"
                                                            onclick="deleteAccountant(this,{{$item->id}})"
                                                            data-toggle="modal" data-target="#ModelAccountantDelete"
                                                            class="btn btn-xs btn-danger btn-delete mt-1 m-2"
                                                            title="Xóa">
                                                        <i
                                                            class="fa fa-trash-alt"></i>
                                                    </button>

                                                    <button style="color:white;" type="button"
                                                            class="btn btn-edit btn-icon-only mt-1 m-2" title="Sửa"><i
                                                            class="fa fa-sync"></i></button>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>

                                    </table>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

            </div>
        </div>

        <div class="clearfix"></div>
    </div>
    @if(!auth()->guard('shareholder')->check())
        <div class="modal fade text-left r-0" id="ModelAccountantUpdate" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-custom my-0 modal-md" role="document">
                <div class="modal-content">
                    <div class="d-flex items-center justify-between p-3 border-bottom-main">
                        <h5 class="font-title font-bold">Cập nhật kế toán viên</h5>
                        <button data-toggle="modal" data-target="#ModelAccountantUpdate" type="button"
                                class="bg-transparent rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center close-modal"
                                data-modal-hide="ModelAccountantUpdate">
                            <i class="fas fa-times" aria-hidden="true"></i>
                            <span class="sr-only">Đóng</span>
                        </button>
                    </div>
                    <div class="p-3">
                        <div class="form-group">
                            <input type="text" name="name_accountant_u" id="name_accountant_u" class="form-control"
                                   placeholder="Tên hiển thị">
                        </div>
                        <input type="hidden" id="accountant_id_u">
                        <div class="form-group">
                            <label class="text-main" for="">Tài khoản đăng nhập</label>
                            <input type="text" name="username_accountant_u" id="username_accountant_u"
                                   class="form-control"

                                   readonly>
                        </div>

                        <div class="form-group">
                            <input type="password" name="password_accountant_u" id="password_accountant_u"
                                   class="form-control"
                                   placeholder="Mật khẩu">
                        </div>
                        <div class="d-flex justify-center p-3 pt-0" style="gap:10px;">

                            <div data-toggle="modal"
                                 {{--                             data-target="#ModalDetail" --}}
                                 class="  pt-3">
                                <button style="padding: 4px 15px;" id="update-submit-accountant"
                                        class="btn btn-color-main text-white font-bold"
                                        type="button"

                                >Cập nhật kế toán viên
                                </button>
                            </div>
                            <div data-toggle="modal" data-target="#ModelAccountantUpdate" class="  pt-3">
                                <button style="padding: 4px 15px;"
                                        class="btn btn-dark text-white font-bold"
                                        type="button"
                                >Đóng
                                </button>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>

        <div class="modal fade text-left r-0" id="ModelAccountantCreate" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-custom my-0 modal-md" role="document">
                <form method="post" class="w-full" action="{{route('admin.accountant.store')}}">
                    <div class="modal-content">
                        <div class="d-flex items-center justify-between p-3 border-bottom-main">
                            <h5 class="font-title font-bold">Thêm kế toán viên</h5>
                            <button data-toggle="modal" data-target="#ModelAccountantCreate" type="button"
                                    class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal"
                                    data-modal-hide="ModalUpdate">
                                <i class="fas fa-times" aria-hidden="true"></i>
                                <span class="sr-only">Đóng</span>
                            </button>
                        </div>
                        @csrf
                        <div class="p-3">
                            <div class="form-group">
                                <input type="text" name="name_accountant" id="name_accountant" class="form-control"
                                       placeholder="Tên hiển thị">
                            </div>
                            <div class="form-group">
                                <label class="text-main" for="">Tài khoản đăng nhập</label>
                                <input type="text" name="username_accountant" id="username_accountant"
                                       class="form-control"
                                       value="{{$username_accountant}}"
                                       readonly>
                            </div>

                            <div class="form-group">
                                <input type="password" name="password_accountant" id="password_accountant"
                                       class="form-control"
                                       placeholder="Mật khẩu">
                            </div>
                            <div class="d-flex justify-center p-3 pt-0" style="gap:10px;">

                                <div data-toggle="modal"
                                     {{--                             data-target="#ModalDetail" --}}
                                     class="  pt-3">
                                    <button style="padding: 4px 15px;"
                                            class="btn btn-color-main text-white font-bold"
                                            type="submit"

                                    >Thêm kế toán viên
                                    </button>
                                </div>
                                <div data-toggle="modal" data-target="#ModelAccountantCreate" class="  pt-3">
                                    <button style="padding: 4px 15px;"
                                            class="btn btn-dark text-white font-bold"
                                            type="button"
                                    >Đóng
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <div class="modal fade text-left r-0 l-0" id="ModelAccountantDelete" tabindex="-1" role="dialog"
             aria-hidden="true">
            <div class="modal-dialog modal-custom my-0" role="document">
                <div class="modal-content mx-3">
                    <div class="d-flex items-center justify-between p-3 border-bottom-main">
                        <h5 class="font-title font-bold mb-0">Bảng xác nhận</h5>
                        <button data-toggle="modal" data-target="#ModelAccountantDelete" type="button"
                                class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal"
                                data-modal-hide="ModelAccountantDelete">
                            <i class="fas fa-times" aria-hidden="true"></i>
                            <span class="sr-only">Đóng</span>
                        </button>
                    </div>
                    <div class="mt-4 p-3 text-center" style="font-size:15px; color: #111">
                        <i class="fa fa-trash-alt text-danger mb-2" style="font-size: 50px;"></i>
                        <p class="font-semibold mt-2">Bạn có chắc muốn xóa kế toán viên này không ?</p>
                    </div>
                    <input type="hidden" id="accountant_id_delete">
                    <div class="d-flex justify-center pb-3 mb-1" style="gap:10px;">
                        <div class=" ">
                            <button id="delete-submit-accountant"
                                    class="btn text-white font-bold bg-danger" style="width: 100px;"
                                    type="button"
                            >Xóa
                            </button>
                        </div>
                        <div data-toggle="modal" data-target="#ModelAccountantDelete" class="">
                            <button class="btn btn-dark text-white font-bold"
                                    style="width: 100px;"
                                    type="submit"
                            >Hủy
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="modal fade text-left r-0" id="ModalUpdate" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0 modal-lg" role="document">
            <form method="post" class="w-full">
                <div class="modal-content">
                    <div class="d-flex items-center justify-between p-3 border-bottom-main">
                        <h5 class="font-title font-bold">Sửa cổ đông</h5>
                        <button data-toggle="modal" data-target="#ModalUpdate" type="button"
                                class="bg-transparent rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center close-modal"
                                data-modal-hide="ModalUpdate">
                            <i class="fas fa-times" aria-hidden="true"></i>
                            <span class="sr-only">Đóng</span>
                        </button>
                    </div>
                    <div class="p-3">
                        <div class="form-group">
                            <input type="text" name="username" id="username_u" class="form-control"
                                   disabled>
                        </div>
                        <input type="hidden" id="customer_id_u" name="customer_id">
                        <input type="hidden" id="money_u" name="money">
                        <input type="hidden" id="id_u" name="id">
                        <input type="hidden" id="id_parent" name="id_parent">
                        <input type="hidden" id="shareholder_id_u" name="id">
                        <input type="hidden" id="type_machine_update" name="type_machine_update" value="0">
                        <div class="row">
                            <div class="col-lg-6">
                                <label class="label-message text-main">Phần trăm</label>
                                <input type="number" id="percent_u" name="percent" class="form-control" value="1"
                                       step="any"
                                       min="0" max="100">
                                <span class="error-percent_u text-danger"></span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="label-message text-main mb-0">Giao/nhận: Thành tiền = tổng tiền * phần
                                trăm</label>
                            <div class="">
                                <input class="" type="checkbox" id="is_send_u" name="is_send" value="0"/>
                                <label for="" id="is_send_u_label">Giao</label>
                            </div>
                        </div>
                        <div class="d-flex justify-center p-3 pt-0" style="gap:10px;">

                            <div data-toggle="modal"
                                 {{--                             data-target="#ModalDetail" --}}
                                 class="  pt-3">
                                <button
                                    style="padding: 4px 15px;" id="update-submit"
                                    class="btn btn-color-main text-white font-bold"
                                    type="button"

                                >Thêm cổ đông
                                </button>
                            </div>
                            <div data-toggle="modal" data-target="#ModalUpdate" class="  pt-3">
                                <button style="padding: 4px 15px;" id="form-transaction-submit-btn"
                                        class="btn btn-dark text-white font-bold"
                                        type="button"
                                >Đóng
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal fade text-left r-0" id="ModalDetail" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0 modal-lg" role="document">
            <form id="form-data" class="w-full" method="post">

                <div class="modal-content">
                    <div class="d-flex items-center justify-between p-3 border-bottom-main">
                        <h5 class="font-title font-bold">Thêm cổ đông</h5>
                        <button data-toggle="modal" data-target="#ModalDetail" type="button"
                                class="bg-transparent rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center close-modal"
                                data-modal-hide="ModalDetail">
                            <i class="fas fa-times" aria-hidden="true"></i>
                            <span class="sr-only">Đóng</span>
                        </button>
                    </div>
                    <div class="p-3">
                        <div id="select_shareholder">
                            <div class="form-group">
                                <label class="label-message text-main">Cổ đông</label>
                                <select name="shareholder_id" id="shareholder_id" class="form-control px-2 py-0">
                                    <option value="">Lựa cổ đông</option>
                                    @foreach($shareholders as $shareholder)
                                        <option value="{{$shareholder->id}}">{{$shareholder->name}}</option>
                                    @endforeach
                                </select>
                                <span class="error-holder text-danger"></span>
                            </div>
                        </div>
                        <div id="create_shareholder" class="d-none">
                            <div class="form-group">
                                <label class="label-message text-main">Cổ đông</label>
                                <input type="text" name="name" id="name" class="form-control" placeholder="Tên cổ đông">
                                <span class="error-name text-danger"></span>
                                <input type="text" name="username" id="username" value="{{$username}}"
                                       class="form-control mt-2"
                                       disabled>
                            </div>
                            <div class="form-group px-0 col-lg-4 col-md-6 col-12 d-flex items-center mt-2">
                                <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main mb-0">Tự xem
                                    sổ:</label>
                                <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch checker">
                                    <input
                                        class="custom-control-input"
                                        type="checkbox" onclick="handleCheckBoxPass()" id="is_password"
                                    >
                                    <label class="custom-control-label" for="is_password"></label>
                                </div>
                            </div>
                            <div class="form-group " id="field_password" style="display:none">

                                <input type="password" name="password" id="password" class="form-control"
                                       placeholder="Mật khẩu">
                                <span class="error-password text-danger"></span>

                            </div>
                        </div>
                        <input type="hidden" id="customer_id" name="customer_id">
                        <input type="hidden" id="type_machine_add" name="type_machine_add" value="0">
                        <input type="hidden" id="money" name="money">
                        <input type="hidden" id="id" name="id">
                        <div class="row">
                            <div class="col-lg-6">
                                <label class="label-message text-main">Phần trăm</label>
                                <input type="number" id="percent" name="percent" class="form-control" value="1"
                                       step="any"
                                       min="0" max="100">
                                <span class="error-percent text-danger"></span>
                            </div>
                            <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center mt-2">
                                <label
                                    class="label-message control-label col-lg-11 col-md-11 col-11 mb-0 p-0 text-main">Tạo
                                    cổ đông mới</label>
                                <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch checker">
                                    <input
                                        class="custom-control-input"
                                        type="checkbox" id="new_shareholder" name="new_shareholder" value="0"
                                        onclick="toggleNewShareholder()"
                                    >
                                    <label class="custom-control-label" for="new_shareholder"></label>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="label-message text-main mb-0">Giao/nhận: Thành tiền = tổng tiền * phần
                                trăm</label>
                            <div>
                                <label for="is_send" class="btn-giaonhan p4 mt-2">
                                    <input type="checkbox" id="is_send" name="is_send" value="0"/>
                                </label>
                            </div>
                        </div>
                        <div class="d-flex justify-center p-3 pt-0" style="gap:10px;">
                            <div data-toggle="modal"
                                 {{--                             data-target="#ModalDetail" --}}
                                 class="">
                                <button style="padding: 4px 15px;" id="create-submit"
                                        class="btn btn-color-main text-white font-bold"
                                        type="button"

                                >Thêm cổ đông
                                </button>
                            </div>
                            <div data-toggle="modal" data-target="#ModalDetail" class="">
                                <button style="padding: 4px 15px;" id="form-transaction-submit-btn"
                                        class="btn btn-dark text-white font-bold"
                                        type="button"
                                >Đóng
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal fade text-left r-0 l-0" id="ModalDelete" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0" role="document">
            <div class="modal-content mx-3">
                <div class="d-flex items-center justify-between p-3 border-bottom-main">
                    <h5 class="font-title font-bold mb-0">Bảng xác nhận</h5>
                    <button data-toggle="modal" data-target="#ModalDelete" type="button"
                            class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white close-modal"
                            data-modal-hide="ModalDelete">
                        <i class="fas fa-times" aria-hidden="true"></i>
                        <span class="sr-only">Đóng</span>
                    </button>
                </div>

                <div class="mt-4 p-3 text-center" style="font-size:15px; color: #111">
                    <i class="fa fa-trash-alt text-danger mb-2" style="font-size: 50px;"></i>
                    <p class="font-semibold mt-2">Bạn có chắc muốn xóa cổ đông này không ?</p>
                </div>
                <input type="hidden" id="customer_id_delete">
                <input type="hidden" id="type_machine_delete" value="0">
                <input type="hidden" id="shareholder_id_delete">
                <input type="hidden" id="id_parent_delete">
                <input type="hidden" id="id_delete">

                <div class="d-flex justify-center pb-3 mb-1" style="gap:10px;">
                    <div class="">
                        <button id="delete-submit"
                                class="btn text-white font-bold bg-danger" style="width: 100px;"
                                type="button"
                        >Xóa
                        </button>
                    </div>
                    <div data-toggle="modal" data-target="#ModalDelete" class=" ">
                        <button id="form-transaction-submit-btn"
                                class="btn btn-dark text-white font-bold"
                                style="width: 100px;"
                                type="submit"
                        >Hủy
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')

    <script src="{{asset('js/dist/datepicker.js')}}"></script>
    <script>

        $('#is_send_u').click(function () {
            // Xử lý sự kiện khi checkbox được click
            if ($(this).is(':checked')) {
                $("#is_send_u_label").text("Nhận")

            } else {
                $("#is_send_u_label").text("Giao")

            }
        });

        function handleCheckBoxPass() {
            var currentDisplay = $("#field_password").css("display");
            if (currentDisplay === "none") {
                $("#field_password").css("display", "block"); // You can use any other valid display value
            } else {
                $("#field_password").css("display", "none");
            }
        }

        function toggleNewShareholder() {
            if ($("#new_shareholder").is(":checked")) {
                $("#select_shareholder").addClass("d-none")
                $("#create_shareholder").removeClass("d-none")
            } else {
                $("#select_shareholder").removeClass("d-none")
                $("#create_shareholder").addClass("d-none")
            }
        }

        function redirect(link) {
            window.location.href = link;
        }

        $("#form-data").submit(function (e) {
            e.preventDefault();
        });
        $('[data-toggle="datepicker"]').datepicker({
            language: 'vi-VN',
            format: 'dd/mm/yyyy'
        });


        let canSubmit = true;
        let isValid = true;

        $("#update-submit").click(function () {
            isValid = true
            let customerId = $("#customer_id_u").val()
            let percent = $("#percent_u").val()
            let isSend = $("#is_send_u").is(":checked") ? 1 : 0

            let shareholderId = $("#shareholder_id_u").val()
            let money = $("#money_u").val()
            let id = $("#id_u").val()
            let id_parent = $("#id_parent").val()
            let machine_id = customerId
            let type_machine_update = $("#type_machine_update").val()
            if (percent > 100) {
                $(".error-percent_u").text("Phần trăm không được lớn hơn 100%")
                isValid = false

            }
            if (percent < 1) {
                $(".error-percent_u").text("Phần trăm không được nhỏ hơn 1%")
                isValid = false

            }

            if (canSubmit && isValid) {
                canSubmit = false
                $.ajax({
                    url: "{{route('api.shareholder.updateShareholderToReport')}}",
                    type: "post",
                    data: {
                        _token: "{{ csrf_token() }}",
                        customer_id: customerId,
                        percent: percent,
                        shareholder_id: shareholderId,
                        money: money,
                        is_send: isSend,
                        machine_id: machine_id,
                        type_machine_update: type_machine_update,

                    },
                    success: function (response) {
                        $(".error-percent_u").empty()
                        let find = ""
                        if (type_machine_update == 0) {
                            find = 'tr[data-id="' + id_parent + '"]'

                        } else {
                            find = 'tr[id="' + id_parent + '"]'

                        }
                        let select = $("#table-transactions").find(`${find}`)
                        select.find("td:nth-child(10)").find(`div:eq(${id - 1})`).replaceWith(`<div>${response.percent}</div`)

                        select.find("td:nth-child(11)").find(`div:eq(${id - 1})`).replaceWith(`<div class="${response.money < 0 ? "text-danger" : ""}">${formatNumber(response.money)}</div`)

                        $("#ModalUpdate").modal('hide')
                        $("#type_machine_update").val(0)
                        canSubmit = true
                    }
                });
            }
        })

        $("#create-submit").click(function () {
            isValid = true
            let customerId = $("#customer_id").val()
            let percent = $("#percent").val()
            let isSend = $("#is_send").is(":checked") ? 1 : 0
            let newShareholder = $("#new_shareholder").is(":checked") ? 1 : 0
            let shareholderId = $("#shareholder_id").val()
            let money = $("#money").val()
            let id = $("#id").val()
            let parentId = id.indexOf("1");
            let name = $("#name").val()
            let username = $("#username").val()
            let type_machine_add = $("#type_machine_add").val()
            let machine_id = customerId
            let password = $("#password").val()
            let isPassword = $("#is_password").is(":checked") ? 1 : 0
            if (percent > 100) {
                $(".error-percent").text("Phần trăm không được lớn hơn 100%")
                isValid = false

            }
            if (percent < 1) {
                $(".error-percent").text("Phần trăm không được nhỏ hơn 1%")
                isValid = false

            }
            if ($("#new_shareholder").is(":checked")) {
                if ($("#name").val() == "") {
                    $(".error-name").text("Tên không được để trống")
                    isValid = false

                }
                if (isPassword == 1) {
                    if ($("#password").val() == "") {
                        $(".password").text("Mật khẩu không được để trống")
                        isValid = false
                    }
                }
            }
            if (shareholderId == "" && !$("#new_shareholder").is(":checked")) {
                $(".error-holder").text("Tên không được để trống")
                isValid = false
            }
            if (canSubmit && isValid) {
                canSubmit = false
                $.ajax({
                    url: "{{route('api.shareholder.addToReportCustomer')}}",
                    type: "post",
                    data: {
                        _token: "{{ csrf_token() }}",
                        customer_id: customerId,
                        percent: percent,
                        is_send: isSend,
                        new_shareholder: newShareholder,
                        shareholder_id: shareholderId,
                        money: money,
                        name: name,
                        username: username,
                        machine_id: machine_id,
                        type_machine_add: type_machine_add,
                        password: password,

                    },
                    success: function (response) {
                        $(".error-name").val("")
                        $(".error-holder").val("")
                        $(".error-percent").val("")
                        $(".error-password").val("")
                        $("#name").val("")
                        $("#password").val("")
                        $("#percent").val(1)
                        $("#select_shareholder").removeClass("d-none")
                        $("#create_shareholder").addClass("d-none")
                        $("#new_shareholder").prop("checked", false)
                        $("#is_password").prop("checked", false)
                        var newNumber = parseInt(username.substr(-4)) + 1;
                        var resultString = username.replace(/\d+/, newNumber.toString().padStart(4, '0'));
                        $("#username").val(resultString)
                        let find = ""
                        let strBtnUpdate = ""
                        let strBtnDelete = ""
                        let select = ""
                        if (type_machine_add == 0) {

                            find = 'tr[data-id="' + id + '"]'
                            select = $("#table-transactions").find(`${find}`)
                            let childId = select.find("td:nth-child(9)").find('div').length + 1
                            strBtnUpdate = `<div>
                                                      <button class="btn btn-primary update-button"
                                                            style="padding: 2px 5px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                                        data-toggle="modal" data-target="#ModalUpdate"
                                        onclick="updateStock(this,${shareholderId},${money},${childId},${id})">
                                                        <i class="fa fa-edit"></i> <!-- Chỉnh sửa icon 'fa fa-edit' -->
                                                      </button>
                                                    </div>`
                            strBtnDelete = `
                                                    <div>
                                                      <button class="btn btn-danger ce delete-button"
                                                            style="padding: 2px 6px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                        data-toggle="modal" data-target="#ModalDelete"
                                            onclick="confirmDelete(this,${shareholderId},${childId},${id})">
                                                        <i class="fas fa-trash-alt"></i>
                                                      </button>
                                                    </div>
                                                  `;

                        } else {
                            find = 'tr[id="' + id + '"]'
                            select = $("#table-transactions").find(`${find}`)
                            let childId = select.find("td:nth-child(9)").find('div').length + 1
                            strBtnUpdate = `<div>
                                                      <button class="btn btn-primary update-button"
                                                            style="padding: 2px 5px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                                        data-toggle="modal" data-target="#ModalUpdate"
                                        onclick="updateStockParent(this,${shareholderId},${money},${childId},${id})">
                                                        <i class="fa fa-edit"></i> <!-- Chỉnh sửa icon 'fa fa-edit' -->
                                                      </button>
                                                    </div>`
                            strBtnDelete = `
                                                    <div>
                                                      <button class="btn btn-danger ce delete-button"
                                                            style="padding: 2px 6px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                        data-toggle="modal" data-target="#ModalDelete"
                                        onclick="confirmDeleteParent(this,${shareholderId},${childId},${id})">
                                                        <i class="fas fa-trash-alt"></i>
                                                      </button>
                                                    </div>
                                                  `;
                        }


                        select.find("td:nth-child(9)").append(`<div>${response.holder_name}</div>`)

                        select.find("td:nth-child(10)").append(`<div>${response.percent}</div>`)
                        select.find("td:nth-child(11)").append(`<div class="${response.money < 0 ? "text-danger" : ""}">${formatNumber(response.money)}</div>`)

                        select.find("td:nth-child(12)").append(strBtnUpdate)
                        select.find("td:nth-child(13)").append(strBtnDelete)

                        $("#ModalDetail").modal('hide')
                        $("#type_machine_add").val(0)

                        canSubmit = true
                    }
                });
            }

        })
        $('#ModalDetail').on('hidden.bs.modal', function () {
            $("#type_machine_update").val(0)
            $("#type_machine_add").val(0)
            $("#type_machine_delete").val(0)

        })

        function updateStock(element, shareholder_id, money, id, parentId) {

            let cusId = $(element).parent().parent().attr('class')
            $("#shareholder_id_u").val(shareholder_id)
            $("#money_u").val(money)
            $("#id_u").val(id)
            $("#id_parent").val(parentId)

            $("#customer_id_u").val(cusId)
            fetchToUpdateShareholder(cusId, shareholder_id)
        }

        function updateStockParent(element, shareholder_id, money, id, parentId) {

            let cusId = $(element).parent().parent().attr('class')
            $("#shareholder_id_u").val(shareholder_id)
            $("#type_machine_update").val(1)

            $("#customer_id_u").val(cusId)
            $("#id_u").val(id)
            $("#id_parent").val(parentId)
            $("#money_u").val(money)
            fetchToUpdateShareholderParent(cusId, shareholder_id)
        }

        function fetchToUpdateShareholder(customerId, shareholderId) {
            $.ajax({
                url: "{{route('api.shareholder.fetchToUpdateShareholder')}}",
                type: "get",
                data: {

                    shareholder_id: shareholderId,
                    customer_id: customerId,
                },
                success: function (response) {
                    console.log(response)
                    $("#percent_u").val(response.percent)
                    $("#username_u").val(response.shareholder.name)
                    $("#is_send_u").val(response.is_send)
                    if (response.is_send == 1) {
                        $('#is_send_u').prop('checked', true);
                    } else {
                        $('#is_send_u').prop('checked', false);

                    }

                    $("#is_send_u_label").text(response.is_send == 1 ? "Nhận" : "Giao")
                }
            });
        }

        function fetchToUpdateShareholderParent(customerId, shareholderId) {
            $.ajax({
                url: "{{route('api.shareholder.fetchToUpdateShareholderParent')}}",
                type: "get",
                data: {

                    shareholder_id: shareholderId,
                    customer_id: customerId,
                },
                success: function (response) {
                    console.log(response)


                    $("#percent_u").val(response.percent)
                    $("#username_u").val(response.shareholder.name)
                    $("#is_send_u").val(response.is_send)
                    $("#is_send_u_label").text(response.is_send == 1 ? "Nhận" : "Giao")
                    if (response.is_send == 1) {
                        $('#is_send_u').prop('checked', true);
                    } else {
                        $('#is_send_u').prop('checked', false);

                    }


                }
            });
        }

        function updateSelect(customerId) {
            $.ajax({
                url: "{{route('api.shareholder.filterShareholder')}}",
                type: "get",
                data: {
                    customer_id: customerId,
                },
                success: function (response) {
                    console.log(response)
                    let selectRender = $("#shareholder_id");
                    selectRender.empty()
                    selectRender.append(`<option value="">Lựa cổ đông</option>`)
                    response.forEach(function (e) {
                        selectRender.append(`<option value="${e.id}">${e.name}</option>`)
                    })
                }
            });
        }

        function updateSelectParent(customerId) {
            $.ajax({
                url: "{{route('api.shareholder.filterShareholderParent')}}",
                type: "get",
                data: {
                    customer_id: customerId,
                },
                success: function (response) {
                    console.log(response)
                    let selectRender = $("#shareholder_id");
                    selectRender.empty()
                    selectRender.append(`<option value="">Lựa cổ đông</option>`)
                    response.forEach(function (e) {
                        selectRender.append(`<option value="${e.id}">${e.name}</option>`)
                    })
                }
            });
        }

        function addStock(customer_id, money, id) {
            $("#customer_id").val(customer_id)
            $("#money").val(money)
            $("#id").val(id)

            updateSelect(customer_id)
        }

        function addStockParent(customer_id, money, id) {
            $("#customer_id").val(customer_id)
            $("#type_machine_add").val(1)
            $("#money").val(money)
            $("#id").val(id)

            updateSelectParent(customer_id)
        }

        let currentDate = new Date();

        var date_end = formatDate(currentDate, 0);

        var date_start = formatDate(currentDate, 0);

        let from_date = $("#accounting_report_from_date")
        let to_day = $("#accounting_report_to_date")
        from_date.change(function () {
            console.log(this.value)
            date_start = this.value
        })
        to_day.change(function () {
            date_end = this.value
        })


        function confirmDelete(element, shareholder_id, id, parent_id) {

            let cusId = $(element).parent().parent().attr('class')

            $("#customer_id_delete").val(cusId)

            $("#shareholder_id_delete").val(shareholder_id)
            $("#id_delete").val(id)
            $("#id_parent_delete").val(parent_id)

        }

        function confirmDeleteParent(element, shareholder_id, id, parent_id) {

            let cusId = $(element).parent().parent().attr('class')

            $("#customer_id_delete").val(cusId)
            $("#type_machine_delete").val(1)

            $("#shareholder_id_delete").val(shareholder_id)
            $("#id_delete").val(id)
            $("#id_parent_delete").val(parent_id)

        }

        let tempElementAccountant
        let tempElementAccountantUpdate

        function deleteAccountant(element, id) {
            tempElementAccountant = $(element).parent().parent();
            $("#accountant_id_delete").val(id)
        }

        function updateAccountant(element, id) {
            tempElementAccountantUpdate = $(element).parent().parent();
            console.log(tempElementAccountantUpdate.find("td:nth-child(2)").text())
            console.log(id)
            $("#accountant_id_u").val(id)
            $("#name_accountant_u").val(tempElementAccountantUpdate.find("td:nth-child(2)").text())
            $("#username_accountant_u").val(tempElementAccountantUpdate.find("td:nth-child(3) input").val())
            $("#password_accountant_u").val("")
        }

        $("#update-submit-accountant").click(function () {
            let accountantId = $("#accountant_id_u").val()
            let password = $("#password_accountant_u").val()
            let name = $("#name_accountant_u").val()
            if (password == null || password.trim() == "" || name == null || name.trim() == "") {
                $("#ModelAccountantUpdate").modal('hide')
                return false;
            }
            $.ajax({
                url: "{{route('admin.accountant.update')}}",
                type: "POST",
                data: {
                    accountant_id: accountantId,
                    name: name,
                    password: password,
                    _token: "{{ csrf_token() }}",
                },
                success: function (response) {
                    $("#ModelAccountantUpdate").modal('hide')
                    tempElementAccountantUpdate.find("td:nth-child(2)").text(response)
                }
            });
        });
        $("#delete-submit-accountant").click(function () {
            let accountantId = $("#accountant_id_delete").val()
            $.ajax({
                url: "{{route('admin.accountant.delete')}}",
                type: "POST",
                data: {
                    accountant_id: accountantId,
                    _token: "{{ csrf_token() }}",
                },
                success: function (response) {
                    $("#ModelAccountantDelete").modal('hide')
                    tempElementAccountant.remove()
                }
            });
        });

        $("#delete-submit").click(function () {
            let customerId = $("#customer_id_delete").val()


            let shareholderId = $("#shareholder_id_delete").val()
            let machineId = customerId
            let id = $("#id_delete").val()
            let id_parent = $("#id_parent_delete").val()
            let type_machine = $("#type_machine_delete").val()

            $.ajax({
                url: "{{route('api.shareholder.deleteShareholderForCustomer')}}",
                type: "POST",
                data: {
                    shareholder_id: shareholderId,
                    customer_id: customerId,
                    machine_id: machineId,
                    type_machine: type_machine,

                    _token: "{{ csrf_token() }}",
                },
                success: function (response) {
                    let find = ""
                    if (type_machine == 0) {
                        find = 'tr[data-id="' + id_parent + '"]'


                    } else {
                        find = 'tr[id="' + id_parent + '"]'
                    }
                    let select = $("#table-transactions").find(`${find}`)
                    select.find("td:nth-child(9)").find(`div:eq(${id - 1})`).remove()
                    select.find("td:nth-child(10)").find(`div:eq(${id - 1})`).remove()

                    select.find("td:nth-child(11)").find(`div:eq(${id - 1})`).remove()
                    select.find("td:nth-child(12)").find(`div:eq(${id - 1})`).remove()
                    select.find("td:nth-child(13)").find(`div:eq(${id - 1})`).remove()
                    $("#ModalDelete").modal('hide')
                    $("#type_machine_delete").val(0)
                }
            });
        });

        function scan() {
            $("#type_machine_update").val(0)

            $("#type_machine_add").val(0)
            $("#type_machine_delete").val(0)
            $.ajax({
                url: "{{route('api.statistic')}}",
                type: "post",
                data: {
                    _token: "{{ csrf_token() }}",
                    date_start: date_start,
                    date_end: date_end,
                },
                success: function (response) {
                    $(".table-responsive").empty()
                    $(".table-responsive").append(`<table class="table table-static table-bordered text-nowrap dataTable text-sm collapsed" id="table-transactions">
                                        <tbody>
                                        <tr>
                                             <th>STT</th>
                                            <th>Tên</th>
                                            <th>Thắng thua</th>
                                            <th>Nam</th>
                                            <th>Bắc</th>
                                            <th>Trung</th>
                                            <th>Tổng cược</th>
                                            <th>Lai về</th>
                                            <th>Thành viên</th>
                                            <th>Phần trăm</th>
                                            <th>Thành tiền</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                        </tbody>
                                        <tbody>
                                        </tbody>
                                    </table>`)
                    let total_bet = 0;
                    let prize = 0;
                    let lai_ve = 0;
                    let sum_nam = 0;
                    let sum_bac = 0;
                    let sum_trung = 0;
                    let id = 0;

                    if (response != null && response.length !== 0) {
                        response.sort((a, b) => a.username.localeCompare(b.username));
                        console.log(response)
                        response.forEach(function (k) {
                            let subBody = ""
                            total_bet += k.tien_cuoc
                            prize += k.thang_thua
                            lai_ve += k.lai_ve != null ? k.lai_ve : 0
                            sum_nam += k.sum_nam != null ? k.sum_nam : 0
                            sum_bac += k.sum_bac != null ? k.sum_bac : 0
                            sum_trung += k.sum_trung != null ? k.sum_trung : 0
                            id++
                            if (Array.isArray(k.customersList)) {
                                let subId = id + "00001" - 1;
                                k.customersList.sort((a, b) => a.username.localeCompare(b.username));

                                k.customersList.forEach(element => {

                                    let id2 = 0
                                    subId++
                                    let strBtn = ""
                                    let removeBtn = ""
                                    element.shareholder_ids.forEach(l => {
                                        id2++;
                                        removeBtn += `
                                                    <div>
                                                      <button class="btn btn-danger ce delete-button"
                                                            style="padding: 2px 6px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                        data-toggle="modal" data-target="#ModalDelete"
                                        onclick="confirmDelete(this,${l},${id2},${subId})">
                                                        <i class="fas fa-trash-alt"></i> <!-- Chỉnh sửa icon 'fa fa-edit' -->
                                                      </button>
                                                    </div>
                                                  `;
                                        strBtn += `
                                                    <div>
                                                      <button class="btn btn-primary update-button"
                                                            style="padding: 2px 5px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                                        data-toggle="modal" data-target="#ModalUpdate"
                                        onclick="updateStock(this,${l},${element.thang_thua},${id2},${subId})">
                                                        <i class="fa fa-edit"></i> <!-- Chỉnh sửa icon 'fa fa-edit' -->
                                                      </button>
                                                    </div>
                                                  `;
                                    })


                                    subBody += `  <tr  data-id="${subId}" class="collapse" id="collapseExample${id}">
                                                            <td></td>
                                                            <td>${element.username}</td>
                                                            <td>${element.thang_thua != null ? formatNumber(element.thang_thua) : 0}</td>
                                            <td><a href="#">${element.sum_nam != null ? formatNumber(element.sum_nam) : 0}</a></td>
                                            <td><a href="#">${element.sum_bac != null ? formatNumber(element.sum_bac) : 0}</a></td>
                                            <td><a href="#">${element.sum_trung != null ? formatNumber(element.sum_trung) : 0}</a></td>
                                                            <td>${formatNumber(element.tien_cuoc)}</td>
                                                            <td>${element.lai_ve != null ? formatNumber(element.lai_ve) : 0}</td>

                                                            <td>${element.name_shareholder != null && element.name_shareholder !== "" ? element.name_shareholder : ""}</td>
                                                            <td>${element.percent != null && element.percent !== "" ? element.percent : ""}</td>
                                                            <td>${element.total_sum != null && element.total_sum !== "" ? element.total_sum : ""}</td>
                                                           <td class="${element.customer_id}">

                                                               ${strBtn}
                                                            </td>
                                                            <td class="${element.customer_id}">

                                                               ${removeBtn}
                                                            </td>
                                                            <td>
                                                                <button
                                                                    type="button"  class="btn btn-primary btn-add" data-toggle="modal" data-target="#ModalDetail"
                                                                    onclick="addStock(${element.customer_id},${element.thang_thua},${subId})"><i class="fas fa-plus"></i>
                                                                </button>
                                                            </td>
                                                        </tr>`
                                })
                            } else if (typeof k.customersList === 'object' && k.customersList !== null) {
                                let subId = id + "00001" - 1;



                                Object.keys(k.customersList).sort((a, b) => {
                                    let usernameA = k.customersList[a].username;
                                    let usernameB = k.customersList[b].username;
                                    return usernameA.localeCompare(usernameB);
                                }).forEach(key => {
                                    let element = k.customersList[key]


                                    let id2 = 0;
                                    subId++;
                                    let strBtn = ""
                                    let removeBtn = ""
                                    element.shareholder_ids.forEach(l => {
                                        id2++;
                                        removeBtn += `
                                                    <div>
                                                      <button
                                                      class="btn btn-danger ce delete-button"
                                                style="padding: 2px 6px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                                                            data-toggle="modal" data-target="#ModalDelete"
                                                            onclick="confirmDelete(this,${l},${id2},${subId})">
                                                        <i class="fas fa-trash-alt"></i>
                                                      </button>
                                                    </div>
                                                  `;
                                        strBtn += `
                                                    <div>
                                                      <button class="btn btn-primary update-button"
                                                            style="padding: 2px 5px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button" class="btn btn-primary"
                                                            data-toggle="modal" data-target="#ModalUpdate"
                                                            onclick="updateStock(this,${l},${element.thang_thua},${id2},${subId})">
                                                        <i class="fa fa-edit"></i>
                                                      </button>
                                                    </div>
                                                  `;
                                    })
                                    subBody += `  <tr data-id="${subId}" class="collapse" id="collapseExample${id}">
                                                            <td></td>
                                                            <td>${element.username}</td>
                                                            <td>${element.thang_thua != null ? formatNumber(element.thang_thua) : 0}</td>
                                            <td><a href="#">${element.sum_nam != null ? formatNumber(element.sum_nam) : 0}</a></td>
                                            <td><a href="#">${element.sum_bac != null ? formatNumber(element.sum_bac) : 0}</a></td>
                                            <td><a href="#">${element.sum_trung != null ? formatNumber(element.sum_trung) : 0}</a></td>
                                                            <td>${formatNumber(element.tien_cuoc)}</td>
                                                            <td>${element.lai_ve != null ? formatNumber(element.lai_ve) : 0}</td>
                                                            <td>${element.name_shareholder != null && element.name_shareholder !== "" ? element.name_shareholder : ""}</td>
                                                            <td>${element.percent != null && element.percent !== "" ? element.percent : ""}</td>
                                                            <td>${element.total_sum != null && element.total_sum !== "" ? element.total_sum : ""}</td>
                                                           <td class="${element.customer_id}">

                                                               ${strBtn}
                                                            </td>
                                                            <td class="${element.customer_id}">

                                                               ${removeBtn}
                                                            </td>
                                                            <td>
                                                                <button
                                                                    style="width: 30px; height: 30px; padding: 6px 0px; font-size: 16px; text-align: center;"
                                                                    type="button"  class="btn btn-primary btn-circle" data-toggle="modal" data-target="#ModalDetail"
                                                                    onclick="addStock(${element.customer_id},${element.thang_thua},${subId})">+
                                                                </button>
                                                            </td>
                                                        </tr>`
                                })
                            }
                            let pStrBtn = ""
                            let pRemoveBtn = ""
                            let pid = 0;
                            k.shareholder_ids.forEach(o => {
                                pid++;
                                pRemoveBtn += `
                                                    <div>
                                                      <button class="btn btn-danger ce delete-button"
                                                            style="padding: 2px 6px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                        data-toggle="modal" data-target="#ModalDelete"
                                        onclick="confirmDeleteParent(this,${o},${pid},${id})">
                                                        <i class="fas fa-trash-alt"></i> <!-- Chỉnh sửa icon 'fa fa-edit' -->
                                                      </button>
                                                    </div>
                                                  `;
                                pStrBtn += `
                                                    <div>
                                                      <button class="btn btn-primary update-button"
                                                            style="padding: 2px 5px; margin-bottom: 3px; font-size: 13px;"
                                                            type="button"
                                        data-toggle="modal" data-target="#ModalUpdate"
                                        onclick="updateStockParent(this,${o},${k.thang_thua},${pid},${id})">
                                                        <i class="fa fa-edit"></i>
                                                      </button>
                                                    </div>
                                                  `;
                            })


                            $("#table-transactions > tbody:nth-child(2) ").append(`  <tr id="${id}">
                                            <td >${id}</td>
                                            <td style="cursor: pointer" data-toggle="collapse"
                                            href="#collapseExample${id}" aria-expanded="false"
                                            aria-controls="collapseExample"><i class="fa fa-caret-down" aria-hidden="true"></i> ${k.username}</td>
                                             <td> ${k.thang_thua != null ? formatNumber(k.thang_thua) : 0}</td>
                                             <td><a href="">${k.sum_nam != null ? formatNumber(k.sum_nam) : 0}</a></td>
                                            <td><a href="">${k.sum_bac != null ? formatNumber(k.sum_bac) : 0}</a></td>
                                            <td><a href="">${k.sum_trung != null ? formatNumber(k.sum_trung) : 0}</a></td>
                                            <td>${formatNumber(k.tien_cuoc)}</td>
                                            <td>${k.lai_ve != null ? formatNumber(k.lai_ve) : 0}</td>
                                            <td>${k.name_shareholder != null && k.name_shareholder !== "" ? k.name_shareholder : ""}</td>
                                            <td>${k.percent != null && k.percent !== "" ? k.percent : ""}</td>
                                            <td>${k.total_sum != null && k.total_sum !== "" ? formatNumber(k.total_sum) : ""}</td>
                                           <td class="${k.id}">
                                                    ${pStrBtn}

                                            </td>
                                            <td class="${k.id}">

                                                    ${pRemoveBtn}
                                            </td>
                                            <td>
                                                    <button
                                                    type="button"  class="btn btn-primary btn-add" data-toggle="modal" data-target="#ModalDetail"
                                                    onclick="addStockParent(${k.id},${k.thang_thua},${id})"><i class="fas fa-plus"></i>
                                                </button>
                                            </td>
                                        </tr>
                    ${subBody}
                                    `)

                        })


                    }
                    $("#table-transactions > tbody:nth-child(2)").append(`<tr>
                                          <td></td>
                                            <td>Tổng</td>
                                            <td>${formatNumber(prize)}</td>
                                            <td>${formatNumber(sum_nam)}</td>
                                            <td>${formatNumber(sum_bac)}</td>
                                            <td>${formatNumber(sum_trung)}</td>
                                            <td>${formatNumber(total_bet)}</td>
                                            <td>${formatNumber(lai_ve)}</td>
                                        </tr>`)
                }
            });

        }

        function setActiveButton(button) {
            document.querySelectorAll('.btn-day').forEach(btn => btn.classList.remove('active'));

            button.classList.add('active');
        }

        function formatNumber(number) {
            if (!number) return '0';
            if (number < 0 && number) {
                return ` <span style="color: red"> ${number.toLocaleString('en-US', {maximumFractionDigits: 0})} </span>`
            }

            return number.toLocaleString('en-US', {maximumFractionDigits: 0});
        }

        function setLastWeek(button) {
            setActiveButton(button);
            var currentDate = new Date();
            var currentDay = currentDate.getDay();

// Đặt ngày hiện tại về thứ 2 của tuần
            var startDayOfWeek = currentDay === 0 ? -6 : 1;
            var startDate = new Date(currentDate);
            startDate.setDate(currentDate.getDate() - currentDay + startDayOfWeek);

// Lấy ngày Chủ nhật của tuần
            var endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

// Lấy ngày thứ 2 của tuần trước
            var startOfLastWeek = new Date(startDate);
            startOfLastWeek.setDate(startDate.getDate() - 7);

// Lấy ngày Chủ nhật của tuần trước
            var endOfLastWeek = new Date(endDate);
            endOfLastWeek.setDate(endDate.getDate() - 7);

            date_start = startOfLastWeek.toLocaleDateString('en-GB');
            date_end = endOfLastWeek.toLocaleDateString('en-GB');
            from_date.val(date_start)
            from_date.datepicker("setDate", date_start);
            to_day.val(date_end)
        }

        function setThisWeek(button) {
            setActiveButton(button);
            var currentDate = new Date();
            var currentDay = currentDate.getDay();

            // Đặt ngày hiện tại về thứ 2 của tuần
            var startDayOfWeek = currentDay === 0 ? -6 : 1;
            var startDate = new Date(currentDate);
            startDate.setDate(currentDate.getDate() - currentDay + startDayOfWeek);

            // Lấy ngày Chủ nhật của tuần
            var endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

            var formattedStartDate = startDate.toLocaleDateString('en-GB');
            var formattedEndDate = endDate.toLocaleDateString('en-GB');
            date_start = formattedStartDate;
            date_end = formattedEndDate;
            from_date.val(date_start)
            from_date.datepicker("setDate", date_start);
            to_day.val(date_end)
        }

        function setYesterday(button) {
            setActiveButton(button);
            date_start = formatDate(currentDate, 1);
            date_end = formatDate(currentDate, 1);
            from_date.val(date_start)
            from_date.datepicker("setDate", date_start);
            to_day.val(date_end)

        }

        function setToday(button) {
            setActiveButton(button);
            date_end = formatDate(currentDate, 0);
            date_start = formatDate(currentDate, 0);
            from_date.val(date_start)
            from_date.datepicker("setDate", date_start);
            to_day.val(date_end)
        }

        function formatDate(date, subDay) {
            var newDate = new Date(date);
            newDate.setDate(date.getDate() - subDay);

            var day = newDate.getDate();
            var month = newDate.getMonth() + 1;
            var year = newDate.getFullYear();


            var formattedDate = (day < 10 ? '0' + day : day) + '/' + (month < 10 ? '0' + month : month) + '/' + year;

            return formattedDate;
        }

        document.addEventListener("DOMContentLoaded", function () {
            var hash = window.location.hash;
            if (hash) {
                var tabLink = document.querySelector(hash);
                var tabA = document.querySelector(".tab-home[href='" + hash + "']")
                console.log(tabA)
                if (tabLink) {

                    document.querySelector("#menu1").classList.remove("active");
                    document.querySelector("#menu2").classList.remove("active");
                    document.querySelector("#menu3").classList.remove("active");
                    document.querySelectorAll(".tab-home ").forEach(function (tab) {
                        tab.classList.remove("active");
                    });

                }
                tabA.classList.add("active");
                tabLink.classList.add("active");
                tabLink.classList.add("show");
            }
        });

    </script>

@endpush
