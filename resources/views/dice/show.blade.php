<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests"/>
    <title>Random Dice Table</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Zoom wrapper - this will contain everything that needs to be zoomed */
        .zoom-wrapper {
            transform-origin: top left;
            transition: transform 0.2s ease;
            width: 100%;
        }

        body {
            background: linear-gradient(135deg, #fff700 0%, #ffe259 100%);
            font-family: 'Roboto', Arial, sans-serif;
            margin: 0;
            min-height: 100vh;
            padding: 20px;
            overflow-x: auto;
        }

        .container {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 4px solid #222;
            padding: 32px 24px 24px 24px;
            max-width: 1200px;
            margin: 0 auto 30px auto;
            width: 100%;
        }

        .session-table {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 4px solid #222;
            padding: 24px;
            max-width: 1200px;
            margin: 0 auto 30px auto;
            width: 100%;
        }

        h1 {
            margin-top: 0;
            font-size: 2.2em;
            color: #333;
            letter-spacing: 2px;
        }

        .date-filter {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .date-filter label {
            font-weight: 500;
            color: #222;
        }

        .date-filter input[type="date"] {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #bbb;
            font-size: 1em;
        }

        /* Table responsive wrapper */
        .table-wrapper {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        table {
            width: 100%;
            min-width: 600px;
            border-collapse: collapse;
            margin-bottom: 0;
            background: white;
        }

        th, td {
            padding: 8px;
            text-align: center;
            border: 1px solid #adb5bd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .header-row th {
            background-color: #e9ecef;
            border-bottom: 2px solid #adb5bd;
        }

        .data-row td {
            border-bottom: 1px solid #adb5bd;
        }

        .summary-row td, .highlight-row td, .tc-row td, .tt-row td {
            border-bottom: 1px solid #adb5bd;
            font-weight: bold;
        }

        .highlight-row {
            background-color: #fff3cd;
        }

        .tc-row {
            background-color: #d1e7dd;
        }

        .tt-row {
            background-color: #cfe2ff;
        }

        .negative {
            color: #e53935;
            font-weight: 500;
        }

        .positive {
            color: rgb(0, 0, 0);
            font-weight: 500;
        }

        /* Excel-style Zoom Controls */
        .zoom-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px 12px;
            border-radius: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 1px solid #ddd;
        }

        .zoom-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: #fff;
            color: #333;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: #f0f0f0;
            border-color: #999;
        }

        .zoom-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .zoom-slider {
            width: 100px;
            height: 4px;
            background: #ddd;
            border-radius: 2px;
            outline: none;
            cursor: pointer;
        }

        .zoom-level {
            font-size: 12px;
            color: #666;
            min-width: 40px;
            text-align: center;
        }

        /* Responsive Design - Mobile First Approach */

        /* Base styles for mobile */
        @media (max-width: 767px) {
            body {
                padding: 10px;
            }

            .container, .session-table {
                padding: 16px;
                margin-bottom: 20px;
                border-radius: 12px;
                border-width: 3px;
            }

            h1 {
                font-size: 1.5em;
                letter-spacing: 1px;
            }

            table {
                min-width: 500px;
            }

            th, td {
                padding: 6px 4px;
                font-size: 0.9em;
            }

            .date-filter {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            .zoom-controls {
                bottom: 10px;
                right: 10px;
                padding: 6px 8px;
                gap: 8px;
            }

            .zoom-btn {
                width: 28px;
                height: 28px;
                font-size: 12px;
            }

            .zoom-slider {
                width: 80px;
            }

            .zoom-level {
                font-size: 11px;
                min-width: 35px;
            }
        }

        /* Tablet styles */
        @media (min-width: 768px) and (max-width: 1199px) {
            body {
                padding: 15px;
            }

            .container, .session-table {
                padding: 24px;
                border-radius: 15px;
            }

            h1 {
                font-size: 1.8em;
            }

            table {
                min-width: 650px;
            }

            th, td {
                padding: 8px 6px;
                font-size: 0.95em;
            }
        }

        /* Desktop styles */
        @media (min-width: 1200px) {
            .container, .session-table {
                max-width: 1200px;
            }

            table {
                min-width: 800px;
            }
        }

        input[readonly]:focus {
            outline: none;
            box-shadow: none;
            border: none;
        }

        .highlight-td {
            /*background-color: #d4f1ff !important;*/
            background-color: #f4de5a8a;
        }

        td.s-highlight {
            background-color: #f44336 !important; /* đỏ */
        }

        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Touch-friendly improvements */
        input[type="number"] {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }

        /* Better button styling for touch */
        .btn {
            min-height: 44px;
            touch-action: manipulation;
        }

        /* Improve modal for mobile */
        @media (max-width: 767px) {
            .modal-dialog {
                margin: 10px;
                max-width: calc(100% - 20px);
            }

            .modal-content {
                border-radius: 8px;
            }
        }
    </style>
</head>
<body>
<!-- Excel-style Zoom Controls -->
<div class="zoom-controls">
    <button class="zoom-btn" id="zoomOut" title="Zoom Out">
        <i class="fas fa-minus"></i>
    </button>
    <input type="range" class="zoom-slider" id="zoomSlider" min="50" max="200" value="100" title="Zoom Level">
    <button class="zoom-btn" id="zoomIn" title="Zoom In">
        <i class="fas fa-plus"></i>
    </button>
    <div class="zoom-level" id="zoomLevel">100%</div>
</div>

<!-- Main content wrapper for zoom -->
<div class="zoom-wrapper" id="zoomWrapper">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
            <h1 class="mb-0 flex-grow-1">Bảng ghi {{ $session->name }}</h1>
            <a href="{{ route('dice.index') }}" class="btn btn-secondary mt-2 mt-md-0">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>
        <form class="date-filter" id="dateForm">
            {{--            <label for="date">Select date:</label>--}}
            {{--            <input type="date" id="date" name="date" value="">--}}
        </form>
    </div>
    <div id="tables-root"></div>
</div>

<!-- Password Confirmation Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">Xác nhận mật khẩu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="password">Vui lòng nhập mật khẩu để thao tác:</label>
                    <input type="password" class="form-control" id="password" placeholder="Nhập mật khẩu">
                    <div class="invalid-feedback" id="passwordError"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="confirmPassword">Xác nhận</button>
            </div>
        </div>
    </div>
</div>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1" aria-labelledby="warningModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warningModalLabel">Thông báo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Đã nhập sai vui lòng kiểm tra lại
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Load Bootstrap first -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Initialize modal first
    let currentInput = null;
    const passwordModal = new bootstrap.Modal(document.getElementById('passwordModal'));

    function handleInputClick(input) {
        // if (!input.readOnly) {
        //     const td = input.closest('td');
        //     const tr = td.closest('tr');
        //
        //     const tds = tr.querySelectorAll('td');
        //
        //     const currentHighlightedTd = Array.from(tds).find(tdItem => tdItem.classList.contains('s-highlight'));
        //
        //     if (currentHighlightedTd && currentHighlightedTd !== td) {
        //         console.log('Có ô đỏ khác, không tăng count ô này');
        //         return;
        //     }
        //
        //     let count = parseInt(input.dataset.clickCount) || 0;
        //     count++;
        //     input.dataset.clickCount = count;
        //
        //     if (count === 3) {
        //         if (td.classList.contains('s-highlight')) {
        //             td.classList.remove('s-highlight');
        //             input.dataset.clickCount = 0;
        //             console.log('Removed highlight from td');
        //         } else {
        //             tds.forEach(tdItem => {
        //                 if (tdItem !== td) {
        //                     tdItem.classList.remove('s-highlight');
        //                     const inputInTd = tdItem.querySelector('input');
        //                     if (inputInTd) {
        //                         inputInTd.dataset.clickCount = 0;
        //                     }
        //                 }
        //             });
        //             td.classList.add('s-highlight');
        //             input.dataset.clickCount = 0;
        //             console.log('Added highlight to td');
        //
        //         }
        //     }
        //
        //     tds.forEach(tdItem => {
        //         if (tdItem !== td && tdItem !== currentHighlightedTd) {
        //             const inputInTd = tdItem.querySelector('input');
        //             if (inputInTd) {
        //                 inputInTd.dataset.clickCount = 0;
        //             }
        //         }
        //     });
        //
        // }

        if (input.readOnly && input.value !== '') {
            currentInput = input;
            passwordModal.show();
        }
    }


    function getMockSessions() {

        fetch(`/api/dice/${idReq}`)
            .then(response => {
                return response.json();
            })
            .then(data => {
                renderTables(data);
            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    const root = document.getElementById('tables-root');
    let stt = 0;
    const currentPath = window.location.pathname;
    const idReq = currentPath.split('/').pop();

    function renderTables(sessions) {
        root.innerHTML = '';
        sessions.forEach((session) => {

            const single = renderSingleSession(session);
            root.appendChild(single);
        });
    }

    function formatDateShort(input) {
        const d = new Date(input);
        const day = d.getDate();
        const month = d.getMonth() + 1;
        const year = d.getFullYear() % 100;
        return `${day}/${month}/${year}`;
    }

    function renderSingleSession(session) {

        stt++;
        const table = document.createElement('table');
        table.setAttribute('data-id', session.id);
        const sessionTitle = document.createElement('div');
        sessionTitle.className = 'session-title';
        // sessionTitle.textContent = `Bảng ${stt} (${session.name})`;
        // const weekdayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
        // const today = new Date();
        const weekdayText = `Bảng ${stt} (${session.name})`;
        const highlightName = (stt - 1) % 4;
        table.innerHTML = `
        <tr class="header-row">
            <th colspan="6">${weekdayText}</th>
        </tr>
        <tr>
            <th style="width: 60px; color:red">${formatDateShort(session.date)}</th>
            <th class="${highlightName == 0 ? 'highlight-td' : ''}" >${session.u1}</th>
            <th class="${highlightName == 1 ? 'highlight-td' : ''}">${session.u2}</th>
            <th class="${highlightName == 2 ? 'highlight-td' : ''}">${session.u3}</th>
            <th class="${highlightName == 3 ? 'highlight-td' : ''}">${session.u4}</th>
            <th >Total</th>
        </tr>
        `;

        session.rows.forEach((row, i) => {
            const total = row.value.reduce((a, b) => a + b, 0);
            const type = row.type;

            const groupIndex = Math.floor(i / 3);
            const isGray = groupIndex % 2 === 1;
            const trStyle = isGray ? 'style="background: #e0e0e0;"' : '';

            table.innerHTML += `
        <tr class="data-row" ${trStyle}>
            <td>${(i % 3) + 1}</td>
            ${row.value.map((val, idx) => `
                <td class="${val < 0 ? 'negative' : 'positive'}
              ${idx + 1 == row.same_cell ? 's-highlight' : ''} " >
                    <input type="number" ${type} value="${val ?? ''}" name="${idx}" data-index="${idx}" data-id="${row.id}"
                        style="width: 60px; text-align: center; background: #f9f9f9; border: 1px solid #ccc; border-radius: 4px; color: inherit; font-weight: inherit;"
                        onclick="handleInputClick(this)" />
                </td>
            `).join('')}
            <td class="${total < 0 ? 'negative' : 'positive'}">${total}</td>
        </tr>
    `;
        });

        table.innerHTML += `
    <tr class="summary-row" style="font-size: 20px;">
        <td>${session.ftd}</td>
        ${session.td.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="${session.td.reduce((a, b) => a + b, 0) < 0 ? 'negative' : 'positive'}">${session.td.reduce((a, b) => a + b, 0)}</td>
    </tr>
    <tr class="highlight-row" data-id="${session.id}" style="font-size: 20px;">
        <td>${session.cc.reduce((a, b) => a + b, 0)}</td>
        ${session.cc.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="positive"></td>
    </tr>
    <tr class="tc-row" data-id="${session.id}" style="font-size: 20px;">
        <td>TC</td>
        ${session.tc.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="positive">0</td>
    </tr>
    <tr class="tt-row" data-id="${session.id}" style="font-size: 20px;">
        <td>TT</td>
        ${session.tt.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v.toLocaleString()}</td>`).join('')}
        <td>${stt}</td>
    </tr>
`;

        const wrapper = document.createElement('div');
        wrapper.className = 'session-table';
        wrapper.appendChild(sessionTitle);

        // Add table wrapper for horizontal scroll on small screens
        const tableWrapper = document.createElement('div');
        tableWrapper.className = 'table-wrapper';
        tableWrapper.appendChild(table);
        wrapper.appendChild(tableWrapper);

        return wrapper;
    }

    function renderForDate() {
        const sessions = getMockSessions();
    }

    function updateLockRow(row_id, c1, c2, c3, c4, table, dice_id) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch('/api/dice/unlock/' + row_id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                c1: c1,
                c2: c2,
                c3: c3,
                c4: c4,

            })
        }).then(response => {
            return response.json();
        })
            .then(data => {
                const summaryRow = table.querySelector('.summary-row');
                const tcRow = table.querySelector('.tc-row');
                const ccRow = table.querySelector('.highlight-row');
                const ttRow = table.querySelector('.tt-row');
                const summaryCells = Array.from(summaryRow.querySelectorAll('td'));
                const tcCells = Array.from(tcRow.querySelectorAll('td'));
                const ccCells = Array.from(ccRow.querySelectorAll('td'));
                const ttCells = Array.from(ttRow.querySelectorAll('td'));

                data[0].td.forEach((value, index) => {
                    summaryCells[index + 1].textContent = value;
                    summaryCells[index + 1].classList.remove('negative', 'positive');
                    summaryCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].cc.forEach((value, index) => {
                    ccCells[index + 1].textContent = value;
                    ccCells[index + 1].classList.remove('negative', 'positive');
                    ccCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tt.forEach((value, index) => {
                    ttCells[index + 1].textContent = value.toLocaleString();
                    ttCells[index + 1].classList.remove('negative', 'positive');
                    ttCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tc.forEach((value, index) => {
                    tcCells[index + 1].textContent = value;
                    tcCells[index + 1].classList.remove('negative', 'positive');
                    tcCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });
                ccCells[0].textContent = data[0].cc.reduce((a, b) => a + b, 0);
                fetchDataFrom(dice_id).then(
                    item => {
                        item.forEach(session => {
                            updateTTRow(session);
                        });
                    }
                );

            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    async function fetchDataFrom(diceId) {
        const response = await fetch(`/api/dice/fetch/${idReq}?diceId=${diceId}`, {
            method: 'GET',
        });
        return await response.json();
    }


    function updateRow(row_id, row_next_id, c1, c2, c3, c4, table, inputsNextRow, highlightedIndex) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch('/api/dice/' + row_id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                row_next_id: row_next_id,
                c1: c1,
                c2: c2,
                c3: c3,
                c4: c4,
                same_cell: highlightedIndex,
            })
        }).then(response => {
            return response.json();
        })
            .then(data => {
                const summaryRow = table.querySelector('.summary-row');
                const tcRow = table.querySelector('.tc-row');
                const ccRow = table.querySelector('.highlight-row');
                const ttRow = table.querySelector('.tt-row');
                const summaryCells = Array.from(summaryRow.querySelectorAll('td'));
                const tcCells = Array.from(tcRow.querySelectorAll('td'));
                const ccCells = Array.from(ccRow.querySelectorAll('td'));
                const ttCells = Array.from(ttRow.querySelectorAll('td'));
                console.log(ccCells)
                data[0].td.forEach((value, index) => {
                    summaryCells[index + 1].textContent = value;
                    summaryCells[index + 1].classList.remove('negative', 'positive');
                    summaryCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].cc.forEach((value, index) => {
                    ccCells[index + 1].textContent = value;
                    ccCells[index + 1].classList.remove('negative', 'positive');
                    ccCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tt.forEach((value, index) => {
                    ttCells[index + 1].textContent = value.toLocaleString();
                    ttCells[index + 1].classList.remove('negative', 'positive');
                    ttCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tc.forEach((value, index) => {
                    tcCells[index + 1].textContent = value;
                    tcCells[index + 1].classList.remove('negative', 'positive');
                    tcCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });
                ccCells[0].textContent = data[0].cc.reduce((a, b) => a + b, 0);

                if (!data[0].is_lock_next) {
                    inputsNextRow.forEach(i => i.readOnly = false);

                } else {
                    fetchDataFrom(table.getAttribute('data-id')).then(
                        item => {
                            item.forEach(session => {
                                updateTTRow(session);
                            });
                        }
                    );
                }

                if (row_next_id == null) {
                    const single = renderSingleSession(data[1]);
                    root.appendChild(single);

                }

            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    function updateTTRow(session) {
        const ccRow = document.querySelector(`tr.highlight-row[data-id="${session.id}"]`);
        const tcRow = document.querySelector(`tr.tc-row[data-id="${session.id}"]`);
        const ttRow = document.querySelector(`tr.tt-row[data-id="${session.id}"]`);


        const ccCells = ccRow.querySelectorAll('td');

        const valueCcs = Array.from(ccCells).slice(1, -1);

        session.cc.forEach((val, idx) => {
            if (valueCcs[idx]) {
                valueCcs[idx].textContent = val.toLocaleString();
                valueCcs[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


        const tcCells = tcRow.querySelectorAll('td');

        const valueTcs = Array.from(tcCells).slice(1, -1);

        session.tc.forEach((val, idx) => {
            if (valueTcs[idx]) {
                valueTcs[idx].textContent = val.toLocaleString();
                valueTcs[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


        const ttCells = ttRow.querySelectorAll('td');

        const valueTts = Array.from(ttCells).slice(1, -1);

        session.tt.forEach((val, idx) => {
            if (valueTts[idx]) {
                valueTts[idx].textContent = val.toLocaleString();
                valueTts[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


    }

    document.addEventListener('change', function (event) {
        const input = event.target;

        if (
            input.tagName === 'INPUT' &&
            input.type === 'number' &&
            !input.readOnly &&
            input.closest('td')
        ) {
            const tr = input.closest('tr');
            const tbody = tr.closest('tbody');
            const table = tbody.closest('table');
            const nextTbody = tbody.nextElementSibling;
            const curInputs = Array.from(tr.querySelectorAll('input[type="number"]'));
            let sum = 0;
            curInputs.forEach(input => {
                const num = Number(input.value);
                if (!isNaN(num)) {
                    sum += num;

                    const td = input.closest('td');
                    td.classList.remove('negative', 'positive');
                    td.classList.add(num < 0 ? 'negative' : 'positive');
                }
            });

            const lastTd = tr.querySelector('td:last-child');
            lastTd.classList.remove('negative', 'positive');
            lastTd.classList.add(-(sum) < 0 ? 'negative' : 'positive');
            lastTd.textContent = -(sum);
            const values = curInputs.map(i => i.value);

            const inputsNextRow = nextTbody ? Array.from(nextTbody.querySelectorAll('input[type="number"]')) : [];

            let rowNextId = inputsNextRow[0]?.getAttribute('data-id') || null;
            // const tr = td.closest('tr');
            //


            // const currentHighlightedTd = Array.from(tds).find(tdItem => tdItem.classList.contains('s-highlight'));

            if (values.every(v => v !== '')) {
                if (sum === 0) {
                    let rowId = curInputs[0].getAttribute('data-id')
                    console.log("locked");
                    curInputs.forEach(i => i.readOnly = true);
                    const tds = tr.querySelectorAll('td');
                    const highlightedIndex = Array.from(tds).findIndex(td => td.classList.contains('s-highlight'));

                    updateRow(rowId, rowNextId, values[0], values[1], values[2], values[3], table, inputsNextRow,
                        highlightedIndex !== -1 ? highlightedIndex : null);
                } else {
                    const warningModal = new bootstrap.Modal(document.getElementById('warningModal'));
                    warningModal.show();
                    // curInputs.forEach(i => i.value = '');
                }
            }
        }
    });


    document.getElementById('confirmPassword').addEventListener('click', async function () {
        const password = document.getElementById('password').value;
        const passwordError = document.getElementById('passwordError');

        try {
            const response = await fetch('{{ route('dice.verify-password') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({password})
            });

            const data = await response.json();

            if (data.success) {
                currentInput.readOnly = false;
                const tr = currentInput.closest('tr');
                const curInputs = Array.from(tr.querySelectorAll('input[type="number"]'));
                const values = curInputs.map(i => i.value);
                console.log(values);

                curInputs.forEach(i => {
                    i.readOnly = false
                    i.value = '';
                });

                let rowId = curInputs[0].getAttribute('data-id')

                const tbody = tr.closest('tbody');

                const table = tbody.closest('table');

                const tds = tr.querySelectorAll('td');
                const highlightedTd = Array.from(tds).find(td => td.classList.contains('s-highlight'));

                updateLockRow(rowId, -values[0], -values[1], -values[2], -values[3], table, table.getAttribute('data-id'), true);
                if (highlightedTd) {
                    highlightedTd.classList.remove('s-highlight');
                }
                passwordModal.hide();
                document.getElementById('password').value = '';

            } else {
                passwordError.textContent = 'Mật khẩu không đúng';
                passwordError.style.display = 'block';
            }
        } catch (error) {
            console.error('Error:', error);
            passwordError.textContent = 'Có lỗi xảy ra';
            passwordError.style.display = 'block';
        }
    });

    document.getElementById('passwordModal').addEventListener('hidden.bs.modal', function () {
        document.getElementById('passwordError').style.display = 'none';
        document.getElementById('password').value = '';
    });

    renderForDate();

    // Excel-style Zoom functionality
    let currentZoom = 100; // Start at 100%
    const minZoom = 50;    // 50%
    const maxZoom = 200;   // 200%
    const zoomStep = 10;   // 10% increments

    const zoomWrapper = document.getElementById('zoomWrapper');
    const zoomInBtn = document.getElementById('zoomIn');
    const zoomOutBtn = document.getElementById('zoomOut');
    const zoomSlider = document.getElementById('zoomSlider');
    const zoomLevelDisplay = document.getElementById('zoomLevel');

    function updateZoom(newZoom) {
        currentZoom = Math.max(minZoom, Math.min(maxZoom, newZoom));
        const scale = currentZoom / 100;

        // Apply zoom to the entire content wrapper
        zoomWrapper.style.transform = `scale(${scale})`;

        // Update UI elements
        zoomLevelDisplay.textContent = `${currentZoom}%`;
        zoomSlider.value = currentZoom;

        // Update button states
        zoomInBtn.disabled = currentZoom >= maxZoom;
        zoomOutBtn.disabled = currentZoom <= minZoom;

        // Adjust body overflow to handle scaled content
        if (scale !== 1) {
            document.body.style.overflow = 'auto';
        }
    }

    // Zoom In button
    zoomInBtn.addEventListener('click', () => {
        updateZoom(currentZoom + zoomStep);
    });

    // Zoom Out button
    zoomOutBtn.addEventListener('click', () => {
        updateZoom(currentZoom - zoomStep);
    });

    // Zoom Slider
    zoomSlider.addEventListener('input', (e) => {
        updateZoom(parseInt(e.target.value));
    });

    // Keyboard shortcuts (Excel-style)
    document.addEventListener('keydown', (e) => {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case '=':
                case '+':
                    e.preventDefault();
                    updateZoom(currentZoom + zoomStep);
                    break;
                case '-':
                    e.preventDefault();
                    updateZoom(currentZoom - zoomStep);
                    break;
                case '0':
                    e.preventDefault();
                    updateZoom(100); // Reset to 100%
                    break;
            }
        }
    });

    // Mouse wheel zoom (Ctrl + wheel)
    document.addEventListener('wheel', (e) => {
        if (e.ctrlKey || e.metaKey) {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -zoomStep : zoomStep;
            updateZoom(currentZoom + delta);
        }
    }, { passive: false });

    // Initialize zoom
    updateZoom(100);
</script>
</body>
</html>
