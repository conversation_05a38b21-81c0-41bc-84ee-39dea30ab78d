<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Random Dice Table</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #fff700 0%, #ffe259 100%);
            font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
            margin: 0;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 4px solid #222;
            padding: 32px 24px 24px 24px;
            max-width: 950px;
            margin: 0 auto 30px auto;
        }

        .session-table {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
            border: 4px solid #222;
            padding: 24px;
            max-width: 950px;
            margin: 0 auto 30px auto;
        }

        h1 {
            margin-top: 0;
            font-size: 2.2em;
            color: #333;
            letter-spacing: 2px;
        }

        .date-filter {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 24px;
            flex-wrap: wrap;
        }

        .date-filter label {
            font-weight: 500;
            color: #222;
        }

        .date-filter input[type="date"] {
            padding: 6px 12px;
            border-radius: 6px;
            border: 1px solid #bbb;
            font-size: 1em;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 0;
            background: white;
        }

        th, td {
            padding: 8px;
            text-align: center;
            border: 1px solid #adb5bd;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .header-row th {
            background-color: #e9ecef;
            border-bottom: 2px solid #adb5bd;
        }

        .data-row td {
            border-bottom: 1px solid #adb5bd;
        }

        .summary-row td, .highlight-row td, .tc-row td, .tt-row td {
            border-bottom: 1px solid #adb5bd;
            font-weight: bold;
        }

        .highlight-row {
            background-color: #fff3cd;
        }

        .tc-row {
            background-color: #d1e7dd;
        }

        .tt-row {
            background-color: #cfe2ff;
        }

        .negative {
            color: #e53935;
            font-weight: 500;
        }

        .positive {
            color: rgb(0, 0, 0);
            font-weight: 500;
        }

        @media (max-width: 700px) {
            body {
                padding: 10px;
            }

            .container, .session-table {
                padding: 16px;
                margin-bottom: 20px;
            }

            table, th, td {
                font-size: 0.95em;
            }

            .session-title {
                font-size: 1.05em;
                margin-bottom: 12px;
            }
        }

        @media (max-width: 500px) {
            body {
                padding: 5px;
            }

            .container, .session-table {
                padding: 12px;
                margin-bottom: 15px;
            }

            th, td {
                padding: 6px 2px;
            }
        }

        input[readonly]:focus {
            outline: none;
            box-shadow: none;
            border: none;
        }

        .highlight-td {
            /*background-color: #d4f1ff !important;*/
            background-color: #f4de5a8a;
        }

        td.s-highlight {
            background-color: #f44336 !important; /* đỏ */
        }
    </style>
</head>
<body>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0">Bảng ghi {{ $session->name }}</h1>
        <a href="{{ route('dice.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Quay lại
        </a>
    </div>
    <form class="date-filter" id="dateForm">
        {{--            <label for="date">Select date:</label>--}}
        {{--            <input type="date" id="date" name="date" value="">--}}
    </form>
</div>
<div id="tables-root"></div>

<!-- Password Confirmation Modal -->
<div class="modal fade" id="passwordModal" tabindex="-1" aria-labelledby="passwordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="passwordModalLabel">Xác nhận mật khẩu</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="password">Vui lòng nhập mật khẩu để thao tác:</label>
                    <input type="password" class="form-control" id="password" placeholder="Nhập mật khẩu">
                    <div class="invalid-feedback" id="passwordError"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="confirmPassword">Xác nhận</button>
            </div>
        </div>
    </div>
</div>

<!-- Warning Modal -->
<div class="modal fade" id="warningModal" tabindex="-1" aria-labelledby="warningModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warningModalLabel">Thông báo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                Đã nhập sau vui lòng kiểm tra lại
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Load Bootstrap first -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
    // Initialize modal first
    let currentInput = null;
    const passwordModal = new bootstrap.Modal(document.getElementById('passwordModal'));

    function handleInputClick(input) {
        if (!input.readOnly) {
            const td = input.closest('td');
            const tr = td.closest('tr');

            const tds = tr.querySelectorAll('td');

            const currentHighlightedTd = Array.from(tds).find(tdItem => tdItem.classList.contains('s-highlight'));

            if (currentHighlightedTd && currentHighlightedTd !== td) {
                console.log('Có ô đỏ khác, không tăng count ô này');
                return;
            }

            let count = parseInt(input.dataset.clickCount) || 0;
            count++;
            input.dataset.clickCount = count;

            if (count === 3) {
                if (td.classList.contains('s-highlight')) {
                    td.classList.remove('s-highlight');
                    input.dataset.clickCount = 0;
                    console.log('Removed highlight from td');
                } else {
                    tds.forEach(tdItem => {
                        if (tdItem !== td) {
                            tdItem.classList.remove('s-highlight');
                            const inputInTd = tdItem.querySelector('input');
                            if (inputInTd) {
                                inputInTd.dataset.clickCount = 0;
                            }
                        }
                    });
                    td.classList.add('s-highlight');
                    input.dataset.clickCount = 0;
                    console.log('Added highlight to td');

                }
            }

            tds.forEach(tdItem => {
                if (tdItem !== td && tdItem !== currentHighlightedTd) {
                    const inputInTd = tdItem.querySelector('input');
                    if (inputInTd) {
                        inputInTd.dataset.clickCount = 0;
                    }
                }
            });

        }

        if (input.readOnly && input.value !== '') {
            currentInput = input;
            passwordModal.show();
        }
    }


    function getMockSessions() {

        fetch(`/api/dice/${idReq}`)
            .then(response => {
                return response.json();
            })
            .then(data => {
                renderTables(data);
            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    const root = document.getElementById('tables-root');
    let stt = 0;
    const currentPath = window.location.pathname;
    const idReq = currentPath.split('/').pop();

    function renderTables(sessions) {
        root.innerHTML = '';
        sessions.forEach((session) => {

            const single = renderSingleSession(session);
            root.appendChild(single);
        });
    }

    function renderSingleSession(session) {

        stt++;
        const table = document.createElement('table');
        table.setAttribute('data-id', session.id);
        const sessionTitle = document.createElement('div');
        sessionTitle.className = 'session-title';
        // sessionTitle.textContent = `Bảng ${stt} (${session.name})`;
        // const weekdayNames = ['Chủ nhật', 'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7'];
        // const today = new Date();
        const weekdayText = `Bảng ${stt} (${session.name})`;
        table.innerHTML = `
        <tr class="header-row">
            <th colspan="6">${weekdayText}</th>
        </tr>
        <tr>
            <th style="width: 60px;"></th>
            <th>${session.u1}</th>
            <th>${session.u2}</th>
            <th>${session.u3}</th>
            <th>${session.u4}</th>
            <th>Total</th>
        </tr>
    `;

        session.rows.forEach((row, i) => {
            const total = row.value.reduce((a, b) => a + b, 0);
            const type = row.type;

            const groupIndex = Math.floor(i / 3);
            const isGray = groupIndex % 2 === 1;
            const trStyle = isGray ? 'style="background: #e0e0e0;"' : '';
            const highlightColumn = (stt - 1) % 4;

            table.innerHTML += `
        <tr class="data-row" ${trStyle}>
            <td>${(i % 3) + 1}</td>
            ${row.value.map((val, idx) => `
                <td class="${val < 0 ? 'negative' : 'positive'}
            ${highlightColumn === idx ? 'highlight-td' : ''}  ${idx + 1 == row.same_cell ? 's-highlight' : ''} " >
                    <input type="text" ${type} value="${val ?? ''}" name="${idx}" data-index="${idx}" data-id="${row.id}"
                        style="width: 60px; text-align: center; background: #f9f9f9; border: 1px solid #ccc; border-radius: 4px; color: inherit; font-weight: inherit;"
                        onclick="handleInputClick(this)" />
                </td>
            `).join('')}
            <td class="${total < 0 ? 'negative' : 'positive'}">${total}</td>
        </tr>
    `;
        });

        table.innerHTML += `
    <tr class="summary-row" style="font-size: 20px;">
        <td>500</td>
        ${session.td.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="${session.td.reduce((a, b) => a + b, 0) < 0 ? 'negative' : 'positive'}">${session.td.reduce((a, b) => a + b, 0)}</td>
    </tr>
    <tr class="highlight-row" data-id="${session.id}" style="font-size: 20px;">
        <td>1000</td>
        ${session.cc.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="${session.cc.reduce((a, b) => a + b, 0) < 0 ? 'negative' : 'positive'}">${session.cc.reduce((a, b) => a + b, 0)}</td>
    </tr>
    <tr class="tc-row" data-id="${session.id}" style="font-size: 20px;">
        <td>TC</td>
        ${session.tc.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v}</td>`).join('')}
        <td class="${session.tc.reduce((a, b) => a + b, 0) < 0 ? 'negative' : 'positive'}">${session.tc.reduce((a, b) => a + b, 0)}</td>
    </tr>
    <tr class="tt-row" data-id="${session.id}" style="font-size: 20px;">
        <td>TT</td>
        ${session.tt.map(v => `<td class="${v < 0 ? 'negative' : 'positive'}">${v.toLocaleString()}</td>`).join('')}
        <td>${stt}</td>
    </tr>
`;

        const wrapper = document.createElement('div');
        wrapper.className = 'session-table';
        wrapper.appendChild(sessionTitle);
        wrapper.appendChild(table);

        return wrapper;
    }

    function renderForDate() {
        const sessions = getMockSessions();
    }

    function updateLockRow(row_id, c1, c2, c3, c4, table, dice_id) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch('/api/dice/unlock/' + row_id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                c1: c1,
                c2: c2,
                c3: c3,
                c4: c4,

            })
        }).then(response => {
            return response.json();
        })
            .then(data => {
                const summaryRow = table.querySelector('.summary-row');
                const tcRow = table.querySelector('.tc-row');
                const ccRow = table.querySelector('.highlight-row');
                const ttRow = table.querySelector('.tt-row');
                const summaryCells = Array.from(summaryRow.querySelectorAll('td'));
                const tcCells = Array.from(tcRow.querySelectorAll('td'));
                const ccCells = Array.from(ccRow.querySelectorAll('td'));
                const ttCells = Array.from(ttRow.querySelectorAll('td'));

                data[0].td.forEach((value, index) => {
                    summaryCells[index + 1].textContent = value;
                    summaryCells[index + 1].classList.remove('negative', 'positive');
                    summaryCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].cc.forEach((value, index) => {
                    ccCells[index + 1].textContent = value;
                    ccCells[index + 1].classList.remove('negative', 'positive');
                    ccCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tt.forEach((value, index) => {
                    ttCells[index + 1].textContent = value.toLocaleString();
                    ttCells[index + 1].classList.remove('negative', 'positive');
                    ttCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tc.forEach((value, index) => {
                    tcCells[index + 1].textContent = value;
                    tcCells[index + 1].classList.remove('negative', 'positive');
                    tcCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                fetchDataFrom(dice_id).then(
                    item => {
                        item.forEach(session => {
                            updateTTRow(session);
                        });
                    }
                );

            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    async function fetchDataFrom(diceId) {
        const response = await fetch(`/api/dice/fetch/${idReq}?diceId=${diceId}`, {
            method: 'GET',
        });
        return await response.json();
    }


    function updateRow(row_id, row_next_id, c1, c2, c3, c4, table, inputsNextRow, highlightedIndex) {
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

        fetch('/api/dice/' + row_id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({
                row_next_id: row_next_id,
                c1: c1,
                c2: c2,
                c3: c3,
                c4: c4,
                same_cell: highlightedIndex,
            })
        }).then(response => {
            return response.json();
        })
            .then(data => {
                const summaryRow = table.querySelector('.summary-row');
                const tcRow = table.querySelector('.tc-row');
                const ccRow = table.querySelector('.highlight-row');
                const ttRow = table.querySelector('.tt-row');
                const summaryCells = Array.from(summaryRow.querySelectorAll('td'));
                const tcCells = Array.from(tcRow.querySelectorAll('td'));
                const ccCells = Array.from(ccRow.querySelectorAll('td'));
                const ttCells = Array.from(ttRow.querySelectorAll('td'));

                data[0].td.forEach((value, index) => {
                    summaryCells[index + 1].textContent = value;
                    summaryCells[index + 1].classList.remove('negative', 'positive');
                    summaryCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].cc.forEach((value, index) => {
                    ccCells[index + 1].textContent = value;
                    ccCells[index + 1].classList.remove('negative', 'positive');
                    ccCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tt.forEach((value, index) => {
                    ttCells[index + 1].textContent = value.toLocaleString();
                    ttCells[index + 1].classList.remove('negative', 'positive');
                    ttCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });

                data[0].tc.forEach((value, index) => {
                    tcCells[index + 1].textContent = value;
                    tcCells[index + 1].classList.remove('negative', 'positive');
                    tcCells[index + 1].classList.add(value < 0 ? 'negative' : 'positive');
                });
                console.log("up[date ne")
                if (!data[0].is_lock_next) {
                    inputsNextRow.forEach(i => i.readOnly = false);

                } else {
                    fetchDataFrom(table.getAttribute('data-id')).then(
                        item => {
                            item.forEach(session => {
                                updateTTRow(session);
                            });
                        }
                    );
                }

                if (row_next_id == null) {
                    const single = renderSingleSession(data[1]);
                    root.appendChild(single);

                }

            })
            .catch(error => {
                console.error('Fetch error:', error);
            });
    }

    function updateTTRow(session) {
        const ccRow = document.querySelector(`tr.highlight-row[data-id="${session.id}"]`);
        const tcRow = document.querySelector(`tr.tc-row[data-id="${session.id}"]`);
        const ttRow = document.querySelector(`tr.tt-row[data-id="${session.id}"]`);


        const ccCells = ccRow.querySelectorAll('td');

        const valueCcs = Array.from(ccCells).slice(1, -1);

        session.cc.forEach((val, idx) => {
            if (valueCcs[idx]) {
                valueCcs[idx].textContent = val.toLocaleString();
                valueCcs[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


        const tcCells = tcRow.querySelectorAll('td');

        const valueTcs = Array.from(tcCells).slice(1, -1);

        session.tc.forEach((val, idx) => {
            if (valueTcs[idx]) {
                valueTcs[idx].textContent = val.toLocaleString();
                valueTcs[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


        const ttCells = ttRow.querySelectorAll('td');

        const valueTts = Array.from(ttCells).slice(1, -1);

        session.tt.forEach((val, idx) => {
            if (valueTts[idx]) {
                valueTts[idx].textContent = val.toLocaleString();
                valueTts[idx].className = val < 0 ? 'negative' : 'positive';
            }
        });


    }

    document.addEventListener('change', function (event) {
        const input = event.target;

        if (
            input.tagName === 'INPUT' &&
            input.type === 'text' &&
            !input.readOnly &&
            input.closest('td')
        ) {
            const tr = input.closest('tr');
            const tbody = tr.closest('tbody');
            const table = tbody.closest('table');
            const nextTbody = tbody.nextElementSibling;
            const curInputs = Array.from(tr.querySelectorAll('input[type="text"]'));
            let sum = 0;
            curInputs.forEach(input => {
                const num = Number(input.value);
                if (!isNaN(num)) {
                    sum += num;

                    const td = input.closest('td');
                    td.classList.remove('negative', 'positive');
                    td.classList.add(num < 0 ? 'negative' : 'positive');
                }
            });

            const lastTd = tr.querySelector('td:last-child');
            lastTd.classList.remove('negative', 'positive');
            lastTd.classList.add(-(sum) < 0 ? 'negative' : 'positive');
            lastTd.textContent = -(sum);
            const values = curInputs.map(i => i.value);

            const inputsNextRow = nextTbody ? Array.from(nextTbody.querySelectorAll('input[type="text"]')) : [];

            let rowNextId = inputsNextRow[0]?.getAttribute('data-id') || null;
            // const tr = td.closest('tr');
            //


            // const currentHighlightedTd = Array.from(tds).find(tdItem => tdItem.classList.contains('s-highlight'));

            if (values.every(v => v !== '')) {
                if (sum === 0) {
                    let rowId = curInputs[0].getAttribute('data-id')
                    console.log("locked");
                    curInputs.forEach(i => i.readOnly = true);
                    const tds = tr.querySelectorAll('td');
                    const highlightedIndex = Array.from(tds).findIndex(td => td.classList.contains('s-highlight'));

                    updateRow(rowId, rowNextId, values[0], values[1], values[2], values[3], table, inputsNextRow,
                        highlightedIndex !== -1 ? highlightedIndex : null);
                } else {
                    const warningModal = new bootstrap.Modal(document.getElementById('warningModal'));
                    warningModal.show();
                    // curInputs.forEach(i => i.value = '');
                }
            }
        }
    });


    document.getElementById('confirmPassword').addEventListener('click', async function () {
        const password = document.getElementById('password').value;
        const passwordError = document.getElementById('passwordError');

        try {
            const response = await fetch('{{ route('dice.verify-password') }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({password})
            });

            const data = await response.json();

            if (data.success) {
                currentInput.readOnly = false;
                const tr = currentInput.closest('tr');
                const curInputs = Array.from(tr.querySelectorAll('input[type="text"]'));
                const values = curInputs.map(i => i.value);
                console.log(values);

                curInputs.forEach(i => {
                    i.readOnly = false
                    i.value = '';
                });

                let rowId = curInputs[0].getAttribute('data-id')

                const tbody = tr.closest('tbody');

                const table = tbody.closest('table');

                const tds = tr.querySelectorAll('td');
                const highlightedTd = Array.from(tds).find(td => td.classList.contains('s-highlight'));

                updateLockRow(rowId, -values[0], -values[1], -values[2], -values[3], table, table.getAttribute('data-id'), true);
                if (highlightedTd) {
                    highlightedTd.classList.remove('s-highlight');
                }
                passwordModal.hide();
                document.getElementById('password').value = '';

            } else {
                passwordError.textContent = 'Mật khẩu không đúng';
                passwordError.style.display = 'block';
            }
        } catch (error) {
            console.error('Error:', error);
            passwordError.textContent = 'Có lỗi xảy ra';
            passwordError.style.display = 'block';
        }
    });

    document.getElementById('passwordModal').addEventListener('hidden.bs.modal', function () {
        document.getElementById('passwordError').style.display = 'none';
        document.getElementById('password').value = '';
    });

    renderForDate();
</script>
</body>
</html>
