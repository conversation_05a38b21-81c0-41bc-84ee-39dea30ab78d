@extends('layout.master')
@section('content')
    @push('head')
        <link href="{{ asset('css/datatables.min.css') }}" rel="stylesheet">
        <link href="{{ asset('css/highlight-within-textarea.min.css') }}" rel="stylesheet">

        <style>
            .draw-color {
                background-color: #ffec99;
            }

            .positive {
                color: green !important;
            }

            .negative {
                color: red !important;
            }

            .hidden-table {
                display: none;
            }
        </style>
    @endpush

    <div class="row">
        <div class="col-md-12">
            <div class="container-fluid">

                <div class="portlet light portlet-fit portlet-datatable bordered">
                    {{ Breadcrumbs::render('report.index', auth()->guard('machine')->user() ?? auth()->user()) }}

                    <div class="portlet-title">
                        <div class="caption">
                            <i class="icon-settings font-dark"></i>
                            <span class="caption-subject font-bold bold uppercase">THỐNG KÊ NGÀY
                                {{ date('d-m-Y', strtotime($fromDate)) }} ĐẾN NGÀY {{ date('d-m-Y', strtotime($toDate)) }}
                                CỦA {{ $name }} {{ $region_name }}</span>
                        </div>
                        <div class="actions">
                            <div class="btn-group btn-group-devided">
                                <a style="background-color: var(--green);" href="{{ route('ticket.create') }}"
                                    class="btn btn-tin blue mr-2">
                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                    Thêm tin nhắn
                                </a>
                            </div>
                            <div class="btn-group">
                                <a class="btn btn-outline-warning" href="javascript:" data-toggle="dropdown"
                                    aria-expanded="false">
                                    <i class="fa fa-share"></i>
                                    <span class="hidden-xs"> Công cụ </span>
                                    <i class="fa fa-angle-down"></i>
                                </a>
                                <ul class="dropdown-menu pull-right">
                                    <li>
                                        <a class="ml-4" id="report_export_excel"> Xuất ra Excel </a>
                                    </li>
                                    <li>
                                        <a class="ml-4" id="report_export_csv"> Xuất ra CSV </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="portlet-body" id="group-table-1">
                        <div id="table-1">
                            <table
                                class="table table-bordered table-striped dataTable no-footer text-sm collapsed table-area"
                                id="table${item.area_id}">
                                <thead>
                                    <tr class="table-fz">
                                        <th>Loại đánh</th>
                                        <th>Tiền đánh</th>
                                        <th>Tiền trúng</th>
                                        <th>Thắng thua</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="4" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <hr />
                    <div class="portlet-body" id="group-table-2">
                        <div class="btn-detail ml-2">
                            <div class="btn-detail ml-2">
                                <a class="btn btn-outline-primary blue  type-selector active" id="hai-so"
                                    onclick="handleChange(this)">Hai số</a>
                            </div>
                            <div class="btn-detail ml-2">
                                <a class="btn btn-outline-primary blue type-selector" id="ba-so"
                                    onclick="handleChange(this)">Ba số</a>
                            </div>
                            <div class="btn-detail ml-2">
                                <a class="btn btn-outline-primary blue type-selector" id="bon-so"
                                    onclick="handleChange(this)">Bốn số</a>
                            </div>
                            <div class="btn-detail ml-2">
                                <a class="btn btn-outline-primary blue type-selector" id="da-thang"
                                    onclick="handleChange(this)">Đá thẳng</a>
                            </div>
                            <div class="btn-detail ml-2">
                                <a class="btn btn-outline-primary blue type-selector" id="da-xien"
                                    onclick="handleChange(this)">Đá xiên</a>
                            </div>
                        </div>

                        <div class="container-danh" id="container-hai-so">
                            <hr />
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed "
                                id="table-hai-so-bao-lo">
                                <thead>
                                    <tr class="table-fz">
                                        <th>STT</th>
                                        <th>Đài</th>
                                        <th>Số đánh</th>
                                        <th>Số lần xuất hiện</th>
                                        <th>Tiền đánh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="5" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                        <div class="hidden-table container-danh" id="container-ba-so">
                            <hr />
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed ">
                                <thead>
                                    <tr class="table-fz">
                                        <th>STT</th>
                                        <th>Đài</th>
                                        <th>Số đánh</th>
                                        <th>Số lần xuất hiện</th>
                                        <th>Tiền đánh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="5" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                        <div class="hidden-table container-danh" id="container-bon-so">
                            <hr />
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed ">
                                <thead>
                                    <tr class="table-fz">
                                        <th>STT</th>
                                        <th>Đài</th>
                                        <th>Số đánh</th>
                                        <th>Số lần xuất hiện</th>
                                        <th>Tiền đánh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="5" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                        <div class="hidden-table container-danh" id="container-da-thang">
                            <hr />
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed ">
                                <thead>
                                    <tr class="table-fz">
                                        <th>STT</th>
                                        <th>Đài</th>
                                        <th>Số đánh</th>
                                        <th>Số lần xuất hiện</th>
                                        <th>Tiền đánh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="5" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                        <div class="hidden-table container-danh" id="container-da-xien">
                            <hr />
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed ">
                                <thead>
                                    <tr class="table-fz">
                                        <th>STT</th>
                                        <th>Đài</th>
                                        <th>Số đánh</th>
                                        <th>Số lần xuất hiện</th>
                                        <th>Tiền đánh</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <td colspan="5" class="text-center">Không có thông tin để hiển thị</td>
                                </tbody>
                            </table>
                        </div>
                    </div>


                </div>
            </div>
            <!-- End: life time stats -->
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('js/datatables.min.js') }}"></script>
    <script src="{{ asset('js/highlight-within-textarea.min.js') }}"></script>
    <script src="{{ asset('js/autosize.min.js') }}"></script>
    <script>
        $(document).ready(function() {

            const url = new URL(window.location.href);
            const pathSegments = url.pathname.split('/');
            const searchParams = new URLSearchParams(url.search);

            const customerId = pathSegments[2];
            const dateStart = searchParams.get('date_start');
            const dateEnd = searchParams.get('date_end');
            const region = searchParams.get('region');
            var ajax_url = `/api/reportDetail/${customerId}`;
            if (region == 0)
                ajax_url = `/api/reportDetail/all/${customerId}`;
            $.ajax({
                url: ajax_url,
                type: 'GET',
                data: {
                    date_start: dateStart,
                    date_end: dateEnd,
                    region: region,
                },
                success: function(response) {
                    if (region != 0) {
                        let table1 = response.table1
                        let container = $('#group-table-1');
                        if (table1.length) {
                            container.empty();
                            container.append(`
                        <h2>Thắng thua</h2>
                            <div class="btn-detail ml-2" id="area-selector">
                        </div>
                            `)
                            let selector = $('#area-selector');
                            let count = 0;
                            table1.forEach(function(item) {
                                selector.append(`
                            <div class="btn-detail ml-2">
                            <a
                               class="btn btn-outline-primary blue area-select" onclick="change_area(${item.area_id})" id="${item.area_id}">${item.area_name}
                                </a>
                        </div>
                            `)

                            })

                            table1.forEach(function(item) {
                                container.append(`
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed hidden-table table-area" id="table${item.area_id}">
                            <thead>
                                <tr class="table-fz">
                                    <th>Loại đánh</th>
                                    <th>Tiền đánh</th>
                                    <th>Tiền trúng</th>
                                    <th>Thắng thua</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)
                                const thisTabble = $(`#table${item.area_id}`);

                                for (const [key, value] of Object.entries(item)) {
                                    if (key != 'area_id' && key != 'area_name') {
                                        if (value.danh !== 0) {
                                            const thang_thua = value.trung.toFixed(1) - value
                                                .danh.toFixed(1)
                                            const colorClass = thang_thua > 0 ? 'text-primary' :
                                                'text-danger';
                                            thisTabble.append(
                                                `<tr class="${colorClass}">
                                    <td>${key}</td>
                                   <td>${Number(value.danh).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 1})}</td>
                                    <td>${Number(value.trung).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 1})}</td>
                                    <td class="${colorClass}">${Number(thang_thua).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 1})}</td>
                                </tr>`
                                            );
                                        }
                                    }
                                }

                            })
                            let firstElement = $('.area-select').first()
                            if (firstElement) {
                                firstElement.addClass('active');
                            }
                            let fistTable = $(`#table${firstElement.attr('id')}`)
                            if (fistTable) {
                                fistTable.removeClass('hidden-table');
                            }
                            container.append('<h2>Số trúng</h2>')
                            let so_trung = response.so_trung

                            Object.entries(so_trung).forEach(([dai, trung]) => {
                                container.append(
                                    `<div id="div_so_trung_${dai}" class="hidden-table table-trung"></div>`
                                    )
                                container2 = $(`#div_so_trung_${dai}`)
                                container2.append(`
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="trung${dai}">
                            <thead>
                                <tr class="table-fz">
                                    <th>Số đánh</th>
                                    <th>Đài</th>
                                    <th>Loại đánh</th>
                                    <th>Tiền trúng gốc</th>
                                    <th>Số lần trúng</th>
                                    <th>Tiền trúng</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)
                                const trungTabble = $(`#trung${dai} tbody`);
                                Object.entries(trung).forEach(([key, value]) => {
                                    const parts = key.split(',');
                                    trungTabble.append(`
                                <tr>
                                    <td>${parts[0]}</td>
                                    <td>${parts[2]}</td>
                                    <td>${parts[1]}</td>
                                    <td>${Number(value.danh).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                                    <td>${value.count}</td>
                                    <td>${Number(value.trung).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                                </tr>
                            `)

                                })
                                $(`#trung${dai}`).DataTable({
                                    processing: true,
                                    paging: true,
                                    lengthMenu: [10, 25, 50, 100, 150],
                                    pageLength: 25,
                                    order: [
                                        [5, 'desc']
                                    ],
                                    info: false
                                });


                            })
                            let fistTrung = $(`#div_so_trung_${firstElement.attr('id')}`)
                            if (fistTrung) {
                                fistTrung.removeClass('hidden-table');
                            }
                        }
                    }else{
                        let container = $('#group-table-1');
                        container.empty()
                        container.append(`
                        <h2>Số trúng</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="trung_">
                            <thead>
                                <tr class="table-fz">
                                    <th>Số đánh</th>
                                    <th>Đài</th>
                                    <th>Loại đánh</th>
                                    <th>Tiền trúng gốc</th>
                                    <th>Số lần trúng</th>
                                    <th>Tiền trúng</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)
                        if(Object.keys(response.so_trung).length){
                            const trungTabble = $(`#trung_ tbody`);
                                Object.entries(response.so_trung).forEach(([key, value]) => {
                                    const parts = key.split(',');
                                    trungTabble.append(`
                                <tr>
                                    <td>${parts[0]}</td>
                                    <td>${parts[2]}</td>
                                    <td>${parts[1]}</td>
                                    <td>${Number(value.danh).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                                    <td>${value.count}</td>
                                    <td>${Number(value.trung).toLocaleString('en-US', {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                                </tr>
                            `)

                                })
                                $(`#trung_`).DataTable({
                                    processing: true,
                                    paging: true,
                                    lengthMenu: [10, 25, 50, 100, 150],
                                    pageLength: 25,
                                    order: [
                                        [5, 'desc']
                                    ],
                                    info: false
                                });
                        }

                    }



                    //table 3
                    let container_hai_so = $('#container-hai-so')
                    if (Object.keys(response.hai_so_bao_lo).length ||
                        Object.keys(response.dau_duoi).length ||
                        Object.keys(response.dau).length || Object.keys(response.duoi).length||Object.keys(response.bay_lo).length
                        ||Object.keys(response.tam_lo).length
                    ) {
                        container_hai_so.empty()
                    }
                    if (Object.keys(response.hai_so_bao_lo).length) {

                        container_hai_so.append(`
                            <h2>Bao lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-hai-so-bao-lo">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_hai_so_bao_lo = $('#table-hai-so-bao-lo tbody');
                        count = 0;
                        let hai_so_bao_lo = response.hai_so_bao_lo
                        Object.entries(hai_so_bao_lo).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_hai_so_bao_lo.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-hai-so-bao-lo').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }
                    if (Object.keys(response.dau_duoi).length) {
                        container_hai_so.append(`
                            <h2>Đầu đuôi</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-dau-duoi">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_dau_duoi = $('#table-dau-duoi tbody');
                        count = 0;
                        let dau_duoi = response.dau_duoi
                        Object.entries(dau_duoi).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_dau_duoi.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-dau-duoi').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],

                            info: false
                        });

                    }

                    if (Object.keys(response.dau).length) {
                        container_hai_so.append(`
                            <h2>Đầu</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-dau">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_dau = $('#table-dau tbody');
                        count = 0;
                        let dau = response.dau
                        Object.entries(dau).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_dau.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-dau').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    if (Object.keys(response.duoi).length) {
                        container_hai_so.append(`
                            <h2>Đuôi</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-duoi">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_duoi = $('#table-duoi tbody');
                        count = 0;
                        let duoi = response.duoi
                        Object.entries(duoi).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_duoi.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-duoi').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });
                    }
                    if (Object.keys(response.bay_lo).length) {
                        container_hai_so.append(`
                            <h2>Bảy lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-bay-lo">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_bay_lo = $('#table-bay-lo tbody');
                        count = 0;
                        let bay_lo = response.bay_lo
                        Object.entries(bay_lo).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_bay_lo.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-bay-lo').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });
                    }

                    if (Object.keys(response.tam_lo).length) {
                        container_hai_so.append(`
                            <h2>Bảy lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-tam-lo">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_tam_lo = $('#table-tam-lo tbody');
                        count = 0;
                        let tam_lo = response.tam_lo
                        Object.entries(tam_lo).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_tam_lo.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-tam-lo').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });
                    }

                    let container_ba_so = $('#container-ba-so')
                    if (Object.keys(response.ba_so_bao_lo).length || Object.keys(response.xiu_chu)
                        .length || Object.keys(response.xiu_chu_dau).length || Object.keys(response
                            .xiu_chu_duoi).length|| Object.keys(response
                            .bay_lo_dao).length|| Object.keys(response
                            .tam_lo_dao).length) {
                        container_ba_so.empty()
                    }
                    if (Object.keys(response.ba_so_bao_lo).length) {
                        container_ba_so.append(`
                            <h2>Bao lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-ba-so-bao-lo">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_ba_so_bao_lo = $('#table-ba-so-bao-lo tbody');
                        count = 0;
                        let ba_so_bao_lo = response.ba_so_bao_lo
                        Object.entries(ba_so_bao_lo).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_ba_so_bao_lo.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-ba-so-bao-lo').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    if (Object.keys(response.xiu_chu).length) {
                        container_ba_so.append(`
                            <h2>Xỉu chủ</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-xiu-chu">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_xiu_chu = $('#table-xiu-chu tbody');
                        count = 0;
                        let xiu_chu = response.xiu_chu
                        Object.entries(xiu_chu).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_xiu_chu.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-xiu-chu').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    if (Object.keys(response.xiu_chu_dau).length) {
                        container_ba_so.append(`
                            <h2>Xỉu chủ đầu</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-xiu-chu-dau">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_xiu_chu_dau = $('#table-xiu-chu-dau tbody');
                        count = 0;
                        let xiu_chu_dau = response.xiu_chu_dau
                        Object.entries(xiu_chu_dau).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_xiu_chu_dau.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-xiu-chu-dau').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    if (Object.keys(response.xiu_chu_duoi).length) {
                        container_ba_so.append(`
                            <h2>Xỉu chủ đầu</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-xiu-chu-duoi">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_xiu_chu_duoi = $('#table-xiu-chu-duoi tbody');
                        count = 0;
                        let xiu_chu_duoi = response.xiu_chu_duoi
                        Object.entries(xiu_chu_duoi).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_xiu_chu_duoi.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-xiu-chu-duoi').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    if (Object.keys(response.bay_lo_dao).length) {
                        container_ba_so.append(`
                            <h2>Bảy lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-bay-lo-dao">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_bay_lo_dao = $('#table-bay-lo-dao tbody');
                        count = 0;
                        let bay_lo_dao = response.bay_lo_dao
                        Object.entries(bay_lo_dao).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_bay_lo_dao.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-bay-lo-dao').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });
                    }

                    if (Object.keys(response.tam_lo_dao).length) {
                        container_ba_so.append(`
                            <h2>Bảy lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-tam-lo-dao">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_tam_lo_dao = $('#table-tam-lo-dao tbody');
                        count = 0;
                        let tam_lo_dao = response.tam_lo_dao
                        Object.entries(tam_lo_dao).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_tam_lo_dao.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-tam-lo-dao').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });
                    }

                    let container_bon_so = $('#container-bon-so')
                    if (Object.keys(response.bon_so_bao_lo).length || Object.keys(response.bon_so_duoi)
                        .length)
                        container_bon_so.empty()
                    if (Object.keys(response.bon_so_bao_lo).length) {
                        container_bon_so.append(`
                            <h2>Bao lô</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-bon-so-bao-lo">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_bon_so_bao_lo = $('#table-bon-so-bao-lo tbody');
                        count = 0;
                        let bon_so_bao_lo = response.bon_so_bao_lo
                        Object.entries(bon_so_bao_lo).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_bon_so_bao_lo.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-bon-so-bao-lo').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }
                    if (Object.keys(response.bon_so_duoi).length) {
                        container_bon_so.append(`
                            <h2>Đuôi</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-bon-so-duoi">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_bon_so_duoi = $('#table-bon-so-duoi tbody');
                        count = 0;
                        let bon_so_duoi = response.bon_so_duoi
                        Object.entries(bon_so_duoi).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_bon_so_duoi.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-bon-so-duoi').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                    let container_da_thang = $('#container-da-thang')

                    if (Object.keys(response.da_thang).length) {
                        container_da_thang.empty()
                        container_da_thang.append(`
                            <h2>Đá thẳng</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-da-thang">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_da_thang = $('#table-da-thang tbody');
                        count = 0;
                        let da_thang = response.da_thang
                        Object.entries(da_thang).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_da_thang.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-da-thang').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }


                    let container_da_xien = $('#container-da-xien')

                    if (Object.keys(response.da_xien).length) {
                        container_da_xien.empty()
                        container_da_xien.append(`
                            <h2>Đá xiên</h2>
                            <table class="table table-bordered table-striped dataTable no-footer text-sm collapsed " id="table-da-xien">
                            <thead>
                                <tr class="table-fz">
                                    <th>STT</th>
                                    <th>Đài</th>
                                    <th>Số đánh</th>
                                    <th>Số lần xuất hiện</th>
                                    <th>Tiền đánh</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        `)

                        const table_da_xien = $('#table-da-xien tbody');
                        count = 0;
                        let da_xien = response.da_xien
                        Object.entries(da_xien).forEach(([key, item]) => {
                            count += 1;
                            const parts = key.split(',');
                            table_da_xien.append(`
                                <tr>
                                        <td>${count}</td>
                                        <td>${parts[1]}</td>
                                        <td>${parts[0]}</td>
                                        <td>${item.count}</td>
                                        <td>${item.danh}</td>
                                    </tr>
                            `)
                        })
                        $('#table-da-xien').DataTable({
                            processing: true,
                            paging: true,
                            lengthMenu: [10, 25, 50, 100, 150],
                            pageLength: 25,
                            order: [
                                [0, 'asc']
                            ],
                            info: false
                        });

                    }

                },
                error: function(xhr, status, error) {
                    console.error('Có lỗi xảy ra:', error);
                }
            });

        });

        function change_area(area_id) {
            let firstElement = $('.active.area-select');

            if (firstElement.length) {
                firstElement.removeClass('active');
            }
            let tables = $('.table-area');

            tables.each(function() {
                if (!$(this).hasClass('hidden-table')) {
                    // Thêm class 'hidden-table'
                    $(this).addClass('hidden-table');
                }
            });

            tables = $('.table-trung');

            tables.each(function() {
                if (!$(this).hasClass('hidden-table')) {
                    // Thêm class 'hidden-table'
                    $(this).addClass('hidden-table');
                }
            });
            let choosen = $(`#${area_id}`);
            if (choosen) {
                choosen.addClass('active');
                $(`#table${area_id}`).removeClass('hidden-table')
                $(`#div_so_trung_${area_id}`).removeClass('hidden-table')
            }
        }

        function handleChange(element) {
            // element.classList.add('active');
            let firstElement = $('.active.type-selector').removeClass('active');

            tables = $('.container-danh');

            tables.each(function() {
                if (!$(this).hasClass('hidden-table')) {
                    $(this).addClass('hidden-table');
                }
            });

            let id = element.id;
            element.classList.add('active');
            $(`#container-${id}`).removeClass('hidden-table')
        }
    </script>
@endpush
