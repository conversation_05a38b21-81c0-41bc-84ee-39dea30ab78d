@extends('layout.master')
@section('content')
    @push("head")
        <link href="{{asset('js/dist/datepicker.css')}}" rel="stylesheet">

    @endpush


    <div class="container-fluid">
        <div class="col-md-12">
            {{ Breadcrumbs::render('report.reportDetail') }}

            <div class="portlet light portlet-fit portlet-datatable bordered md:mx-4">

                <div class="portlet-title">
                    
                </div>
                <div class="">
                    <div class="tab-content">
                        <div id="menu1" class="tab-pane  active ">
                            <div class="portlet-body">
                                <div class="row">
                                    <div class="container w-full margin-bottom-20" style="text-align: center">
                                        <div class="row items-center justify-center mb-2">
                                            <button class="m-2 btn-day" onclick="setYesterday(this)">
                                                <i class="fa fa-calendar-alt"></i> Hôm qua
                                            </button>
                                            <button class="m-2 btn-day active" onclick="setToday(this)">
                                                <i class="fa fa-calendar-alt"></i> Hôm nay
                                            </button>
                                            <button class="m-2 btn-day" onclick="setThisWeek(this)">
                                                <i class="fa fa-calendar-alt"></i> Tuần này
                                            </button>
                                            <button class="m-2 btn-day" onclick="setLastWeek(this)">
                                                <i class="fa fa-calendar-alt"></i> Tuần trước
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex justify-center items-center">
                                    <form class="col-md-6"
                                          action="{{route('statistic.store')}}"

                                          method="POST">
                                        @csrf
                                        <div class="text-main">Từ ngày</div>
                                        <div id="datepicker_from" class="input-group date date-picker margin-bottom-10"
                                             data-date-language="vi" data-date-format="dd/mm/yyyy"
                                             data-date-today-highlight="true">
                                            <input style="border-radius: 4px;" class="form-control" readonly="readonly"
                                                   type="text"
                                                   value="{{date('d/m/Y')}}"
                                                   data-toggle="datepicker"
                                                   name="from_date" id="accounting_report_from_date">
                                            <span class="help-block help-block-error"></span>
                                        </div>
                                        <div class="text-main">Đến ngày</div>
                                        <div id="datepicker_to" class="input-group date date-picker margin-bottom-10"
                                             data-date-language="vi" data-date-format="dd/mm/yyyy"
                                             data-date-today-highlight="true">
                                            <input style="border-radius: 4px;" class="form-control" readonly="readonly"
                                                   type="text"
                                                   data-toggle="datepicker"
                                                   name="to_date" id="accounting_report_to_date"
                                                   value="{{date('d/m/Y')}}">
                                            <span class="help-block help-block-error"></span>
                                        </div>

                                        <div class="col text-center mt-4 btn-ketoan">
                                            <button id="btn_scan" style="width: 150px"
                                                    type="button" onclick="scan()"
                                                    class="btn btn-color-main text-white btn-quet margin-bottom-10">
                                                <i class="fa fa-floppy-o"></i>Quét
                                            </button>

                                            <button style="width:150px" type="submit"
                                                    class="btn btn-baocao margin-bottom-10">
                                                <i class="fa fa-floppy-o"></i>Tạo BC
                                            </button>
                                        </div>

                                    </form>
                                </div>
                                <div class="table-responsive mt-4" style="overflow: auto">
                                </div>
                            </div>

                        </div>
                        
                    </div>
                </div>

            </div>
        </div>

        <div class="clearfix"></div>
        <!-- END DASHBOARD STATS 1-->
    </div>

   
@endsection



@push('js')

    <script src="{{asset('js/dist/datepicker.js')}}"></script>
    <script>

        $(document).ready(function () {
            var formId
            $(".custom-button1").click(function (e) {
                e.stopPropagation();
                e.preventDefault();
                $("#ModalDelete").modal('show');
                formId = $(this).attr('id');
            });
            $("#delete-submit").click(function () {
                $("#deleteForm" + formId).submit();
            });

        });

        function setActiveButton(button) {
            document.querySelectorAll('.btn-day').forEach(btn => btn.classList.remove('active'));

            button.classList.add('active');
        }

        function handleCheckBoxPass() {
            var currentDisplay = $("#field_password").css("display");
            if (currentDisplay === "none") {
                $("#field_password").css("display", "block"); // You can use any other valid display value
            } else {
                $("#field_password").css("display", "none");
            }
        }

        function toggleNewShareholder() {
            $("#password").val("")
            if ($("#new_shareholder").is(":checked")) {
                $("#select_shareholder").addClass("d-none")
                $("#create_shareholder").removeClass("d-none")
            } else {
                $("#select_shareholder").removeClass("d-none")
                $("#create_shareholder").addClass("d-none")
            }
        }

        function redirect(link) {
            window.location.href = link;
        }

        $("#form-data").submit(function (e) {
            e.preventDefault();
        });
        $('[data-toggle="datepicker"]').datepicker({
            language: 'vi-VN',
            format: 'dd/mm/yyyy'
        });



        let currentDate = new Date();

        var date_end = formatDate(currentDate, 0);

        var date_start = formatDate(currentDate, 0);

        let from_date = $("#accounting_report_from_date")
        let to_day = $("#accounting_report_to_date")
        from_date.change(function () {
            date_start = this.value
        })
        to_day.change(function () {
            date_end = this.value
        })


       

        function scan() {
            $.ajax({
                url: "{{route('api.statistic')}}",
                type: "post",
                data: {
                    _token: "{{ csrf_token() }}",
                    date_start: date_start,
                    date_end: date_end,
                },
                success: function (response) {
                    $(".table-responsive").empty()
                    $(".table-responsive").append(`<table class="table table-static table-bordered text-nowrap" id="table-transactions">
                                        <tbody>
                                        <tr>
                                            <th>STT</th>
                                            <th>Tên</th>
                                            <th>Nam</th>
                                            <th>Trung</th>
                                            <th>Bắc</th>
                                            <th>Thắng thua</th>
                                        </tr>
                                        </tbody>
                                        <tbody>
                                        </tbody>
                                    </table>`)
                    let total_bet = 0;
                    let prize = 0;
                    let lai_ve = 0;
                    let sum_nam = 0;
                    let sum_bac = 0;
                    let sum_trung = 0;
                    let id = 0;
                    if (response != null && response.length !== 0) {
                        response.sort((a, b) => a.username.localeCompare(b.username));
                        response.forEach(function (e) {
                            id++;
                            total_bet += e.tien_cuoc
                            prize += e.thang_thua
                            lai_ve += e.lai_ve != null ? e.lai_ve : 0
                            sum_nam += e.sum_nam != null ? e.sum_nam : 0
                            sum_bac += e.sum_bac != null ? e.sum_bac : 0
                            sum_trung += e.sum_trung != null ? e.sum_trung : 0
                            let strBtn = ""
                            let removeBtn = ""
                            let id2 = 0;
                            const date_start_ = $("#accounting_report_from_date")[0].defaultValue;
                            const date_end_ = $("#accounting_report_to_date")[0].defaultValue;
                            let urlNam = "{{url('/reportDetail')}}/" + e.customer_id + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=1";
                            let urlBac = "{{url('/reportDetail')}}/" + e.customer_id + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=2";
                            let urlTrung = "{{url('/reportDetail')}}/" + e.customer_id + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=3";
                            let urlAll = "{{url('/reportDetail')}}/" + e.customer_id + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=0";
                            

                            $("#table-transactions > tbody:nth-child(2) ").append(`  <tr id="${id}">
                                            <td>${id}</td>
                                            <td>${e.username}</td>
                                            
                                            <td><a href="${urlNam}">${e.sum_nam != null ? formatNumber(e.sum_nam) : 0}</a></td>
                                            <td><a href="${urlTrung}">${e.sum_trung != null ? formatNumber(e.sum_trung) : 0}</a></td>
                                            <td><a href="${urlBac}">${e.sum_bac != null ? formatNumber(e.sum_bac) : 0}</a></td>
                                            <td><a href="${urlAll}"> ${e.thang_thua != null ? formatNumber(e.thang_thua) : 0}</a></td>
                                            
                                        </tr>
                                    `)
                        })
                    }
                    

                    let urlTongNam = "{{url('/reportDetail')}}/" + '0' + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=1";
                    let urlTongBac = "{{url('/reportDetail')}}/" + '0' + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=2";
                    let urlTongTrung = "{{url('/reportDetail')}}/" + '0' + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=3";
                    let urlTongAll = "{{url('/reportDetail')}}/" + '0' + "?date_start=" + encodeURIComponent(date_start) + "&date_end=" + encodeURIComponent(date_end) + "&region=0";

                    $("#table-transactions > tbody:nth-child(2)").append(`<tr>
                                            <td></td>
                                            <td>Tổng</td>
                                            
                                            <td class="${sum_nam>=0?'':'text-danger'}"><a href="${urlTongNam}">${formatNumber(sum_nam)}</a></td>
                                            <td class="${sum_trung>=0?'':'text-danger'}"><a href="${urlTongTrung}">${formatNumber(sum_trung)}</a></td>
                                            <td class="${sum_bac>=0?'':'text-danger'}"><a href="${urlTongBac}">${formatNumber(sum_bac)}</a></td>
                                            <td class="${prize>=0?'':'text-danger'}"><a href="${urlTongAll}">${formatNumber(prize)}</a></td>
                                        </tr>`)
                }
            });

        }

        function formatNumber(number) {
            if (!number) return '0';

            if (number < 0 && number) {
                return ` <span style="color: red"> ${number.toLocaleString('en-US', {maximumFractionDigits: 0})} </span>`
            }

            return number.toLocaleString('en-US', {maximumFractionDigits: 0});
        }

        function setLastWeek(button) {
            setActiveButton(button);
            var currentDate = new Date();
            var currentDay = currentDate.getDay();

// Đặt ngày hiện tại về thứ 2 của tuần
            var startDayOfWeek = currentDay === 0 ? -6 : 1;
            var startDate = new Date(currentDate);
            startDate.setDate(currentDate.getDate() - currentDay + startDayOfWeek);

// Lấy ngày Chủ nhật của tuần
            var endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

// Lấy ngày thứ 2 của tuần trước
            var startOfLastWeek = new Date(startDate);
            startOfLastWeek.setDate(startDate.getDate() - 7);

// Lấy ngày Chủ nhật của tuần trước
            var endOfLastWeek = new Date(endDate);
            endOfLastWeek.setDate(endDate.getDate() - 7);

            date_start = startOfLastWeek.toLocaleDateString('en-GB');
            date_end = endOfLastWeek.toLocaleDateString('en-GB');
            from_date.val(date_start)
            to_day.val(date_end)
        }

        function setThisWeek(button) {
            setActiveButton(button);
            var currentDate = new Date();
            var currentDay = currentDate.getDay();

            // Đặt ngày hiện tại về thứ 2 của tuần
            var startDayOfWeek = currentDay === 0 ? -6 : 1;
            var startDate = new Date(currentDate);
            startDate.setDate(currentDate.getDate() - currentDay + startDayOfWeek);

            // Lấy ngày Chủ nhật của tuần
            var endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + 6);

            var formattedStartDate = startDate.toLocaleDateString('en-GB');
            var formattedEndDate = endDate.toLocaleDateString('en-GB');
            date_start = formattedStartDate;
            date_end = formattedEndDate;
            from_date.val(date_start)
            to_day.val(date_end)
        }

        function setYesterday(button) {
            setActiveButton(button);
            date_start = formatDate(currentDate, 1);
            date_end = formatDate(currentDate, 1);
            from_date.val(date_start)
            to_day.val(date_end)
        }

        function setToday(button) {
            setActiveButton(button);
            date_end = formatDate(currentDate, 0);
            date_start = formatDate(currentDate, 0);
            from_date.val(date_start)
            to_day.val(date_end)
        }

        function formatDate(date, subDay) {
            var newDate = new Date(date);
            newDate.setDate(date.getDate() - subDay);

            var day = newDate.getDate();
            var month = newDate.getMonth() + 1;
            var year = newDate.getFullYear();


            var formattedDate = (day < 10 ? '0' + day : day) + '/' + (month < 10 ? '0' + month : month) + '/' + year;

            return formattedDate;
        }

        function chuyenDoiNgay(ngay) {
            // Tách ngày, tháng và năm từ chuỗi
            var parts = ngay.split('/');
            var ngayMoi = new Date(parts[2], parts[1] - 1, parts[0]); // -1 vì tháng trong đối tượng Date bắt đầu từ 0

            // Định dạng lại ngày theo "YYYY-MM-DD"
            var ngayDaChuyenDoi = ngayMoi.getFullYear() + '-' + (ngayMoi.getMonth() + 1).toString().padStart(2, '0') + '-' + ngayMoi.getDate().toString().padStart(2, '0');

            return ngayDaChuyenDoi;
        }

        document.addEventListener("DOMContentLoaded", function () {
            var hash = window.location.hash;
            if (hash) {
                var tabLink = document.querySelector(hash);
                var tabA = document.querySelector(".tab-home[href='" + hash + "']")
                console.log(tabA)
                if (tabLink) {

                    document.querySelector("#menu1").classList.remove("active");
                    document.querySelector("#menu2").classList.remove("active");
                    document.querySelector("#menu3").classList.remove("active");
                    document.querySelectorAll(".tab-home ").forEach(function (tab) {
                        tab.classList.remove("active");
                    });

                }
                tabA.classList.add("active");
                tabLink.classList.add("active");
                tabLink.classList.add("show");
            }
        });

    </script>

@endpush
