<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <meta name="description" content=""/>
    <meta name="author" content=""/>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />
    <title>4h30</title>

    <!-- Custom fonts for this template-->
    <link
{{--        href="{{asset('vendor/fontawesome-free/css/all.min.css')}}"--}}
        rel="stylesheet"
        type="text/css"/>
    <link
        href="https://fonts.googleapis.com/css?family=Amatic+SC|Anton|Arsenal|Baloo|Bungee|Bungee+Hairline|Bungee+Inline|Comfortaa:300,400,700|Cormorant+Unicase:300,400,500,600,700|Dancing+Script:400,700|Jura:400,500,600|Lobster|Maven+Pro:400,500,700,900|Mitr:300,400,500,600,700|Noto+Serif:400,400i,700,700i|Open+Sans:300,300i,400,400i,600,600i,700,700i,800|Pacifico|Pangolin|Quicksand:300,400,500,700|Roboto+Slab:100,300,400,700|Roboto:300,300i,400,400i,500,500i,700,700i|Sedgwick+Ave+Display|Sigmar+One|VT323|Varela+Round"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link href="{{ asset('css/soft-ui-dashboard-tailwind.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/nucleo-icons.css') }}" rel="stylesheet" />
    <link href="{{ asset('css/nucleo-svg.css') }}" rel="stylesheet" />
    <link href="{{ asset('vendor/fontawesome-free/css/all.min.css') }}" rel="stylesheet" />


    <script src="https://unpkg.com/@popperjs/core@2"></script>
    {{-- <script src="https://kit.fontawesome.com/42d5adcbca.js" crossorigin="anonymous"></script> --}}
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700" rel="stylesheet" />
    <link href="{{asset('css/sb-admin-2.min.css')}}" rel="stylesheet" type="text/css"/>
    <link href="{{asset('css/main.css')}}" rel="stylesheet" type="text/css"/>
    <style>
        html {
            scroll-behavior: smooth;
            box-sizing: border-box;
        }

        #ontop {
            position: fixed;
            z-index: 99999;
            bottom: 20px;
            right: 20px;
            height: 70px;
            width: 70px;
            display: none;
            place-items: center;
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
            cursor: pointer;
        }

        #ontop-value {
            display: block;
            height: calc(100% - 15px);
            width: calc(100% - 15px);
            background-color: #fff;
            border-radius: 50%;
            display: grid;
            place-items: center;
            font-size: 35px;
            color: #001a2e;
        }
    </style>
    @stack('head')
    {{--    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.4/jquery.min.js" integrity="sha512-pumBsjNRGGqkPzKHndZMaAG+bir374sORyzM3uulLV14lN5LyykqNk8eEeUlUkB3U0M4FApyaHraT65ihJhDpQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>--}}
</head>

<body id="" class="m-0 font-sans antialiased font-normal text-base leading-default bg-gray-50 text-slate-500">
<!-- Page Wrapper -->
<div id="wrapper">
    <!-- Sidebar -->
    @include('layout.sidebar')
    <!-- End of Sidebar -->

    <!-- Content Wrapper -->
    <div id="content-wrapper" class="">
        <!-- Main Content -->
        <main class="ease-soft-in-out xl:ml-68.5 relative rounded-xl transition-all duration-200">
            <div id="content">
                <!-- Topbar -->
                @include('layout.topbar')
                <!-- End of Topbar -->

                <!-- Begin Page Content -->
                @yield('content')
            </div>
        </main>


        <!-- End of Main Content -->

        <!-- Footer -->
        {{--        @include('layout.footer')--}}
        <!-- End of Footer -->
    </div>
    <!-- End of Content Wrapper -->
</div>
<!-- End of Page Wrapper -->

<!-- Scroll to Top Button-->
{{-- <div id="ontop" >
    <span  id="ontop-value">T</span>
</div> --}}
{{--<div class="scroll-top">--}}
{{--    <a class="scroll-to-top rounded" href="#page-top">--}}
{{--        <i class="fas fa-angle-up"></i>--}}
{{--    </a>--}}
{{--</div>--}}

<!-- Logout Modal-->


<!-- Bootstrap core JavaScript-->

<script src="{{asset('vendor/jquery/jquery.min.js')}}"></script>
<script src="{{asset('vendor/bootstrap/js/bootstrap.bundle.min.js')}}"></script>

<!-- Core plugin JavaScript-->
{{-- <script src="{{asset('js/sb-admin-2.min.js')}}"></script>
<script src="{{asset('js/sidenav-burger.js')}}"></script> --}}
<script src="{{asset('js/navbar.js')}}"></script>
<!-- plugin for charts  -->
<script src="{{asset('js/plugins/chartjs.min.js')}}" async></script>
<!-- plugin for scrollbar  -->
{{--<script src="{{asset('js/plugins/perfect-scrollbar.min.js')}}" async></script>--}}
<!-- github button -->
{{--<script async defer src="https://buttons.github.io/buttons.js"></script>--}}
<!-- main script file  -->
{{--<script src="{{asset('js/soft-ui-dashboard-tailwind.js')}}" async></script>--}}

<!-- Custom scripts for all pages-->

<!-- Page level plugins -->
<script>

    function sendKeepAlive() {
        $.ajax({
            url: '/keep-alive',
            method: 'GET',
            success: function(response) {
                console.log('Keep-alive success:', response);
            },
            error: function(xhr, status, error) {
                console.error('Keep-alive error:', status, error);
            }
        });
    }
    sendKeepAlive()
    // Gửi yêu cầu keep-alive mỗi 5 phút (300000 ms)
    setInterval(sendKeepAlive, 300000);
    // $('.scroll-top').on("click",function(){
    //     $(window).scrollTop(0);
    // });
    const body = document.querySelector('body'),
        sidebar = body.querySelector('nav'),
        toggle = body.querySelector(".toggle-bar"),
        closeMobile = body.querySelector(".close-mobile"),
        searchBtn = body.querySelector(".search-box"),
        modeSwitch = body.querySelector(".toggle-switch"),
        modeText = body.querySelector(".mode-text");


    toggle.addEventListener("click", (e) => {
        e.stopPropagation();
        checkScreenSize();
        sidebar.classList.toggle("close");
    })
    closeMobile.addEventListener("click", (e) => {
        e.stopPropagation();
        sidebar.classList.toggle("close");
    })

    function checkScreenSize() {
    if (window.innerWidth < 1200 && sidebar.classList.contains("close")) {
        sidebar.classList.add("mobile-view");
    } else {
        sidebar.classList.remove("mobile-view");
    }
}

</script>

@stack('js')
</body>
</html>



