@extends('layout.master')
@section('content')

    <div class="container-fluid">
        <!-- Page Heading -->
        <!-- DataTales Example -->
        <div class="col-md-12">
                {{ Breadcrumbs::render('shareholder.edit',"Sửa cổ đông") }}
                <!-- Begin: life time stats -->
                <div class="portlet light portlet-fit portlet-datatable bordered">
                    <div class="portlet-title text-center">
                        <div class="caption">
                            <h3 class="font-bold mb-4 font-title uppercase">Sửa đông mới</h3>
                        </div>
                    </div>
                    <div class="portlet-body  w-100">
                        <form class="form-horizontal" id="form_accounting"
                              action="{{route('shareholder.update',$shareholder->id)}}"
                              accept-charset="UTF-8"
                              method="POST">
                            @csrf
                            @method('PUT')
                            <div class="form-body">
                                <div class="row d-flex align-items-center flex-wrap">
                                    {{-- <div class="col-md-2"></div> --}}
                                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                        <label class="control-label text-main">Tên hiển thị:
                                        </label>
                                        <input class="form-control" placeholder="Tên hiển thị" type="text"
                                                name="name" id="user_nick_name" value="{{$shareholder->name}}">
                                        @error('name')
                                        <span class="text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                    <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                        <label class="control-label text-main">Tên đăng nhập:
                                        </label>
                                        <input class="form-control" placeholder="Tên đăng nhập"
                                                readonly="readonly" type="text"
                                                value="{{$shareholder->username}}"
                                                name="username" id="user_user_name" aria-required="true"
                                                aria-invalid="false"
                                                aria-describedby="user_user_name-error"><span
                                            id="user_user_name-error"
                                            class="help-block help-block-error"></span>
                                        <span class="help-block help-block-error"></span>
                                    </div>
                                    <div id="field_password" class="col-lg-4 col-md-6 col-sm-12 " style="display:none">
                                        <div class="d-flex align-items-center flex-wrap mb-3">
                                            <label class="control-label text-main">Mật khẩu:
                                            </label>
                                            <input class="form-control" placeholder="Mật khẩu" type="password"
                                                    name="password" id="user_input_password">
                                            @error('password')
                                            <span class="text-danger">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    {{-- <div class="col-md-2"></div> --}}
                                </div>
                                <div class="form-group form-group-row row d-flex align-items-center flex-wrap">
                                    <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                        <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Đổi mật khẩu:
                                        </label>
                                        <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch checker" id="uniform-have_pass_checkbox">
                                            <input class="custom-control-input"
                                                type="checkbox" onclick="handleCheckBoxPass()"
                                                name="is_password"
                                                id="have_pass_checkbox">
                                            <label class="custom-control-label" for="have_pass_checkbox"></label>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <a class="font-title font-bold" href="#">Danh
                                        sách cổ đông</a>
                                </div>
                                <div class="table-responsive" style="overflow: auto;">
                                    <table class="table table-bordered text-nowrap" 
                                           id="table-stock-select">
                                        <tbody>
                                        <tr>
                                            <th style="width: 15%;">Trang</th>
                                            <th class="text-center" style="width: 10%;">Giao/Nhận</th>
                                            <th class="text-center" style="width: 35%; min-width: 150px">Phần trăm</th>
                                        </tr>
                                        @foreach($customers as $customer)
                                            <tr>
                                                <td class="form-value">{{$customer->username}}<br></td>
                                                <td class="text-center">
                                                        {{-- <input type="checkbox" id="check{{$customer->id}}"
                                                               name="is_sent[{{$customer->id}}]"
                                                               value="{{$customer->shareholderCustomers->first() ? $customer->shareholderCustomers->first()->is_send : 0 }}"
                                                            {{$customer->shareholderCustomers->first() &&  $customer->shareholderCustomers->first()->is_send==1 ? "checked" : "" }}

                                                        /> --}}
                                                    <label for="check{{$customer->id}}" class="btn-giaonhan p4">
                                                        <input type="checkbox" id="check{{$customer->id}}"
                                                        name="is_sent[{{$customer->id}}]"
                                                        value="{{$customer->shareholderCustomers->first() ? $customer->shareholderCustomers->first()->is_send : 0 }}"
                                                        {{$customer->shareholderCustomers->first() &&  $customer->shareholderCustomers->first()->is_send==1 ? "checked" : "" }}/>
                                                    </label>
                                                </td>
                                                <td class="text-center">
                                                    <input class="form-control"
                                                           type="number" step="any"
                                                           min="0" max="100"
                                                           name="percents[{{$customer->id}}]"
                                                           value="{{$customer->shareholderCustomers->first() ? $customer->shareholderCustomers->first()->percent:null }}"
                                                    >
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {{-- <button id="form-stock-submit-btn" class="btn btn-green btn-lg btn-block" type="submit"
                                    style="margin-bottom: 20px; color:white;">Tạo mới
                            </button> --}}
                            <div class="d-flex justify-content-center mt-3">
                                <button id="form-stock-submit-btn" class="btn btn-color-main text-white text-center mobile-w-full" type="submit">
                                    Tạo mới
                                </button>
                            </div>
                        </form>


                    </div>
                </div>

            </div>
    </div>
@endsection
@push('js')
    <script>
        function handleCheckBoxPass() {
            var currentDisplay = $("#field_password").css("display");
            if (currentDisplay === "none") {
                $("#field_password").css("display", "block"); // You can use any other valid display value
            } else {
                $("#field_password").css("display", "none");
            }
        }
    </script>
@endpush
