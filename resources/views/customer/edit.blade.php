@extends('layout.master')
@section('content')
{{--    @push('head')--}}
{{--        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">--}}
{{--        <style>--}}
{{--            .form-floating {--}}
{{--                margin-bottom: 1rem;--}}
{{--                /* <PERSON><PERSON><PERSON>ng cách dưới của các ô input */--}}
{{--            }--}}

{{--            @media (min-width: 260px) {--}}
{{--                .form-floating {--}}
{{--                    /* flex: 0 0 calc(50% - 1rem); <PERSON> ô input mỗi hàng trên màn hình >= 576px */--}}
{{--                    max-width: calc(50% - 1rem);--}}
{{--                    /* <PERSON>iớ<PERSON> hạn chiều rộng tối đa của ô input */--}}
{{--                }--}}
{{--            }--}}

{{--            @media (min-width: 360px) {--}}
{{--                .form-floating {--}}
{{--                    /* flex: 0 0 calc(33.33% - 1rem); Ba ô input mỗi hàng trên màn hình >= 768px */--}}
{{--                    max-width: calc(33.33% - 1rem);--}}
{{--                    /* Giới hạn chiều rộng tối đa của ô input */--}}
{{--                }--}}
{{--            }--}}

{{--            @media (min-width: 480px) {--}}
{{--                .form-floating {--}}
{{--                    /* flex: 0 0 calc(25% - 1rem); Bốn ô input mỗi hàng trên màn hình >= 992px */--}}
{{--                    max-width: calc(25% - 1rem);--}}
{{--                    /* Giới hạn chiều rộng tối đa của ô input */--}}
{{--                }--}}
{{--            }--}}
{{--        </style>--}}
{{--    @endpush--}}
    <div class="container-fluid">
        <div class="col-md-12">
            {{ Breadcrumbs::render('customer.edit', $customer) }}
            <div class="portlet light portlet-fit portlet-datatable bordered md:mx-4">
                <form class="p-2" action="{{ route('customer.update', $id) }}" method="POST">
                    @csrf
                    @method('PUT')
                    <div class=" p-2 pb-0">
                        <div class="row flex-center p-2">
                            <div class="form p-2 form-1 col-lg-6 col-md-6 col-sm-12">
                                <label class="mr-2 text-main">Tên người dùng</label>
                                <input class="form-control" placeholder="User Name" type="text" name="customer[name]"
                                    id="customer_name" value="{{ $customer->username }}">
                                @error('customer.name')
                                    <span style="color: red">{{ $message }}aaa</span>
                                @enderror
        
                            </div>
                            <div class="form p-2 form-1 col-lg-6 col-md-6 col-sm-12">
                                <label class="mr-2 text-main">Tài khoản bot telegram </label>
                                <input class="form-control" placeholder="Tài khoản bot telegram" type="text"
                                    name="customer[username_bot]" autocomplete="off" id="field_username_bot"
                                    value="{{ $customer->username_bot == null && $customer->password_bot == null ? $username_bot : $customer->username_bot }}">
                                @error('customer.username_bot')
                                    <span style="color: red">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>
                        <div class="p-2" style="border-bottom:1px solid #e3e6f0;">
                            <div class="row">
                                <div class="col-lg-4 col-md-6 col-12 p-0">
                                    <div class="form-group col-12 d-flex align-items-center mb-3">
                                        <label class="control-label col-11 p-0 text-main">Đổi mật khẩu bot telegram</label>
                                        <div class="col-1 custom-control custom-switch text-end">
                                            <input type="checkbox" class="custom-control-input" id="passwordBotSwitch" name="is_password" onclick="handleCheckBoxAcc()" {{ $customer->is_password ? 'checked' : '' }}>
                                            <label class="custom-control-label" for="passwordBotSwitch"></label>
                                        </div>
                                    </div>
                                    <div class="form-group col-12 form-2" style="display: none; padding: 0px 12px;">
                                        <label class="mr-2 text-main">Mật khẩu bot telegram</label>
                                        <input class="form-control" placeholder="Mật khẩu bot telegram" type="password"
                                            name="customer[password_bot]" autocomplete="off" id="field_password_bot"
                                            value="{{ old('customer.password_bot') }}">
                                        @error('customer.password_bot')
                                            <span style="color: red">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Tắt bot telegram cho khách hàng</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="turnOffBotSwitch" name="off_tele" {{ $customer->off_tele ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="turnOffBotSwitch"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Chặn gửi tin nhắn telegram trước giờ xổ</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="blockSendTelegramSwitch" onclick="showConfirmPin(event, this, 1)" name="is_send" {{ $customer->block_send_telegram ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="blockSendTelegramSwitch"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xóa tin nhắn telegram trước giờ xổ</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="deleteTelegramSwitch"
                                            onclick="showConfirmPin(event, this, 2)" name="is_remove"
                                            {{ $customer->is_remove_telegram ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="deleteTelegramSwitch"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xem chi tiết tin nhắn telegram</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="viewDetailsSwitch" name="is_detail"
                                            {{ $customer->is_detail_message ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="viewDetailsSwitch"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xem hạn mức</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="viewLimit" name="is_view_limit"
                                            {{ $customer->is_view_limit ? 'checked' : '' }} onClick="handleCheckBoxLimit()">
                                        <label class="custom-control-label" for="viewLimit"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Lọc hạn mức</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="limitSwitch" name="is_limit"
                                            {{ $customer->is_limit ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="limitSwitch"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Sửa tin</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_edit_ticket"
                                            name="is_edit_ticket" {{ $customer->is_edit_ticket ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_edit_ticket"></label>
                                    </div>
                                </div>
                                <div class="form-group col-lg-4 col-md-6 col-12 d-flex items-center">
                                    <label class="control-label col-lg-11 col-md-11 col-11 p-0 text-main">Xóa tin</label>
                                    <div class="col-lg-1 col-md-1 col-sm-1 text-end custom-control custom-switch">
                                        <input type="checkbox" class="custom-control-input" id="is_remove_ticket"
                                            name="is_remove_ticket" {{ $customer->is_remove_ticket ? 'checked' : '' }}>
                                        <label class="custom-control-label" for="is_remove_ticket"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form p-2 form-3" style="display: none">
                            <div class="d-flex align-items-center">
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="viewLimitOption" id="viewAllLimit"
                                        onclick="handleRadioChange()" checked>
                                    <label class="form-check-label" for="viewAllLimit">
                                        Tất cả
                                    </label>
        
                                </div>
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="viewLimitOption" id="viewDetailLimit"
                                        onclick="handleRadioChange()">
                                    <label class="form-check-label" for="viewDetailLimit">
                                        Chi tiết
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form p-2 form-4" style="display: none">
                            <label class="control-label font-bold col-md-6 p-0">2C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2CDau" style="width: 10ch;" value="123">
                                    <label for="viewAllLimit2CDau">Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2Duoi" style="width: 10ch;" value="456">
                                    <label for="viewAllLimit2Duoi">Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2CLo" style="width: 10ch;" value="789">
                                    <label for="viewAllLimit2CLo">Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2C7Lo" style="width: 10ch;" value="012">
                                    <label for="viewAllLimit2C7Lo">7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">3C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3CDau" style="width: 10ch;">
                                    <label for="viewAllLimit3CDau">Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3Duoi" style="width: 10ch;">
                                    <label for="viewAllLimit3Duoi">Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3CLo" style="width: 10ch;">
                                    <label for="viewAllLimit3CLo">Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewAllLimit3C7Lo" style="width: 10ch;">
                                    <label for="viewAllLimit3C7Lo">7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">4C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit4CDuoi" style="width: 10ch;">
                                    <label for="viewAllLimit4CDuoi">Đuôi</label>
                                </div>
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit4CLo" style="width: 10ch;">
                                    <label for="viewAllLimit4CLo">Lô</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá thẳng</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimitDT" style="width: 10ch;">
                                    <label for="viewAllLimitDT">Đá thẳng</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá xiên</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimitDX" style="width: 10ch;">
                                    <label for="viewAllLimitDX">Đá xiên</label>
                                </div>
    
                            </div>
                        </div>
                        <div class="form p-2 form-5" style="display: none">
                            <label class="control-label font-bold col-md-6 p-0">2C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CTNDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTNDau">TN Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2TNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2TNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CTN7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTN7Lo">TN 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CAGDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CAGDau">AG Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2AGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2AGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CAG7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2C7AGLo">AG 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CBTDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBTDau">BT Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2BTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2BTDuoi">BT Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBTLo">BT Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CBT7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBT7Lo">BT 7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">3C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNDau">TN Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3CTN7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTN7Lo">TN 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGDau">AG Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3CAG7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAG7Lo">AG 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTDau">BT Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTDuoi">BT Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTLo">BT Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3C7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3C7Lo">BT 7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">4C</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CTNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CTNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CAGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CAGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CBTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CBTDuoi">BT Đuôi</label>
                                </div>
    
                            </div>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CBTLo">BT Lô</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá thẳng</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTTN" style="width: 10ch;">
                                    <label for="viewDetailLimitDTTN">TN</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTAG" style="width: 10ch;">
                                    <label for="viewDetailLimitDTAG">AG</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTBT" style="width: 10ch;">
                                    <label for="viewDetailLimitDTBT">BT</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá xiên</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXTN" style="width: 10ch;">
                                    <label for="viewDetailLimitDXTN">TN</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXAG" style="width: 10ch;">
                                    <label for="viewDetailLimitAG">AG</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXBT" style="width: 10ch;">
                                    <label for="viewDetailLimitDXBT">BT</label>
                                </div>
    
                            </div>
                        </div>
                        <div class="form p-2 form-3" style="display: none">
                            <div class="d-flex align-items-center">
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="viewLimitOption" id="viewAllLimit"
                                        onclick="handleRadioChange()" checked>
                                    <label class="form-check-label" for="viewAllLimit">
                                        Tất cả
                                    </label>
        
                                </div>
                                <div class="form-check form-check-inline d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="viewLimitOption" id="viewDetailLimit"
                                        onclick="handleRadioChange()">
                                    <label class="form-check-label" for="viewDetailLimit">
                                        Chi tiết
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="form p-2 form-4" style="display: none">
                            <label class="control-label font-bold col-md-6 p-0">2C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2CDau" style="width: 10ch;" value="123">
                                    <label for="viewAllLimit2CDau">Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2Duoi" style="width: 10ch;" value="456">
                                    <label for="viewAllLimit2Duoi">Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2CLo" style="width: 10ch;" value="789">
                                    <label for="viewAllLimit2CLo">Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit2C7Lo" style="width: 10ch;" value="012">
                                    <label for="viewAllLimit2C7Lo">7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">3C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3CDau" style="width: 10ch;">
                                    <label for="viewAllLimit3CDau">Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3Duoi" style="width: 10ch;">
                                    <label for="viewAllLimit3Duoi">Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit3CLo" style="width: 10ch;">
                                    <label for="viewAllLimit3CLo">Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewAllLimit3C7Lo" style="width: 10ch;">
                                    <label for="viewAllLimit3C7Lo">7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">4C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit4CDuoi" style="width: 10ch;">
                                    <label for="viewAllLimit4CDuoi">Đuôi</label>
                                </div>
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimit4CLo" style="width: 10ch;">
                                    <label for="viewAllLimit4CLo">Lô</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá thẳng</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimitDT" style="width: 10ch;">
                                    <label for="viewAllLimitDT">Đá thẳng</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá xiên</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewAllLimitDX" style="width: 10ch;">
                                    <label for="viewAllLimitDX">Đá xiên</label>
                                </div>
    
                            </div>
                        </div>
                        <div class="form p-2 form-5" style="display: none">
                            <label class="control-label font-bold col-md-6 p-0">2C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CTNDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTNDau">TN Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2TNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2TNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CTN7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CTN7Lo">TN 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CAGDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CAGDau">AG Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2AGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2AGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CAG7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2C7AGLo">AG 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CBTDau" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBTDau">BT Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2BTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit2BTDuoi">BT Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit2CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBTLo">BT Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit2CBT7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit2CBT7Lo">BT 7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">3C</label>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNDau">TN Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3CTN7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CTN7Lo">TN 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGDau">AG Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3CAG7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CAG7Lo">AG 7Lô</label>
                                </div>
                            </div>
                            <div class="d-flex flex-wrap">
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTDau" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTDau">BT Đầu</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTDuoi">BT Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit3CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit3CBTLo">BT Lô</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input class="form-control" id="viewDetailLimit3C7Lo" style="width: 10ch;">
                                    <label for="viewDetailLimit3C7Lo">BT 7Lô</label>
                                </div>
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">4C</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CTNDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CTNDuoi">TN Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CAGDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CAGDuoi">AG Đuôi</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CBTDuoi" style="width: 10ch;">
                                    <label for="viewDetailLimit4CBTDuoi">BT Đuôi</label>
                                </div>
    
                            </div>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CTNLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CTNLo">TN Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CAGLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CAGLo">AG Lô</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimit4CBTLo" style="width: 10ch;">
                                    <label for="viewDetailLimit4CBTLo">BT Lô</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá thẳng</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTTN" style="width: 10ch;">
                                    <label for="viewDetailLimitDTTN">TN</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTAG" style="width: 10ch;">
                                    <label for="viewDetailLimitDTAG">AG</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDTBT" style="width: 10ch;">
                                    <label for="viewDetailLimitDTBT">BT</label>
                                </div>
    
                            </div>
                            <label class="control-label font-bold col-md-6 p-0">Đá xiên</label>
                            <div class="d-flex flex-wrap">
    
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXTN" style="width: 10ch;">
                                    <label for="viewDetailLimitDXTN">TN</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXAG" style="width: 10ch;">
                                    <label for="viewDetailLimitAG">AG</label>
                                </div>
                                <div class="form-floating mb-3 me-3">
                                    <input class="form-control" id="viewDetailLimitDXBT" style="width: 10ch;">
                                    <label for="viewDetailLimitDXBT">BT</label>
                                </div>
    
                            </div>
                        </div>
                        <div class=" p-2 pb-0">
                            <div class="caption mb-2">
                                <span class="text-main font-semibold" style="font-size: 17px;">PHẦN CHO NGƯỜI DÙNG</span>
                            </div>
                            <div class="row d-flex align-items-center justify-content-between flex-wrap">
                                <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                        <label class="text-main"
                                            style="margin-right: 10px;text-align: right;padding-top: 10px;">PT
                                            tổng</label>
                                        <input class="form-control" placeholder="PT tổng" type="number" step="any"
                                            value="{{ $customer->percent }}" name="customer[percent]"
                                            id="customer_percent" aria-required="true">
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                    <label class="text-main"
                                        style="margin-right: 10px;text-align: right;padding-top: 10px;">Lai
                                        về xác</label>
                                    <input class="form-control" placeholder="Lai về" type="number" step="any"
                                        value="{{ $customer->laive_xac }}" name="customer[laive_xac]"
                                        id="customer_laive_xac" aria-required="true">
    
                                </div>
                                <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3" style="gap:5px">
                                    <label class="text-main"
                                        style="margin-right: 10px;text-align: right;padding-top: 10px;">Lai
                                        về nguyên
                                        ngày</label>
                                    <input class="form-control" placeholder="Lai về" type="number"
                                        step="any" value="{{ $customer->laive }}" name="customer[laive]"
                                        id="customer_laive" aria-required="true">
                                </div>
                            </div>
                            <div class="d-flex align-items-center flex-wrap justify-content-end">
                                <div class="d-flex align-items-center">
                                    <label for="check" class="btn-giaonhan p4 mt-2">
                                        <input type="checkbox" id="check" />
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                
                    {{-- nhap --}}
                    {{-- <div class="form-inline m-3">
                        <label>User name: </label>
    
    
                        <input class="form-control w-25 ml-5" placeholder="User Name" type="text" name="customer[name]"
                               id="customer_name"
                               value="{{$customer->username}}"
                        >
    
                    </div>
                    <div class="row p-2 ">
    
                        <div class="caption" style="margin-Bottom: 20px;">
                            <i class="icon-info font-green"></i>
                            <span class="">PHẦN CHO USER</span>
                        </div>
                        <div class="col-4">
                            <div class="row">
                                <div class="col-6 d-flex justify-content-between">
                                    <div class="col-2"></div>
                                    <label class="col-10" style="text-align: right;padding-top: 10px;">PT tổng:</label>
                                </div>
                                <div class="col-6">
                                    <div class="form-inline mt-2 mb-2">
    
                                        <input class="form-control" placeholder="PT tổng" type="number"
                                               step="any"
                                               value="{{$customer->percent}}"
                                               name="customer[percent]" id="customer_percent" aria-required="true">
                                    </div>
                                </div>
    
                            </div>
                            <div class="row">
                                <div class="col-6 d-flex justify-content-between">
                                    <div class="col-2"></div>
                                    <label class="col-10" style="text-align: right;padding-top: 10px;">Lai về xác:</label>
                                </div>
                                <div class="col-6">
                                    <div class="form-inline mt-2 mb-2">
    
                                        <input class="form-control" placeholder="Lai về" type="number"
                                               step="any"
                                               value="{{$customer->laive_xac}}"
                                               name="customer[laive_xac]" id="customer_laive_xac" aria-required="true">
                                    </div>
                                </div>
    
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="row">
                                <div class="col-6 d-flex justify-content-between">
    
                                    <label class="col-12" style="text-align: right;padding-top: 10px;">Lai về nguyên
                                        ngày:</label>
                                </div>
                                <div class="col-6">
                                    <div class="form-inline mt-2 mb-2">
                                        <input class="form-control" placeholder="Lai về" type="number"
                                               step="any"
                                               value="{{$customer->laive}}"
                                               name="customer[laive]" id="customer_laive" aria-required="true">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-6 d-flex justify-content-between">
                                    <div class="col-6"></div>
                                    <label class="col-6" style="text-align: right;padding-top: 10px;">Giao/nhận</label>
                                </div>
                                <div class="col-6">
                                    <div class="form-inline mt-2 mb-2">
    
                                        {{--                                <input class="form-control" placeholder="Lai về" type="number" value="1" name="customer[laive]" id="customer_laive" aria-required="true"> --}}
                    {{-- </div>
                                </div>
                            </div>
                        </div>
                    </div>  --}}
                    <div class="p-2">
                        <div class="table-responsive p-2" style="overflow: auto;">
                            <table class="table table-bordered text-nowrap">
                                <thead>
                                    <tr style="text-align: center">
                                        <th>Miền</th>
                                        <th>Hai số</th>
                                        <th>Ba số</th>
                                        <th>Bốn số</th>
                                        <th>Đầu đuôi</th>
                                        <th>Xỉu chủ</th>
                                        <th>Đá thẳng</th>
                                        <th>Đá xiên</th>
                                        <th>Lai về</th>
                                        <th>Nữa vòng</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- South -->
                                    <tr>
                                        <td><br>Nam</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->hai_so_percent }}"
                                                    name="customer[south_percent_haiso]" id="customer_south_percent_haiso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->hai_so_ration }}"
                                                    name="customer[south_ratio_haiso]" id="customer_south_ratio_haiso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->ba_so_percent }}"
                                                    name="customer[south_percent_baso]" id="customer_south_percent_baso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->ba_so_ration }}"
                                                    name="customer[south_ratio_baso]" id="customer_south_ratio_baso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->bon_so_percent }}"
                                                    name="customer[south_percent_bonso]" id="customer_south_percent_bonso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->bon_so_ration }}"
                                                    name="customer[south_ratio_bonso]" id="customer_south_ratio_bonso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->dau_duoi_percent }}"
                                                    name="customer[south_percent_dauduoi]" id="customer_south_percent_dauduoi"
                                                    aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->dau_duoi_ration }}"
                                                    name="customer[south_ratio_dauduoi]" id="customer_south_ratio_dauduoi"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->xiu_chu_percent }}"
                                                    name="customer[south_percent_xiuchu]" id="customer_south_percent_xiuchu"
                                                    aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->xiu_chu_ration }}"
                                                    name="customer[south_ratio_xiuchu]" id="customer_south_ratio_xiuchu"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->da_thang_percent }}"
                                                    name="customer[south_percent_dathang]" id="customer_south_percent_dathang"
                                                    aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->da_thang_ration }}"
                                                    name="customer[south_ratio_dathang]" id="customer_south_ratio_dathang"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[0]->da_xien_percent }}"
                                                    name="customer[south_percent_daxien]" id="customer_south_percent_daxien"
                                                    aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[0]->da_xien_ration }}"
                                                    name="customer[south_ratio_daxien]" id="customer_south_ratio_daxien"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
            
                                            <div class="form-group">
                                                <input class="form-control custom-input" placeholder="Lai về" type="number"
                                                    step="any" value="{{ $rations[0]->lai_ve }}" name="customer[laive_south]"
                                                    id="customer_laive_south" aria-required="true">
                                                <span class="help-block help-block-error"></span>
                                            </div>
            
                                        </td>
                                        <td class="text-center">
                                            {{-- <div class="form-group"><input name="customer[is_half_win]" type="hidden"
                                                    value="0">
                                                <div class="checker" id="uniform-customer_is_half_win"><span><input
                                                            class="form-control custom-input" type="checkbox" value="1"
                                                            name="customer[is_half_win]"
                                                            {{ $rations[0]->nua_vong == 1 ? 'checked' : '' }}
                                                            id="customer_is_half_win"></span></div>
                                            </div> --}}
                                            <div class="form-group">
                                                <input name="customer[is_half_win]" type="hidden" value="0">
                                                <div class="checker checkbox-wrapper-19" id="uniform-customer_is_half_win">
                                                    <input
                                                    class="custom-input" type="checkbox" value="1"
                                                    {{ $rations[0]->nua_vong == 1 ? 'checked' : '' }}
                                                    name="customer[is_half_win]"
                                                    id="customer_is_half_win">
                                                    <label for="customer_is_half_win" class="check-box">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- North -->
                                    <tr>
                                        <td><br>Bắc</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->hai_so_percent }}"
                                                    name="customer[north_percent_haiso]" id="customer_north_percent_haiso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->hai_so_ration }}"
                                                    name="customer[north_ratio_haiso]" id="customer_north_ratio_haiso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->ba_so_percent }}"
                                                    name="customer[north_percent_baso]" id="customer_north_percent_baso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->ba_so_ration }}"
                                                    name="customer[north_ratio_baso]" id="customer_north_ratio_baso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->bon_so_percent }}"
                                                    name="customer[north_percent_bonso]" id="customer_north_percent_bonso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->bon_so_ration }}"
                                                    name="customer[north_ratio_bonso]" id="customer_north_ratio_bonso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->dau_duoi_percent }}"
                                                    name="customer[north_percent_dauduoi]" id="customer_north_percent_dauduoi"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->dau_duoi_ration }}"
                                                    name="customer[north_ratio_dauduoi]" id="customer_north_ratio_dauduoi"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->xiu_chu_percent }}"
                                                    name="customer[north_percent_xiuchu]" id="customer_north_percent_xiuchu"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->xiu_chu_ration }}"
                                                    name="customer[north_ratio_xiuchu]" id="customer_north_ratio_xiuchu"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[1]->da_thang_percent }}"
                                                    name="customer[north_percent_dathang]" id="customer_north_percent_dathang"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[1]->da_thang_ration }}"
                                                    name="customer[north_ratio_dathang]" id="customer_north_ratio_dathang"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                        </td>
                                        <td>
            
                                            <div class="form-group">
                                                <input class="form-control custom-input" placeholder="Lai về" type="number"
                                                    value="{{ $rations[1]->lai_ve }}" step="any" name="customer[laive_north]"
                                                    id="customer_laive_north" aria-required="true">
                                                <span class="help-block help-block-error"></span>
                                            </div>
            
                                        </td>
                                        <td class="text-center">
                                            {{-- <div class="form-group"><input name="customer[is_half_win_north]" type="hidden"
                                                    value="0">
                                                <div class="checker" id="uniform-customer_is_half_win_north"><span><input
                                                            class="form-control custom-input" type="checkbox" value="1"
                                                            {{ $rations[1]->nua_vong == 1 ? 'checked' : '' }}
                                                            name="customer[is_half_win_north]"
                                                            id="customer_is_half_win_mnorth"></span>
                                                </div>
                                            </div> --}}
                                            <div class="form-group">
                                                <input name="customer[is_half_win_north]" type="hidden" value="0">
                                                <div class="checker checkbox-wrapper-19" id="uniform-customer_is_half_win_north">
                                                    <input
                                                    class="custom-input" type="checkbox" value="1"
                                                    {{ $rations[1]->nua_vong == 1 ? 'checked' : '' }}
                                                    name="customer[is_half_win_north]"
                                                    id="customer_is_half_win_mnorth">
                                                    <label for="customer_is_half_win_mnorth" class="check-box">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    <!-- Middle -->
                                    <tr>
                                        <td><br>Trung</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->hai_so_percent }}"
                                                    name="customer[middle_percent_haiso]" id="customer_middle_percent_haiso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->hai_so_ration }}"
                                                    name="customer[middle_ratio_haiso]" id="customer_middle_ratio_haiso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->ba_so_percent }}"
                                                    name="customer[middle_percent_baso]" id="customer_middle_percent_baso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->ba_so_ration }}"
                                                    name="customer[middle_ratio_baso]" id="customer_middle_ratio_baso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->bon_so_percent }}"
                                                    name="customer[middle_percent_bonso]" id="customer_middle_percent_bonso"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->bon_so_ration }}"
                                                    name="customer[middle_ratio_bonso]" id="customer_middle_ratio_bonso"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->dau_duoi_percent }}"
                                                    name="customer[middle_percent_dauduoi]" id="customer_middle_percent_dauduoi"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->dau_duoi_ration }}"
                                                    name="customer[middle_ratio_dauduoi]" id="customer_middle_ratio_dauduoi"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->xiu_chu_percent }}"
                                                    name="customer[middle_percent_xiuchu]" id="customer_middle_percent_xiuchu"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->xiu_chu_ration }}"
                                                    name="customer[middle_ratio_xiuchu]" id="customer_middle_ratio_xiuchu"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->da_thang_percent }}"
                                                    name="customer[middle_percent_dathang]" id="customer_middle_percent_dathang"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->da_thang_ration }}"
                                                    name="customer[middle_ratio_dathang]" id="customer_middle_ratio_dathang"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[2]->da_xien_percent }}"
                                                    name="customer[middle_percent_daxien]" id="customer_middle_percent_daxien"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[2]->da_xien_ration }}"
                                                    name="customer[middle_ratio_daxien]" id="customer_middle_ratio_daxien"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
            
                                            <div class="form-group">
                                                <input class="form-control custom-input" placeholder="Lai về" type="number"
                                                    step="any" value="{{ $rations[2]->lai_ve }}"
                                                    name="customer[laive_middle]" id="customer_laive_middle" aria-required="true">
                                                <span class="help-block help-block-error"></span>
                                            </div>
            
                                        </td>
                                        <td class="text-center">
                                            <div class="form-group">
                                                <input name="customer[is_half_win_middle]" type="hidden" value="0">
                                                {{-- <div class="checker" id="uniform-customer_is_half_win_middle"><span><input
                                                            class="custom-input" type="checkbox" value="1"
                                                            {{ $rations[2]->nua_vong == 1 ? 'checked' : '' }}
                                                            name="customer[is_half_win_middle]"
                                                            id="customer_is_half_win_middle"></span>
                                                </div> --}}
                                                <div class="checker checkbox-wrapper-19" id="uniform-customer_is_half_win_middle">
                                                    <input
                                                    class="custom-input" type="checkbox" value="1"
                                                    {{ $rations[2]->nua_vong == 1 ? 'checked' : '' }}
                                                    name="customer[is_half_win_middle]"
                                                    id="customer_is_half_win_middle">
                                                    <label for="customer_is_half_win_middle" class="check-box">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
    
                    <div class="p-3">
                        <div class="caption" style="margin-Bottom: 20px;">
                            <span class="text-main font-semibold" style="font-size: 17px;"> PHẦN CHO KHÁCH</span>
                        </div>
                        <div class="row d-flex align-items-center flex-wrap">
                            <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                <label class="mr-2 text-main" style="text-align: right; padding-top: 10px;">PT tổng:</label>
                                <input class="form-control custom-input" placeholder="PT tổng" type="number" step="any"
                                    value="{{ $customer->percent_2nd }}" name="customer[percent_2nd]"
                                    id="customer_percent_2nd" aria-required="true">
                            </div>
                    
                            <div class="col-lg-4 col-md-6 col-sm-12 d-flex align-items-center flex-wrap mb-3">
                                <label class="text-main" style="margin-right: 10px; text-align: right; padding-top: 10px;">Lai về:</label>
                                <input class="form-control" placeholder="Lai về" type="number" step="any"
                                    value="{{ $customer->laive_2nd }}" name="customer[laive_2nd]"
                                    id="customer_laive_2nd" aria-required="true">
                            </div>
                        </div>
                        <div class="table-responsive" style="overflow: auto;">
                            <table class="table table-bordered text-nowrap">
                                <thead>
                                    <tr style="text-align: center">
                                        <th>Miền</th>
                                        <th>Hai số</th>
                                        <th>Ba số</th>
                                        <th>Bốn số</th>
                                        <th>Đầu đuôi</th>
                                        <th>Xỉu chủ</th>
                                        <th>Đá thẳng</th>
                                        <th>Đá xiên</th>
        
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><br>Nam</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->hai_so_percent }}"
                                                    name="customer[south_percent_haiso_2nd]"
                                                    id="customer_south_percent_haiso_2nd" aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->hai_so_ration }}"
                                                    name="customer[south_ratio_haiso_2nd]" id="customer_south_ratio_haiso_2nd"
                                                    aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->ba_so_percent }}"
                                                    name="customer[south_percent_baso_2nd]" id="customer_south_percent_baso_2nd"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->ba_so_ration }}"
                                                    name="customer[south_ratio_baso_2nd]" id="customer_south_ratio_baso_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->bon_so_percent }}"
                                                    name="customer[south_percent_bonso_2nd]"
                                                    id="customer_south_percent_bonso_2nd" aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->bon_so_ration }}"
                                                    name="customer[south_ratio_bonso_2nd]" id="customer_south_ratio_bonso_2nd"
                                                    aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->dau_duoi_percent }}"
                                                    name="customer[south_percent_dauduoi_2nd]"
                                                    id="customer_south_percent_dauduoi_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->dau_duoi_ration }}"
                                                    name="customer[south_ratio_dauduoi_2nd]"
                                                    id="customer_south_ratio_dauduoi_2nd" aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->xiu_chu_percent }}"
                                                    name="customer[south_percent_xiuchu_2nd]"
                                                    id="customer_south_percent_xiuchu_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->xiu_chu_ration }}"
                                                    name="customer[south_ratio_xiuchu_2nd]" id="customer_south_ratio_xiuchu_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->da_thang_percent }}"
                                                    name="customer[south_percent_dathang_2nd]"
                                                    id="customer_south_percent_dathang_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->da_thang_ration }}"
                                                    name="customer[south_ratio_dathang_2nd]"
                                                    id="customer_south_ratio_dathang_2nd" aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[3]->da_xien_percent }}"
                                                    name="customer[south_percent_daxien_2nd]"
                                                    id="customer_south_percent_daxien_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[3]->da_xien_ration }}"
                                                    name="customer[south_ratio_daxien_2nd]" id="customer_south_ratio_daxien_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><br>Bắc</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->hai_so_percent }}"
                                                    name="customer[north_percent_haiso_2nd]"
                                                    id="customer_north_percent_haiso_2nd" aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->hai_so_ration }}"
                                                    name="customer[north_ratio_haiso_2nd]" id="customer_north_ratio_haiso_2nd"
                                                    aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->ba_so_percent }}"
                                                    name="customer[north_percent_baso_2nd]" id="customer_north_percent_baso_2nd"
                                                    aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->ba_so_ration }}"
                                                    name="customer[north_ratio_baso_2nd]" id="customer_north_ratio_baso_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->bon_so_percent }}"
                                                    name="customer[north_percent_bonso_2nd]"
                                                    id="customer_north_percent_bonso_2nd" aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->bon_so_ration }}"
                                                    name="customer[north_ratio_bonso_2nd]" id="customer_north_ratio_bonso_2nd"
                                                    aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->dau_duoi_percent }}"
                                                    name="customer[north_percent_dauduoi_2nd]"
                                                    id="customer_north_percent_dauduoi_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->dau_duoi_ration }}"
                                                    name="customer[north_ratio_dauduoi_2nd]"
                                                    id="customer_north_ratio_dauduoi_2nd" aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->xiu_chu_percent }}"
                                                    name="customer[north_percent_xiuchu_2nd]"
                                                    id="customer_north_percent_xiuchu_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->xiu_chu_ration }}"
                                                    name="customer[north_ratio_xiuchu_2nd]" id="customer_north_ratio_xiuchu_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[4]->da_thang_percent }}"
                                                    name="customer[north_percent_dathang_2nd]"
                                                    id="customer_north_percent_dathang_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[4]->da_thang_ration }}"
                                                    name="customer[north_ratio_dathang_2nd]"
                                                    id="customer_north_ratio_dathang_2nd" aria-required="true"></div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><br>Trung</td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[5]->hai_so_percent }}"
                                                    name="customer[middle_percent_haiso_2nd]"
                                                    id="customer_middle_percent_haiso_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->hai_so_ration }}"
                                                    name="customer[middle_ratio_haiso_2nd]" id="customer_middle_ratio_haiso_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[5]->ba_so_percent }}"
                                                    name="customer[middle_percent_baso_2nd]"
                                                    id="customer_middle_percent_baso_2nd" aria-required="true"></div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->ba_so_ration }}"
                                                    name="customer[middle_ratio_baso_2nd]" id="customer_middle_ratio_baso_2nd"
                                                    aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[5]->bon_so_percent }}"
                                                    name="customer[middle_percent_bonso_2nd]"
                                                    id="customer_middle_percent_bonso_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->bon_so_ration }}"
                                                    name="customer[middle_ratio_bonso_2nd]" id="customer_middle_ratio_bonso_2nd"
                                                    aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any"
                                                    value="{{ $rations[5]->dau_duoi_percent }}"
                                                    name="customer[middle_percent_dauduoi_2nd]"
                                                    id="customer_middle_percent_dauduoi_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->dau_duoi_ration }}"
                                                    name="customer[middle_ratio_dauduoi_2nd]"
                                                    id="customer_middle_ratio_dauduoi_2nd" aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[5]->xiu_chu_percent }}"
                                                    name="customer[middle_percent_xiuchu_2nd]"
                                                    id="customer_middle_percent_xiuchu_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->xiu_chu_ration }}"
                                                    name="customer[middle_ratio_xiuchu_2nd]"
                                                    id="customer_middle_ratio_xiuchu_2nd" aria-required="true"></div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any"
                                                    value="{{ $rations[5]->da_thang_percent }}"
                                                    name="customer[middle_percent_dathang_2nd]"
                                                    id="customer_middle_percent_dathang_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->da_thang_ration }}"
                                                    name="customer[middle_ratio_dathang_2nd]"
                                                    id="customer_middle_ratio_dathang_2nd" aria-required="true">
                                            </div>
                                        </td>
                                        <td>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" step="any" value="{{ $rations[5]->da_xien_percent }}"
                                                    name="customer[middle_percent_daxien_2nd]"
                                                    id="customer_middle_percent_daxien_2nd" aria-required="true">
                                            </div>
                                            <div class="form-group"><input class="form-control custom-input" error="false"
                                                    type="number" value="{{ $rations[5]->da_xien_ration }}"
                                                    name="customer[middle_ratio_daxien_2nd]"
                                                    id="customer_middle_ratio_daxien_2nd" aria-required="true"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="d-flex justify-content-center">
                        <button id="form-customer-submit-btn" class="btn btn-color-main" type="submit"
                            style="width: 150px;margin: 20px 0px 30px 0px;color:white;">Lưu thông tin
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade text-left r-0" id="ModalUpdate" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-custom my-0" role="document">
            <form method="post" style="flex: 1; margin: 0px 10px;">
                <div class="modal-content">
                    <div class="d-flex items-center justify-between p-3 border-bottom-main">
                        <h5 class="font-title font-bold">Xác nhận mã pin</h5>
                        <button type="button" class="bg-transparent rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center close-modal" data-bs-dismiss="modal" aria-label="Close">
                            <i class="fas fa-times" style="font-size:20px" aria-hidden="true"></i>
                            <span class="sr-only">Close modal</span>
                        </button>
                    </div>
                    <div class="form-group p-3">
                        <label class="text-main" for="pin">Mã pin <span class="text-red-600">*</span></label>
                        <input type="text" name="pin" id="pin" class="form-control" placeholder="Nhập mã pin">
                        <span style="color: red" class="message-err"></span>
                    </div>
                    <div class="d-flex justify-center p-3 pt-0" style="gap:10px;">
                        <button id="update-submit" class="btn btn-color-main text-white font-bold" style="width: 100px;" type="button">Xác nhận</button>
                        <button id="form-transaction-submit-btn" class="btn btn-dark text-white font-bold" style="width: 100px;" type="button" data-bs-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </form>
        </div>
    </div>


@endsection
@push('js')
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let changeBlock
        let changeDelete
        let flag = 1;


        function showConfirmPin(event, value, $number) {
            event.preventDefault()
            if ($number == 1) {
                flag = 1
                changeBlock = value.checked
            } else if ($number == 2) {
                flag = 2
                changeDelete = value.checked
            }
            $(".message-err").text('')
            $("#ModalUpdate").modal('show')

        }

        $("#update-submit").click(function() {
            let pin = $("#pin").val()
            $.ajax({
                url: "{{ route('confirmPin') }}",
                type: "post",
                data: {
                    _token: "{{ csrf_token() }}",
                    pin: pin,
                    customer_id: '{{ $id }}',
                },
                success: function(response) {
                    console.log(response)
                    if (response.status == false) {
                        $("#pin").val('')
                        $(".message-err").text('Mã pin không đúng vui lòng thử lại.')
                    } else {
                        if (flag == 1) {
                            $("#blockSendTelegramSwitch").prop('checked', changeBlock)
                        } else if (flag == 2) {
                            $("#deleteTelegramSwitch").prop('checked', changeDelete)
                        }
                        $("#pin").val('')
                        $("#ModalUpdate").modal('hide')
                    }

                }
            });

        })

        function handleCheckBoxAcc() {

            var currentDisplay_P = $(".form-2").css("display");

            if (currentDisplay_P === "none") {
                $(".form-2").css("display", "block");
                $("#field_password_bot").prop("disable", true);
            } else {
                $(".form-2").css("display", "none");
                $("#field_password_bot").prop("disable", false);
            }
        }
        function handleCheckBoxLimit() {
            var form3 = document.querySelector('.form-3');
            var viewLimitCheckbox = document.getElementById('viewLimit');
            var form4 = document.querySelector('.form-4');
            var form5 = document.querySelector('.form-5');

            if (viewLimitCheckbox.checked) {
                form3.style.display = 'block';
                document.getElementById('viewAllLimit').checked = true;
                form4.style.display = 'block';
                form5.style.display = 'none';
            } else {
                form3.style.display = 'none';
                form4.style.display = 'none';
                form5.style.display = 'none';
            }
        }

        function handleRadioChange() {
            var viewAllLimit = document.getElementById('viewAllLimit');
            var viewDetailLimit = document.getElementById('viewDetailLimit');
            var form4 = document.querySelector('.form-4');
            var form5 = document.querySelector('.form-5');

            if (viewAllLimit.checked) {
                form4.style.display = 'block';
                form5.style.display = 'none';
            } else if (viewDetailLimit.checked) {
                form4.style.display = 'none';
                form5.style.display = 'block';
            }
        }
    </script>
@endpush
