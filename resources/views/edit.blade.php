@extends('layout.master')
@section('content')
    @push("head")
    @endpush
    <div class="page-content">
        <!-- BEGIN PAGE HEADER-->
        <!-- BEGIN PAGE BAR -->

        <!-- END PAGE BAR -->
        <!-- BEGIN PAGE TITLE-->
        <h3 class="page-title">
        </h3>
        <!-- END PAGE TITLE-->
        <!-- END PAGE HEADER-->
        <!-- BEGIN DASHBOARD STATS 1-->
        <div class="row" style="margin-top: 100px">
            <div class="col-lg-12">
                <div class="portlet-body" style="margin-left: 20px;">

                    <form id="form_member" action="{{route('updateMachine',$machine->id)}}" method="post"
                          style="margin-left: 20px;margin-right: 30px">
                        @csrf
                        @method('PUT')

                        <div class="caption" style="margin-Bottom: 20px;">
                            <i class="icon-info font-green"></i>
                            <span class="caption-subject font-bold sbold uppercase">Sửa thông tin User</span>
                        </div>


                        <div class="row machine-item">
                            <div class="col-lg-5 machine-box">
                                <div class="row flex-dec flex-dec ">
                                    <label class="control-label ">User name
                                    </label>
                                    <div class="">
                                        <input class="form-control" placeholder="User Name" type="text"
                                               name="username" id="user_user_name" value="{{$machine->username}}"
                                               readonly>
                                        @error('username')
                                        <span style="color: red">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="row flex-dec ">
                                    <label class="control-label ">Email
                                    </label>
                                    <div class="">
                                        <input class="form-control" placeholder="Email" type="text" name="email"
                                               id="email" value="{{$machine->email}}">

                                    </div>
                                </div>

                                <div class="row flex-dec ">
                                    <label class="control-label ">Mật khẩu
                                    </label>
                                    <div class="">
                                        <input class="form-control" placeholder="Mật khẩu" type="password"
                                               name="password" id="user_input_password">
                                        @error('password')
                                        <span style="color: red">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                @if($machine->pin)
                                    <div class="row flex-dec ">
                                        <label class="control-label ">Mã pin cũ
                                        </label>
                                        <div class="">
                                            <input class="form-control" placeholder="Pin cũ" type="password"
                                                   name="old_pin" id="old_pin">
                                            @error('old_pin')
                                            <span style="color: red">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                @endif
                                <div class="row flex-dec ">
                                    <label class="control-label ">Mã pin mới
                                    </label>
                                    <div class="">
                                        <input class="form-control" placeholder="Pin mới" type="password"
                                               name="pin" id="pin">
                                        @error('pin')
                                        <span style="color: red">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="row" style="justify-content: flex-end">
                                    <div class="col-lg-offset-3 col-lg-3" style="text-align: center">
                                        <button id="form-member-submit-btn" class="btn btn-green text-white"
                                                type="submit"
                                                style="max-width: 150px;margin: 20px 0px;">Cập nhật
                                        </button>
                                    </div>
                                </div>
                            </div>

                        </div>


                    </form>        <!-- END FORM-->
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <!-- END DASHBOARD STATS 1-->
    </div>

@endsection

@push('js')
@endpush
